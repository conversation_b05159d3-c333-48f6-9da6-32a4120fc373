
import { createGiftInstance } from '../helpers/testhelper';
import {

    GiftMapItemFace,

} from "./new_data_interface";

import PolicyUtil from '../src/package/policy_util';
import PPM from 'plugin_public_methods';
jest.mock('plugin_public_methods');


describe('Gift', () => {
    let instance;

    beforeEach(() => {
        // 清理所有模拟调用信息
        jest.clearAllMocks();
        instance = createGiftInstance();
        instance.detailApiName = "detailApiName";
        // PPM.multiplicational = jest.fn((value, multiplier) => value * multiplier);
        // PPM.division = jest.fn((amount, price) => amount / price);
        // PPM.accSub = jest.fn((a, b) => a - b);
        // PPM.uniqueCode = jest.fn().mockImplementation(() => 'unique_code');
        // PPM.ajax = jest.fn();
        // PPM.parsePriceBookDataRangeDetails = jest.fn((oDetails, priceBookIdField) => {
        //     return { parsedDetails: 'mockedDetails' };
        // });
        // PPM.collectCalFields=jest.fn().mockImplementation((arg1,arg2,arg3)=>{ 
        //     return {
        //         detailApiName: ['field1', 'field2', 'field3']
        //     }
        // });
        // PPM.getCalFields=jest.fn().mockImplementation((arg1,arg2)=>{
        //     return {
        //         detailApiName: ['field1', 'field2', 'field3']
        //     }
        // })
       
       
    });

    describe('collectGiftMapList', () => {
       
        it('should collect gift data from masterData and detailDataMap', async () => {
            const mockParam = {
                masterData: {
                    prod_pkg_key: 'masterKey',
                    gift_map: {
                        'rule1': {
                            hold_chose: '0',
                            gift_list: [
                                { product_id: 'prod1', actual_unit: 'unit1', is_this_product: true }
                            ]
                        }
                    },
                    price_book_id: 'masterPriceBookId'
                },
                detailDataMap: {
                    detail1: {
                        prod_pkg_key: 'detailKey1',
                        gift_map: {
                            'rule2': {
                                hold_chose: '0',
                                gift_list: [
                                    { product_id: 'prod2', actual_unit: 'unit2', is_this_product: false, price_book_id: 'detailPriceBookId1' }
                                ]
                            }
                        },
                        price_book_id: 'detailPriceBookId1'
                    },
                    detail2: {
                        prod_pkg_key: 'detailKey2',
                        gift_map: {
                            'rule3': {
                                hold_chose: '0',
                                gift_list: [
                                    { product_id: 'prod3', unit: 'unit3', is_this_product: false }
                                ]
                            }
                        },
                        price_book_id: 'detailPriceBookId2'
                    }
                }
            };
    
            const giftData = await instance.collectGiftMapList(mockParam);
            
            expect(giftData[0]).toEqual({
                product_id: 'prod1',
                actual_unit: 'unit1',
                rowId: 'unique_code',
                data_index: 'unique_code',
                prod_pkg_key: 'unique_code',
                price_policy_rule_ids: ['rule1'],
                price_book_id: 'masterPriceBookId',
                "gift_type": "system",
                "icon_fake_val": true,
                "is_this_product": true,
                "parent_gift_key": "masterKey",
            });
        });
    });
    
    describe('getCommonProps', () => {
        it('should return commonProps for master data', () => {
            const data = {
                record_type: 'mockRecordTypeData',
            };
    
            const parentKey1 = 'master';
            const expectedCommonProps = {
                record_type: "default__C",
                parent_gift_key: 'master',
                price_policy_id: undefined, // data[price_policy_id] is undefined
                icon_fake_val: true,
                gift_type: 'system'
            };
    
            const result = instance.getCommonProps(data, 'master');
            expect(result).toEqual(expectedCommonProps);
        });
    
        it('should return commonProps for detail data', () => {
            const data = {
                record_type: 'mockRecordTypeData',
                price_policy_id: 'mockPricePolicyIdData',
                price_book_id: 'mockPriceBookIdData'
            };
    
            const parentKey = 'detail';
            const expectedCommonProps = {
                record_type: 'mockRecordTypeData',
                parent_gift_key: 'detail',
                price_policy_id: 'mockPricePolicyIdData',
                icon_fake_val: true,
                gift_type: 'system'
            };
    
            const result = instance.getCommonProps(data, parentKey);
            expect(result).toEqual(expectedCommonProps);
        });
    });
    
    describe('cacheGiftHandle', () => {
        
        it('should update gift lists correctly for master data', () => {
            const param = {
                changeInfo: {
                    masterUpdate: {
                        gift_map: {
                            rule1: {
                                cacheInit: true,
                                gift_list: [{ product_id: 'prod1', actual_unit: 'unit1', rowId: 'gift1' }]
                            }
                        },
                        detailDataMap: {}
                    },
                    mdUpdate: {}
                },
                detailDataMap: {
                    detail1: { parent_gift_key: 'master', product_id: 'prod1', actual_unit: 'unit1', rowId: 'gift1' }
                },
                policyInfo: {
                    groupMap: {}
                }
            };
    
            instance.cacheGiftHandle(param);
    
            expect(param.changeInfo.masterUpdate.gift_map.rule1.gift_list[0]).toEqual({
                product_id: 'prod1',
                actual_unit: 'unit1',
                rowId: 'gift1',
                isActive: true,
                parent_gift_key: 'master',
                prod_pkg_key: undefined,
                data_index: undefined,
                quantity: undefined,
                unit: undefined,
                unit__v: undefined,
                unit__r: undefined
            });
    
            expect(param.changeInfo.masterUpdate.gift_map.rule1.hold_chose).toBe("1");
            expect(param.changeInfo.masterUpdate.gift_map.rule1.cacheInit).toBeUndefined();
        });
    
        it('should update gift lists correctly for detail data', () => {
            const param = {
                changeInfo: {
                    masterUpdate: {},
                    mdUpdate: {
                        detail1: {
                            gift_map: {
                                rule1: {
                                    cacheInit: true,
                                    gift_list: [{ product_id: 'prod1', actual_unit: 'unit1', rowId: 'gift1' }]
                                }
                            },
                            group_key: 'group1'
                        }
                    }
                },
                detailDataMap: {
                    detail1: { parent_gift_key: 'detail1', product_id: 'prod1', actual_unit: 'unit1', rowId: 'gift1' }
                },
                policyInfo: {
                    groupMap: {
                        group1: ['detail1']
                    }
                }
            };
    
            instance.cacheGiftHandle(param);
    
            expect(param.changeInfo.mdUpdate.detail1.gift_map.rule1.gift_list[0]).toEqual({
                product_id: 'prod1',
                actual_unit: 'unit1',
                rowId: 'gift1',
                isActive: true,
                parent_gift_key: 'detail1',
                prod_pkg_key: undefined,
                data_index: undefined,
                quantity: undefined,
                unit: undefined,
                unit__v: undefined,
                unit__r: undefined
            });
    
            expect(param.changeInfo.mdUpdate.detail1.gift_map.rule1.hold_chose).toBe("1");
            expect(param.changeInfo.mdUpdate.detail1.gift_map.rule1.cacheInit).toBeUndefined();
        });
    
        it('should handle no matching original gift', () => {
            const param = {
                changeInfo: {
                    masterUpdate: {
                        gift_map: {
                            rule1: {
                                cacheInit: true,
                                gift_list: [{ product_id: 'prod2', actual_unit: 'unit2', rowId: 'gift2' }]
                            }
                        },
                        detailDataMap: {}
                    },
                    mdUpdate: {}
                },
                detailDataMap: {
                    detail1: { parent_gift_key: 'master', product_id: 'prod1', actual_unit: 'unit1', rowId: 'gift1' }
                },
                policyInfo: {
                    groupMap: {}
                }
            };
    
            instance.cacheGiftHandle(param);
    
            expect(param.changeInfo.masterUpdate.gift_map.rule1.gift_list[0]).toEqual({
                product_id: 'prod2',
                actual_unit: 'unit2',
                rowId: 'gift2',
                isActive: false,
                quantity: 0
            });
    
            expect(param.changeInfo.masterUpdate.gift_map.rule1.hold_chose).toBe("1");
            expect(param.changeInfo.masterUpdate.gift_map.rule1.cacheInit).toBeUndefined();
        });
    });
    
    describe('findOriGift', () => {
        
        it('should find the original gift that matches all criteria', () => {
            const parentKeys = ['key1', 'key2'];
            const detailsArr = [
                { parent_gift_key: 'key1', product_id: 'prod1', quantity: 5, actual_unit: 'unit1' },
                { parent_gift_key: 'key2', product_id: 'prod2', quantity: 10, actual_unit: 'unit2' },
                { parent_gift_key: 'key1', product_id: 'prod1', quantity: 15, actual_unit: 'unit3' }
            ];
            const g = { product_id: 'prod1', min_value: 0, max_value: 20, actual_unit: 'unit1' };
    
            const expectedOriGift = { parent_gift_key: 'key1', product_id: 'prod1', quantity: 5, actual_unit: 'unit1' };
    
            const result = instance.findOriGift(parentKeys, detailsArr, g);
            expect(result).toEqual(expectedOriGift);
        });
    
        it('should return undefined when no gift matches the criteria', () => {
            const parentKeys = ['key1', 'key2'];
            const detailsArr = [
                { parent_gift_key: 'key1', product_id: 'prod3', quantity: 5, actual_unit: 'unit1' },
                { parent_gift_key: 'key2', product_id: 'prod2', quantity: 10, actual_unit: 'unit2' }
            ];
            const g = { product_id: 'prod1', min_value: 0, max_value: 20, actual_unit: 'unit1' };
    
            const result = instance.findOriGift(parentKeys, detailsArr, g);
            expect(result).toBeUndefined();
        });
    
        it('should handle missing min_value and max_value', () => {
            const parentKeys = ['key1', 'key2'];
            const detailsArr = [
                { parent_gift_key: 'key1', product_id: 'prod1', quantity: 5, actual_unit: 'unit1' },
                { parent_gift_key: 'key2', product_id: 'prod2', quantity: 10, actual_unit: 'unit2' }
            ];
            const g = { product_id: 'prod1', actual_unit: 'unit1' }; // no min_value and max_value
    
            const expectedOriGift = { parent_gift_key: 'key1', product_id: 'prod1', quantity: 5, actual_unit: 'unit1' };
    
            const result = instance.findOriGift(parentKeys, detailsArr, g);
            expect(result).toEqual(expectedOriGift);
        });
    
        it('should handle missing actual_unit', () => {
            const parentKeys = ['key1', 'key2'];
            const detailsArr = [
                { parent_gift_key: 'key1', product_id: 'prod1', quantity: 5 },
                { parent_gift_key: 'key2', product_id: 'prod2', quantity: 10 }
            ];
            const g = { product_id: 'prod1', min_value: 0, max_value: 20 };
    
            const expectedOriGift = { parent_gift_key: 'key1', product_id: 'prod1', quantity: 5 };
    
            const result = instance.findOriGift(parentKeys, detailsArr, g);
            expect(result).toEqual(expectedOriGift);
        });
    });
   
    describe('collectGift', () => {
    
        it('should collect gifts from masterData and detailDataMap', () => {
            const param = {
                masterData: {
                    gift_map: {
                        gift1: {
                            gift_list: [{ isActive: true, prod_pkg_key: 'gift1' }]
                        }
                    }
                },
                changeInfo: {
                    mdUpdate: {
                        detail1: {}
                    }
                },
                detailDataMap: {
                    detail1: { gift_map: { gift2: { gift_list: [{ isActive: true, prod_pkg_key: 'gift2' }] } } }
                },
                matchFrom: 'mock_match_from'
            };
    
            const result = instance.collectGift(param);
    
    
            expect(result).toEqual({
                holdGifts: [],
                newGifts: [{ isActive: true, prod_pkg_key: 'gift1' }, { isActive: true, prod_pkg_key: 'gift2'  }]
            });
        });
    
        it('should handle empty masterData and detailDataMap', () => {
            const param = {
                masterData: null,
                changeInfo: {
                    mdUpdate: {}
                },
                detailDataMap: {},
                matchFrom: 'mock_match_from'
            };
    
            const result = instance.collectGift(param);
    
    
            expect(result).toEqual({
                holdGifts: [],
                newGifts: []
            });
        });
    
        it('should handle masterData without gift_map', () => {
            const param = {
                masterData: {
                    gift_map: null
                },
                changeInfo: {
                    mdUpdate: {}
                },
                detailDataMap: {},
                matchFrom: 'mock_match_from'
            };
    
            const result = instance.collectGift(param);
    
            expect(result).toEqual({
                holdGifts: [],
                newGifts: []
            });
        });
    
        it('should handle detailDataMap without gift_map', () => {
            const param = {
                masterData: null,
                changeInfo: {
                    mdUpdate: {
                        detail1: {}
                    }
                },
                detailDataMap: {
                    detail1: { gift_map: null }
                },
                matchFrom: 'mock_match_from'
            };
    
            const result = instance.collectGift(param);
    
            expect(result).toEqual({
                holdGifts: [],
                newGifts: []
            });
        });
    });
    
    describe('separateGifts', () => {
    
        it('should separate holdGifts and newGifts correctly', () => {
            const data = {
                gift_map: {
                    rule1: {
                        hold_chose: '1',
                        gift_list: [{ isActive: true, prod_pkg_key: 'gift1' }]
                    },
                    rule2: {
                        hold_chose: '0',
                        gift_list: [{ isActive: true, prod_pkg_key: 'gift2' }]
                    }
                },
                prod_pkg_key: 'master_key',
                price_policy_id: 'policy1',
                group_key: 'policy1_rule1'
            };
    
            const holdGifts = [];
            const newGifts = [];
            const detailDataArr = [
                { parent_gift_key: 'master_key', price_policy_rule_ids: ['rule1'], prod_pkg_key: 'detail1', price_policy_id: 'policy1' },
                { parent_gift_key: 'master_key', price_policy_rule_ids: ['rule2'], prod_pkg_key: 'detail2', price_policy_id: 'policy1' }
            ];
            const matchFrom = 'mock_match_from';
    
            const result = instance.separateGifts(data, holdGifts, newGifts, detailDataArr, matchFrom);
    
            expect(result.holdGifts).toEqual(['detail1']);
            expect(result.newGifts).toEqual([{ isActive: true, prod_pkg_key: 'gift2' }]);
        });
    
        it('should handle empty gift_map', () => {
            const data = {
                gift_map: {},
                prod_pkg_key: 'master_key',
                price_policy_id: 'policy1',
                group_key: 'policy1_rule1'
            };
    
            const holdGifts = [];
            const newGifts = [];
            const detailDataArr = [
                { parent_gift_key: 'master_key', price_policy_rule_ids: ['rule1'], prod_pkg_key: 'detail1', price_policy_id: 'policy1' }
            ];
            const matchFrom = 'mock_match_from';
    
            const result = instance.separateGifts(data, holdGifts, newGifts, detailDataArr, matchFrom);
    
            expect(result.holdGifts).toEqual([]);
            expect(result.newGifts).toEqual([]);
        });
    
        it('should handle holdGifts for group rules', () => {
            const data = {
                gift_map: {
                    rule1: {
                        hold_chose: '1',
                        gift_list: [{ isActive: true, prod_pkg_key: 'gift1' }]
                    }
                },
                prod_pkg_key: 'master_key',
                price_policy_id: 'policy1',
                group_key: 'policy1_rule1'
            };
    
            const holdGifts = [];
            const newGifts = [];
            const detailDataArr = [
                { parent_gift_key: 'master_key', price_policy_rule_ids: ['rule1'], prod_pkg_key: 'detail1', price_policy_id: 'policy1' },
                { parent_gift_key: 'other_key', price_policy_rule_ids: ['rule1'], prod_pkg_key: 'detail2', price_policy_id: 'policy1' }
            ];
            const matchFrom = 'mock_match_from';
    
            const result = instance.separateGifts(data, holdGifts, newGifts, detailDataArr, matchFrom);
    
            expect(result.holdGifts).toEqual(['detail1', 'detail2']);
            expect(result.newGifts).toEqual([]);
        });
    
        it('should not add inactive gifts to newGifts', () => {
            const data = {
                gift_map: {
                    rule1: {
                        hold_chose: '0',
                        gift_list: [{ isActive: false, prod_pkg_key: 'gift1' }]
                    }
                },
                prod_pkg_key: 'master_key',
                price_policy_id: 'policy1',
                group_key: 'policy1_rule1'
            };
    
            const holdGifts = [];
            const newGifts = [];
            const detailDataArr = [
                { parent_gift_key: 'master_key', price_policy_rule_ids: ['rule1'], prod_pkg_key: 'detail1', price_policy_id: 'policy1' }
            ];
            const matchFrom = 'mock_match_from';
    
            const result = instance.separateGifts(data, holdGifts, newGifts, detailDataArr, matchFrom);
    
            expect(result.holdGifts).toEqual([]);
            expect(result.newGifts).toEqual([]);
        });
    });
    
    describe('updateByLocalNew', () => {
        beforeEach(()=>{
            instance.detailApiName="detailApiName";
            instance.fields = {
                detailApiName: {
                    product_id: {
                        is_open_display_name: true
                    }
                }
            };
            instance.getRowBasicData = jest.fn(() => ({
                basicField1: 'basicValue1',
                basicField2: 'basicValue2'
            }));
    
        })
        it('should update gifts with local data', () => {
            const gifts = [
                {
                    product_id: 'prod1',
                    display_name: 'Product display1',
                    product_id__s: 'P1',
                    price: 100,
                    unit: 'unit1',
                    unit_name: 'Unit 1',
                    is_multiple_unit: true
                }
            ];
    
            const result = instance.updateByLocalNew(gifts);
    
            expect(result).toEqual([
                {
                    basicField1: 'basicValue1',
                    basicField2: 'basicValue2',
                    id:'prod1',
                    product_id: 'prod1',
                    product_id__r: 'Product display1',
                    discount: '100',
                    gift_amortize_price: 100,
                    unit: 'unit1',
                    unit__v: 'unit1',
                    unit__r: 'Unit 1',
                    is_multiple_unit: true,
                    is_multiple_unit__v: true,
                    is_giveaway: '1',
                    product_id__s: 'Product display1',
                    price: 100,
                    unit: 'unit1',
                    display_name: 'Product display1',
                    unit_name: 'Unit 1',
                }
            ]);
        });
    
        it('should update gifts with local data when display name is not open', () => {
            // Close the display name
            instance.fields.detailApiName.product_id.is_open_display_name = false;
    
            const gifts = [
                {
                    product_id: 'prod1',
                    product_id__s: 'P1',
                    price: 100,
                    unit: 'unit1',
                    unit_name: 'Unit 1',
                    is_multiple_unit: true
                }
            ];
    
            const result = instance.updateByLocalNew(gifts);
    
            expect(result).toEqual([
                {
                    basicField1: 'basicValue1',
                    basicField2: 'basicValue2',
                    id:'prod1',
                    product_id: 'prod1',
                    product_id__r: 'P1',
                    discount: '100',
                    gift_amortize_price: 100,
                    unit: 'unit1',
                    unit__v: 'unit1',
                    unit__r: 'Unit 1',
                    is_multiple_unit: true,
                    is_multiple_unit__v: true,
                    is_giveaway: '1',
                    product_id__s: 'P1',
                    price: 100,
                    unit_name: 'Unit 1',
                }
            ]);
        });
    });
    
    describe('getUnitByCalcPrice', () => {
    
        beforeEach(() => {
            // Mock reqUnit method
            instance.reqUnit = jest.fn(async (unitGifts) => {
                return {
                    caclResult: unitGifts.map(gift => ({
                        rowId: gift.rowId,
                        productId: gift[instance.fieldMap.product_id],
                        unitId: gift[instance.fieldMap.actual_unit],
                        price: 100,
                        priceBookPrice: 90,
                        conversion_ratio: 1,
                        base_unit_count: 10,
                        stat_unit_count: 10,
                        other_unit: 'unit1',
                        other_unit_quantity: 10
                    }))
                };
            });
            instance.collectChangInfo = jest.fn();
        });
    
        it('should update gifts with calculated unit information', async () => {
            const param = {};
            const gifts = [
                { rowId: '1', product_id: 'prod1', actual_unit: 'unit1', is_multiple_unit__v: true, price: 100 },
                { rowId: '2', product_id: 'prod2', actual_unit: 'unit2', is_multiple_unit__v: true }
            ];
    
            const result = await instance.getUnitByCalcPrice(param, gifts);
    
    
            expect(result).toEqual([
                {
                    rowId: '1',
                    product_id: 'prod1',
                    actual_unit: 'unit1',
                    is_multiple_unit__v: true,
                    price: 100,
                    product_price: 100,
                    price_book_price: 90,
                    conversion_ratio: 1,
                    base_unit_count: 10,
                    stat_unit_count: 10,
                    other_unit: 'unit1',
                    other_unit_quantity: 10,
                    gift_amortize_price: 100
                },
                {
                    rowId: '2',
                    product_id: 'prod2',
                    actual_unit: 'unit2',
                    is_multiple_unit__v: true,
                    product_price: 100,
                    price_book_price: 90,
                    conversion_ratio: 1,
                    base_unit_count: 10,
                    stat_unit_count: 10,
                    other_unit: 'unit1',
                    other_unit_quantity: 10
                }
            ]);
    
            expect(instance.collectChangInfo).toHaveBeenCalledTimes(2);
        });
    
        it('should handle gifts without matching unit information', async () => {
            instance.reqUnit = jest.fn(async (unitGifts) => {
                return {
                    caclResult: []
                };
            });
    
            const param = {};
            const gifts = [
                { rowId: '1', product_id: 'prod1', actual_unit: 'unit1', is_multiple_unit__v: true, quantity: 5 }
            ];
    
            const result = await instance.getUnitByCalcPrice(param, gifts);
    
            expect(result).toEqual([
                {
                    rowId: '1',
                    product_id: 'prod1',
                    actual_unit: 'unit1',
                    is_multiple_unit__v: true,
                    quantity: 5,
                    conversion_ratio: "1",
                    base_unit_count: 5,
                    stat_unit_count: ''
                }
            ]);
    
        });
    });
    
    describe('reqUnit', () => {
        
        beforeEach(() => {
            // Mock masterData
            instance.masterData = {
                mcCurrency: 'USD'
            };
            // Mock detailApiName
            instance.detailApiName = 'detailApiName';
        });
    
        it('should return an empty array if unitGifts is empty', () => {
            const unitGifts = [];
            const result = instance.reqUnit(unitGifts);
            expect(result).toEqual([]);
        });
    
        it('should return an empty array if unitGifts is not provided', () => {
            const result = instance.reqUnit(undefined);
            expect(result).toEqual([]);
        });
    
        it('should call PPM.ajax with the correct arguments', () => {
            const unitGifts = [
                {
                    product_id: 'prod1',
                    price_book_product_id: 'pbp1',
                    actual_unit: 'unit1',
                    quantity: 10,
                    prod_pkg_key: 'pkg1'
                },
                {
                    product_id: 'prod2',
                    price_book_product_id: '',
                    actual_unit: 'unit2',
                    quantity: 5,
                    prod_pkg_key: 'pkg2'
                }
            ];
    
            const expectedArgs = {
                params: [
                    {
                        productId: 'prod1',
                        priceBookProductId: 'pbp1',
                        unitId: 'unit1',
                        count: 10,
                        rowId: 'pkg1'
                    },
                    {
                        productId: 'prod2',
                        priceBookProductId: '',
                        unitId: 'unit2',
                        count: 5,
                        rowId: 'pkg2'
                    }
                ],
                describeApiName: 'detailApiName',
                mcCurrency: 'USD'
            };
    
            const url = "FHH/EM1HNCRM/API/v1/object/mutipleUnit/service/calcPriceByUnit";
    
            instance.reqUnit(unitGifts);
    
            expect(PPM.ajax).toHaveBeenCalledWith(instance.request, url, expectedArgs);
        });
    });
    
    describe('giftPriceByPriceBook', () => {
        beforeEach(() => {
            // Mock reqRealPrice
            instance.reqRealPrice = jest.fn(async (gifts, param) => {
                return {
                    newRst: gifts.map(gift => ({
                        rowId: gift.rowId,
                        selling_price: 100,
                        discount: 10,
                        pricebook_price: 90,
                        pricebook_id: 'pricebook1',
                        pricebook_id__r: 'Price Book 1',
                        _id: 'pricebook_product1',
                        name: 'Product 1'
                    }))
                };
            });
        });
    
        it('should update gifts with price information from price book', async () => {
            const param = {};
            const gifts = [
                { rowId: '1', product_id: 'prod1' },
                { rowId: '2', product_id: 'prod2' }
            ];
    
            const result = await instance.giftPriceByPriceBook(param, gifts);
    
            expect(instance.reqRealPrice).toHaveBeenCalledWith(gifts, param);
    
            expect(result).toEqual([
                {
                    rowId: '1',
                    product_id: 'prod1',
                    product_price: 100,
                    price_book_discount: 10,
                    price_book_price: 90,
                    price_book_id: 'pricebook1',
                    price_book_id__r: 'Price Book 1',
                    price_book_product_id: 'pricebook_product1',
                    price_book_product_id__r: 'Product 1'
                },
                {
                    rowId: '2',
                    product_id: 'prod2',
                    product_price: 100,
                    price_book_discount: 10,
                    price_book_price: 90,
                    price_book_id: 'pricebook1',
                    price_book_id__r: 'Price Book 1',
                    price_book_product_id: 'pricebook_product1',
                    price_book_product_id__r: 'Product 1'
                }
            ]);
        });
    
        it('should handle gifts with no matching price information', async () => {
            instance.reqRealPrice = jest.fn(async (gifts, param) => {
                return {
                    newRst: []
                };
            });
    
            const param = {};
            const gifts = [
                { rowId: '1', product_id: 'prod1' }
            ];
    
            const result = await instance.giftPriceByPriceBook(param, gifts);
    
            expect(instance.reqRealPrice).toHaveBeenCalledWith(gifts, param);
    
            expect(result).toEqual([
                { rowId: '1', product_id: 'prod1' }
            ]);
        });
    });
    
    describe('reqRealPrice', () => {
    
        beforeEach(() => {
              
            // Mock masterData
            instance.masterData = {
                account_id: 'acc123',
                partner_id: 'partner123',
                mcCurrency: 'USD',
                price_book_id: 'pricebook123'
            };
    
            // Mock detailApiName
            instance.detailApiName = 'detailApiName';
            // Mock detailDesc
            instance.detailDesc = {
                fields: {
                    price_book_id: 'price_book_id'
                }
            };
            // Mock request object
            instance.request = {};
        });
    
        it('should return an empty array if reqData is empty', () => {
            const reqData = [];
            const param = {};
            const result = instance.reqRealPrice(reqData, param);
            expect(result).toEqual({ newRes: [] });
        });
    
        it('should return an empty array if reqData is not provided', () => {
            const param = {};
            const result = instance.reqRealPrice(undefined, param);
            expect(result).toEqual({ newRes: [] });
        });
    
        it('should call PPM.ajax with the correct arguments', () => {
            const reqData = [
                {
                    rowId: '1',
                    product_id: 'prod1',
                    actual_unit: 'unit1',
                    is_this_product: true,
                    price_book_id: 'pb123',
                    quantity: 5
                },
                {
                    rowId: '2',
                    product_id: 'prod2',
                    actual_unit: 'unit2',
                    quantity: 10
                }
            ];
    
            const param = {
                detailDataMap: {
                    detail1: {
                        product_id: 'prod1',
                        actual_unit: 'unit1'
                    },
                    detail2: {
                        product_id: 'prod2',
                        actual_unit: 'unit2'
                    }
                }
            };
    
            const expectedArgs = {
                accountId: 'acc123',
                partnerId: 'partner123',
                mcCurrency: 'USD',
                fullProductList: [
                    {
                        rowId: '1',
                        productId: 'prod1',
                        unit: 'unit1',
                        priceBookId: 'pb123'
                    },
                    {
                        rowId: '2',
                        productId: 'prod2',
                        unit: 'unit2',
                        priceBookId: 'pricebook123'
                    }
                ],
                productIdList: ['prod1', 'prod2'],
                object_data: {
                    account_id: 'acc123',
                    partner_id: 'partner123',
                    mcCurrency: 'USD',
                    price_book_id: 'pricebook123'
                },
                details: { parsedDetails: 'mockedDetails' }
            };
    
            const url = 'FHH/EM1HNCRM/API/v1/object/available_range/service/get_real_price';
    
            instance.reqRealPrice(reqData, param);
    
            expect(PPM.parsePriceBookDataRangeDetails).toHaveBeenCalledWith(
                { detailApiName: Object.values(param.detailDataMap) },
                instance.detailDesc.fields.price_book_id
            );
    
            expect(PPM.ajax).toHaveBeenCalledWith(instance.request, url, expectedArgs);
        });
    });
    
    describe('calSelfGiftPrice', () => {

        it('should calculate gift prices based on parent details', () => {
            const param = {
                detailDataMap: {
                    parent1: {
                        price_book_discount: 10,
                        price_book_price: 100,
                        price_book_id: 'pb1',
                        price_book_id__r: 'Price Book 1',
                        price_book_product_id: 'pbp1',
                        price_book_product_id__r: 'Product 1',
                        is_multiple_unit__v: true,
                        actual_unit: 'unit1',
                        conversion_ratio: 2
                    }
                }
            };
    
            const gifts = [
                {
                    is_this_product: true,
                    parent_gift_key: 'parent1',
                    actual_unit: 'unit2',
                    conversion_ratio: 1
                },
                {
                    is_this_product: false,
                    parent_gift_key: 'parent1'
                }
            ];
    
            const result = instance.calSelfGiftPrice(param, gifts);
    
            expect(result).toEqual([
                {
                    is_this_product: true,
                    parent_gift_key: 'parent1',
                    actual_unit: 'unit2',
                    conversion_ratio: 1,
                    price_book_discount: 10,
                    price_book_price: 50,
                    price_book_id: 'pb1',
                    price_book_id__r: 'Price Book 1',
                    price_book_product_id: 'pbp1',
                    price_book_product_id__r: 'Product 1'
                },
                {
                    is_this_product: false,
                    parent_gift_key: 'parent1'
                }
            ]);
    
            expect(PPM.multiplicational).toHaveBeenCalledWith(100, 1);
            expect(PPM.division).toHaveBeenCalledWith(100, 2);
        });
    
        it('should handle gifts that are not the main product', () => {
            const param = {
                detailDataMap: {
                    parent1: {
                        price_book_discount: 10,
                        price_book_price: 100,
                        price_book_id: 'pb1',
                        price_book_id__r: 'Price Book 1',
                        price_book_product_id: 'pbp1',
                        price_book_product_id__r: 'Product 1'
                    }
                }
            };
    
            const gifts = [
                {
                    is_this_product: false,
                    parent_gift_key: 'parent1'
                }
            ];
    
            const result = instance.calSelfGiftPrice(param, gifts);
    
            expect(result).toEqual([
                {
                    is_this_product: false,
                    parent_gift_key: 'parent1'
                }
            ]);
    
            expect(PPM.multiplicational).not.toHaveBeenCalled();
            expect(PPM.division).not.toHaveBeenCalled();
        });
    });
    
    describe('calGiftAmortize', () => {
    
        beforeEach(() => {
            // Mock giftAmortizeBasis
            instance.giftAmortizeBasis = 'price_book_price';
    
            // Mock collectChangInfo
            instance.collectChangInfo = jest.fn();
        });
    
        it('should calculate amortize values based on price_book_price', () => {
            const param = {};
            const gifts = [
                {
                    prod_pkg_key: 'pkg1',
                    quantity: 10,
                    price_book_price: 5
                },
                {
                    prod_pkg_key: 'pkg2',
                    quantity: 20,
                    price_book_price: 10
                }
            ];
    
            const result = instance.calGiftAmortize(param, gifts);
    
            expect(result).toEqual([
                {
                    prod_pkg_key: 'pkg1',
                    quantity: 10,
                    price_book_price: 5,
                    policy_dynamic_amount: -50,
                    gift_amortize_price: 5
                },
                {
                    prod_pkg_key: 'pkg2',
                    quantity: 20,
                    price_book_price: 10,
                    policy_dynamic_amount: -200,
                    gift_amortize_price: 10
                }
            ]);
    
            expect(PPM.multiplicational).toHaveBeenCalledWith(10, 5);
            expect(PPM.multiplicational).toHaveBeenCalledWith(20, 10);
            //expect(PolicyUtil.parseNumByDecimal).toHaveBeenCalledWith(-50, instance.decimalMap.policy_dynamic_amount, -50, true);
            //expect(PolicyUtil.parseNumByDecimal).toHaveBeenCalledWith(-200, instance.decimalMap.policy_dynamic_amount, -200, true);
            //expect(PolicyUtil.parseNumByDecimal).toHaveBeenCalledWith(5, instance.decimalMap.gift_amortize_price);
            //expect(PolicyUtil.parseNumByDecimal).toHaveBeenCalledWith(10, instance.decimalMap.gift_amortize_price);
            expect(instance.collectChangInfo).toHaveBeenCalledTimes(2);
        });
    
        it('should use existing gift_amortize_price when giftAmortizeBasis is not price_book_price', () => {
            instance.giftAmortizeBasis = 'other_basis';
            
            const param = {};
            const gifts = [
                {
                    prod_pkg_key: 'pkg1',
                    quantity: 10,
                    price_book_price: 5,
                    gift_amortize_price: 3
                },
                {
                    prod_pkg_key: 'pkg2',
                    quantity: 20,
                    price_book_price: 10,
                    gift_amortize_price: 8
                }
            ];
    
            const result = instance.calGiftAmortize(param, gifts);
    
            expect(result).toEqual([
                {
                    prod_pkg_key: 'pkg1',
                    quantity: 10,
                    price_book_price: 5,
                    gift_amortize_price: 3,
                    policy_dynamic_amount: -50
                },
                {
                    prod_pkg_key: 'pkg2',
                    quantity: 20,
                    price_book_price: 10,
                    gift_amortize_price: 8,
                    policy_dynamic_amount: -200
                }
            ]);
    
            expect(PPM.multiplicational).toHaveBeenCalledWith(10, 5);
            expect(PPM.multiplicational).toHaveBeenCalledWith(20, 10);
           // expect(PolicyUtil.parseNumByDecimal).toHaveBeenCalledWith(-50, instance.decimalMap.policy_dynamic_amount, -50, true);
            //expect(PolicyUtil.parseNumByDecimal).toHaveBeenCalledWith(-200, instance.decimalMap.policy_dynamic_amount, -200, true);
            expect(instance.collectChangInfo).toHaveBeenCalledTimes(2);
        });
    });
    
    describe('collectChangInfo', () => {
        let param: any;
    
        beforeEach(() => {
            param = {
                changeInfo: {
                    mdUpdate: {}
                }
            };
        });
    
        it('should add new key-value pair if key does not exist in mdUpdate', () => {
            const key = 'testKey';
            const updateItem = { field1: 'value1' };
    
            instance.collectChangInfo(param, key, updateItem);
    
            expect(param.changeInfo.mdUpdate[key]).toEqual(updateItem);
        });
    
        it('should update existing key-value pair if key already exists in mdUpdate', () => {
            const key = 'testKey';
            const existingItem = { field1: 'oldValue', field2: 'value2' };
            const updateItem = { field1: 'newValue' };
    
            param.changeInfo.mdUpdate[key] = existingItem;
    
            instance.collectChangInfo(param, key, updateItem);
    
            expect(param.changeInfo.mdUpdate[key]).toEqual({
                field1: 'newValue',
                field2: 'value2'
            });
        });
    
        it('should handle empty updateItem gracefully', () => {
            const key = 'testKey';
            const updateItem = {};
    
            instance.collectChangInfo(param, key, updateItem);
    
            expect(param.changeInfo.mdUpdate[key]).toEqual({});
        });
    
        it('should merge updateItem into existing item correctly', () => {
            const key = 'testKey';
            const existingItem = { field1: 'value1', field2: 'value2' };
            const updateItem = { field3: 'value3' };
    
            param.changeInfo.mdUpdate[key] = existingItem;
    
            instance.collectChangInfo(param, key, updateItem);
    
            expect(param.changeInfo.mdUpdate[key]).toEqual({
                field1: 'value1',
                field2: 'value2',
                field3: 'value3'
            });
        });
    });

    // describe('calBatch', () => {
    //     let gifts;
    
    //     beforeEach(() => {
    //         gifts = [{ productId: 'gift1' }, { productId: 'gift2' }];
    
    //         // 模拟方法的实现
    //     jest.spyOn(instance, 'updateCalRes').mockImplementation(async (data) => data);
    //     jest.spyOn(instance, 'triggerCal').mockImplementation(async (data) => data);
    //     jest.spyOn(instance, 'parseCalArgs').mockImplementation(async (arg, data) => data);

    //     });
    
    //     it('should call composeAsync with the correct functions', async () => {
    //         await instance.calBatch(gifts);
    
    //         expect(PPM.curry).toHaveBeenCalledWith(2, instance.updateCalRes.bind(instance));
    //         expect(PPM.curry).toHaveBeenCalledWith(2, instance.parseCalArgs.bind(instance));
    //         expect(PPM.composeAsync).toHaveBeenCalledWith(
    //             instance.updateCalRes.bind(instance),
    //             instance.triggerCal.bind(instance),
    //             instance.parseCalArgs.bind(instance)
    //         );
    //     });
    
    //     it('should execute the composed functions in the correct order', async () => {
    //         const result = await instance.calBatch(gifts);
    
    //         expect(instance.parseCalArgs).toHaveBeenCalledWith(gifts);
    //         expect(instance.triggerCal).toHaveBeenCalledWith(gifts);
    //         expect(instance.updateCalRes).toHaveBeenCalledWith(gifts);
    //         expect(result).toEqual(gifts);
    //     });
    
    //     it('should return the dataFormPolicy', async () => {
    //         const mockResult = [{ productId: 'resultGift' }];
    //         instance.updateCalRes.mockResolvedValue(mockResult);
    
    //         const result = await instance.calBatch(gifts);
    
    //         expect(result).toEqual(mockResult);
    //     });
    // });

    describe('parseCalArgs', () => {
        let gifts;
    
        beforeEach(() => {
            gifts = [{ prod_pkg_key: 'gift1' }, { prod_pkg_key: 'gift2' }];
           
            // 设置 PolicyUtil.getCalArgs 的返回值
            PolicyUtil.getCalArgs = jest.fn().mockImplementation(()=>'calculatedArgs');
           
        });
    
        it('should call collectCalFields if modifyFields is provided', () => {
            const modifyFields = { apiName: ['field1'] };
            const result = instance.parseCalArgs(modifyFields, gifts);
    
            expect(PPM.collectCalFields).toHaveBeenCalledWith(instance.fields, modifyFields, [instance.detailApiName]);
            expect(PPM.getCalFields).not.toHaveBeenCalled();
            expect(result).toEqual('calculatedArgs');
        });
    
        it('should call getCalFields if modifyFields is not provided', () => {
            const result = instance.parseCalArgs(null, gifts);
    
            expect(PPM.getCalFields).toHaveBeenCalledWith(instance.fields[instance.detailApiName], true);
            expect(PPM.collectCalFields).not.toHaveBeenCalled();
            expect(result).toEqual('calculatedArgs');
        });
    
        it('should filter out non-calculable fields', () => {
            const modifyFields = { apiName: ['field1'] };
            const result = instance.parseCalArgs(modifyFields, gifts);
    
            expect(PPM.collectCalFields).toHaveBeenCalledWith(instance.fields, modifyFields, [instance.detailApiName]);
            expect(result).toEqual('calculatedArgs');
        });
    
        it('should return the correct arguments for getCalArgs', () => {
            const modifyFields = { apiName: ['field1'] };
            const result = instance.parseCalArgs(modifyFields, gifts);
    
            expect(PolicyUtil.getCalArgs).toHaveBeenCalledWith(
                {
                    masterData: instance.masterData,
                    modifyInfo: {
                        modifyFields: modifyFields,
                        modifyIndex: gifts.map((g) => g[instance.fieldMap.prod_pkg_key]),
                    },
                },
                instance.masterApiName,
                instance.detailApiName,
                {
                    detailApiName: ['field1', 'field2', 'field3']
                },
                "mdEdit",
                gifts,
                {
                    detailApiName: [
                        instance.fieldMap.product_price,
                        instance.fieldMap.price_book_price,
                        instance.fieldMap.price_book_discount,
                        instance.fieldMap.policy_dynamic_amount,
                        instance.fieldMap.quantity
                    ]
                }
            );
            expect(result).toEqual('calculatedArgs');
        });
    });

    describe('updateCalRes', () => {
        let gifts, result;
    
        beforeEach(() => {
            gifts = [
                { prod_pkg_key: 'gift1', otherField: 'value1' },
                { prod_pkg_key: 'gift2', otherField: 'value2' }
            ];
            result = {
                Value: {
                    calculateResult: {
                        detailApiName: {
                            gift1: { is_package__v: true, field1: 'newValue1' },
                            gift2: { is_package__v: false, field1: 'newValue2' }
                        }
                    }
                }
            };
        });
    
        it('should return gifts unchanged if result is not provided', () => {
            const result = null;
            const updatedGifts = instance.updateCalRes(gifts, result);
            expect(updatedGifts).toEqual(gifts);
        });
    
        it('should update gifts with the calculation results', () => {
            const updatedGifts = instance.updateCalRes(gifts, result);
            expect(updatedGifts).toEqual([
                { prod_pkg_key: 'gift1', otherField: 'value1', field1: 'newValue1' },
                { prod_pkg_key: 'gift2', otherField: 'value2', field1: 'newValue2', is_package__v: false }
            ]);
        });
    
        it('should remove package information from bom gifts', () => {
            result.Value.calculateResult.detailApiName.gift1.is_package = true;
            const updatedGifts = instance.updateCalRes(gifts, result);
            expect(updatedGifts[0]).toEqual({
                prod_pkg_key: 'gift1',
                otherField: 'value1',
                field1: 'newValue1'
            });
        });
    });

    describe('updateCalResForModify', () => {
        let param, result;
    
        beforeEach(() => {
            param = {
                detailDataMap: {
                    detail1: { rowId: '1', fieldA: 'oldA' },
                    detail2: { rowId: '2', fieldB: 'oldB' }
                },
                changeInfo: {
                    mdUpdate: {
                        detail1: { fieldA: 'oldA' },
                        detail2: { fieldB: 'oldB' }
                    }
                }
            };
    
            result = {
                Value: {
                    calculateResult: {
                        detailApiName: {
                            1: { fieldA: 'newA' },
                            2: { fieldB: 'newB' }
                        }
                    }
                }
            };
        });
    
        it('should return param unchanged if result is not provided', () => {
            const result = null;
            const updatedParam = instance.updateCalResForModify(param, result);
            expect(updatedParam).toEqual(param);
        });
    
        it('should update param with the calculation results', () => {
            const updatedParam = instance.updateCalResForModify(param, result);
            expect(updatedParam.detailDataMap).toEqual({
                detail1: { rowId: '1', fieldA: 'newA' },
                detail2: { rowId: '2', fieldB: 'newB' }
            });
            expect(updatedParam.changeInfo.mdUpdate).toEqual({
                detail1: { fieldA: 'newA' },
                detail2: { fieldB: 'newB' }
            });
        });
    
        it('should handle empty calculateResult in result', () => {
            result = { Value: { calculateResult: {} } };
            const updatedParam = instance.updateCalResForModify(param, result);
            expect(updatedParam).toEqual(param);
        });
    
        it('should handle missing detailApiName in calculateResult', () => {
            result = { Value: { calculateResult: { someOtherApiName: { 1: { fieldA: 'newA' } } } } };
            const updatedParam = instance.updateCalResForModify(param, result);
            expect(updatedParam).toEqual(param);
        });
    });

    describe('getGiftByUnit', () => {
        let gifts, unitType, getUnitParam, result;
    
        beforeEach(() => {
            gifts = [{ id: 'gift1' }, { id: 'gift2' }];
            unitType = 'someUnitType';
            getUnitParam = {
                gifts: gifts,
                unitType: unitType,
                info: null
            };
            result = { processed: true };
         
            // 模拟方法的返回值
            jest.spyOn(instance, 'parseUnitGifts').mockImplementation(async (data) => data);
            jest.spyOn(instance, 'requestUnit').mockImplementation(async (data) => result);
        });
    
        it('should call composeAsync with the correct functions', async () => {
            await instance.getGiftByUnit(gifts, unitType);
    
            expect(PPM.curry).toHaveBeenCalled();
            expect(PPM.composeAsync).toHaveBeenCalled();
        });
    
        it('should execute the composed functions in the correct order', async () => {
            const result = await instance.getGiftByUnit(gifts, unitType);
    
            expect(instance.requestUnit).toHaveBeenCalledWith(getUnitParam);
            //expect(instance.parseUnitGifts).toHaveBeenCalledWith({"processed": true});
           // expect(result).toEqual({"processed": true});
        });
    
        it('should return the processed result', async () => {
            const result = await instance.getGiftByUnit(gifts, unitType);
    
            expect(result).toEqual(getUnitParam);
        });
    });




    describe('allocateQuantities', () => {
       
        beforeEach(() => {
            jest.spyOn(instance, 'allocateGifts').mockImplementation(() => {});
        });

        it('allocateQuantities calls allocateGifts for master and detail gifts', () => {
            const mockParam = {
                masterData: { gift_map: { 'rule1': {} } },
                detailDataMap: {
                    'detail1': { gift_map: { 'rule2': {} } },
                    'detail2': { gift_map: { 'rule3': {} } }
                }
            };
    
            instance.allocateQuantities(mockParam, []);
    
            // 检查allocateGifts是否被调用了正确的次数，应该是一次主对象赠品，两次从对象赠品
            expect(instance.allocateGifts).toHaveBeenCalledTimes(3);
            
            // 检查allocateGifts是否使用了正确的参数
            expect(instance.allocateGifts).toHaveBeenCalledWith( { 'rule1': {} } , 2);
            expect(instance.allocateGifts).toHaveBeenCalledWith({ 'rule2': {} }, 2);
            expect(instance.allocateGifts).toHaveBeenCalledWith({ 'rule3': {} }, 2);
        });
    });
    
    describe('allocateGifts', () => {

        it('allocateGifts calls the appropriate parser function for each gift item', () => {
            const mockParser = jest.fn();
            const giftMap = {
                "rule1": { type: 'type1', hold_chose: '0', cacheInit: false },
                "rule2": { type: 'type2', hold_chose: '1', cacheInit: true },
                "rule3": { type: 'type3', hold_chose: '1', cacheInit: false }
            };

            instance.getGiftParser = jest.fn().mockImplementation(() => mockParser);

            instance.allocateGifts(giftMap, 2);

            // Expect getGiftParser to be called for rule1 and rule3, but not for rule2
            expect(instance.getGiftParser).toHaveBeenCalledTimes(2);
            expect(instance.getGiftParser).toHaveBeenCalledWith('type1');
            expect(instance.getGiftParser).toHaveBeenCalledWith('type2');
            expect(instance.getGiftParser).not.toHaveBeenCalledWith('type3');

            // Expect the parser function to be called for rule1 and rule3
            expect(mockParser).toHaveBeenCalledTimes(2);
            expect(mockParser).toHaveBeenCalledWith("rule1", giftMap["rule1"], 2);
            expect(mockParser).toHaveBeenCalledWith("rule2", giftMap["rule2"], 2);
        });
    });

    describe('getGiftParser', () => {
        beforeEach(() => {

            // 设置 spy
            jest.spyOn(instance, 'parseFixGift').mockImplementation(() => { });
            jest.spyOn(instance, 'parseOptionalGift').mockImplementation(() => { });
        });

        test('getGiftParser returns mock functions for known types', () => {
            const fixParser = instance.getGiftParser('FIX');
            const optionalParser = instance.getGiftParser('OPTIONAL');

            // 调用这些函数
            fixParser();
            expect(instance.parseFixGift).toHaveBeenCalled();
            optionalParser();
            expect(instance.parseOptionalGift).toHaveBeenCalled();
        });


    });

    describe('parseFixGift Method', () => {

        it('should process gift items based on quantity and cycle count', () => {
            const ruleId = 'rule123';
            const qDecimal = 2;
            let mockGiftItem = {
                gift_basis: 'quantity',
                cycle_count: 2,
                gift_list: [
                    { max_value: 100, 'somePriceKey': 50 }
                ]
            };
            instance.parseFixGift(ruleId, mockGiftItem, qDecimal);

            //expect(mockGiftItem.gift_list[0].quantity).toBe(200);
            expect(mockGiftItem.gift_list[0].isActive).toBeTruthy();
        });
    });

    describe('parseOptionalGift', () => {

        const ruleId = "rule123";
        const qDecimal = 2;
        const mockGiftItem = {
            gift_basis: "quantity",
            cycle_count: 1,
            gift_kind_upper_limit: '3',
            gift_total_num: '100',
            gift_list: [{
                required: true,
                max_value: "50",
                min_value: "5"
            }]
        };


        it('should initialize gift values on first stage and calculate correct total numbers', () => {
            instance.parseOptionalGift(ruleId, mockGiftItem, qDecimal);

            // Verify that gift attributes are set correctly
            expect(mockGiftItem.gift_list[0].max_value).not.toBe(Infinity);
            expect(mockGiftItem.gift_list[0].min_value).toBeGreaterThan(0);

            // Check updated values on the giftItem
            expect(mockGiftItem.gift_total_num).toBe(100); // Example, should match expected logic
        });

    });

    describe('calQuantityInfo', () => {

        const mockGift = {
            required: true,
            max_value: 100,
            min_value: 1,
            isActive: false,
            quantity: 0,
            price_book_price: 20
        };


        test('should return existing quantity, used kind, and number when activeNum >= maxNum', () => {
            const result = instance.calQuantityInfo(mockGift, 2, 3, 0, 5, 2, 1, 'quantity');
            expect(result.quantityNum).toBe(1);
            expect(result.usedKind).toBe(3);
            expect(result.usedNum).toBe(1);
        });

        // Test for stage 2
        test('stage 2 only allocates up to the maximum amount or remaining total, whichever is smaller', () => {
            const result = instance.calQuantityInfo(mockGift, 1, 3, 3, 100, 2, 2, 'quantity');
            expect(result.quantityNum).toBeLessThanOrEqual(100);
            expect(result.usedNum).toBeLessThanOrEqual(100);
        });

        // Test for stage 3
        test('stage 3 allocates remaining gifts up to the smaller of the gift or total remaining', () => {
            const result = instance.calQuantityInfo(mockGift, 1, 3, 95, 100, 2, 3, 'quantity');
            expect(result.usedNum).toBeLessThan(100);
        });

        test('throws an error for unknown stage', () => {
            expect(() => {
                instance.calQuantityInfo(mockGift, 1, 3, 3, 5, 2, 99, 'quantity');
            }).toThrow('Unknown stage: 99');
        });
    });

    describe('getNumByPrice', () => {
        it('getNumByPrice returns 1 if price is 0', () => {
            const result = instance.getNumByPrice(100, 0);
            expect(result).toBe(1);
        });

        it('getNumByPrice returns 1 if price is undefined', () => {
            const result = instance.getNumByPrice(100);
            expect(result).toBe(1);
        });

        it('getNumByPrice calls PPM.division and returns its result if price is not 0', () => {
            const amount = 100;
            const price = 10;
            const expected = 10; // Expected result of 100 / 10
            const result = instance.getNumByPrice(amount, price);
            expect(PPM.division).toHaveBeenCalledWith(amount, price);
            expect(result).toBe(expected);
        });
    })

});
