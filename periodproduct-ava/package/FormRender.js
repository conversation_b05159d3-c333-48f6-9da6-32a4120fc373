import {isDraft} from "../../pluginbase-ava/package/pluginutils";
import {MdBatchAdd} from "./MdBatchAdd";
import {setFieldsStatus} from "./utils";

export class FormRender {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.mdBatchAdd = new MdBatchAdd(context);
    }

    async formRenderEnd(pluginExecResult, options) {
        let {dataGetter, dataUpdater, formApis} = options;
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let objectDataList = dataGetter.getDetail(objApiName);
        if (isDraft(sourceAction)) {//草稿，周期性相关字段取最新的
            let {
                product_id, whole_period_sale, service_start_time, service_end_time,
                pricing_period, pricing_rate, pricing_mode, pricing_cycle,
                settlement_period, settlement_rate, settlement_mode, settlement_cycle
            } = detailFieldMapping;
            let clearPeriodProductFields = {
                [whole_period_sale]: undefined,
                [service_start_time]: undefined,
                [service_end_time]: undefined,
                [pricing_period]: undefined,
                [pricing_rate]: undefined,
                [pricing_mode]: undefined,
                [pricing_cycle]: undefined,
                [settlement_period]: undefined,
                [settlement_rate]: undefined,
                [settlement_mode]: undefined,
                [settlement_cycle]: undefined
            }
            let modifiedDataIndexs = [];
            objectDataList && objectDataList.length && objectDataList.forEach(it => {
                let dataIndex = it.dataIndex;
                let defaultData = dataGetter.getDetailDefaultData(objApiName, it.record_type);
                let updateData = {};
                Object.keys(clearPeriodProductFields).forEach(key => {
                    updateData[key] = defaultData[key];
                });
                dataUpdater.updateDetail(objApiName, dataIndex, updateData);
                modifiedDataIndexs.push(dataIndex);
            })
            if (modifiedDataIndexs && modifiedDataIndexs.length) {
                let keys = Object.keys(clearPeriodProductFields);
                await formApis.triggerCalAndUIEvent({
                    objApiName: objApiName,
                    modifiedDataIndexs,
                    changeFields: [product_id],
                    extraFields: {[objApiName]: keys}
                });
                let objectDataList = dataGetter.getDetail(objApiName);//取最新的数据
                objectDataList && objectDataList.length && objectDataList.forEach(it => {
                    let {[pricing_mode]: pricingMode, dataIndex} = it;
                    if (pricingMode !== 'cycle') {//一次性产品
                        dataUpdater.updateDetail(objApiName, dataIndex, {
                            [service_start_time]: null,//强制清空
                            [service_end_time]: null,//强制清空
                            [pricing_period]: 1,//强制为1
                            [settlement_period]: 1//强制为1
                        });
                    }
                })
                await this.mdBatchAdd.periodicProductCalculate(objectDataList, null, options);
            }
        }
        let finalObjectDataList = dataGetter.getDetail(objApiName);//取最新的数据
        setFieldsStatus(finalObjectDataList, Object.assign({}, options, {objApiName}), detailFieldMapping, this.pluginApi);
    }
}