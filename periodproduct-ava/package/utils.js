import {isEmpty} from "../../pluginbase-ava/package/pluginutils";

export const KEY_ORIGIN_PRICE_PER_SET = 'key_origin_price_per_set';
export const KEY_ORIGIN_PRICING_PERIOD = 'key_origin_pricing_period';

export function setFieldsStatus(objectDataList, options, detailFieldMapping, pluginApi) {
    if (!objectDataList || !objectDataList.length || isEmpty(options) || isEmpty(detailFieldMapping)) {
        return;
    }
    let {dataUpdater, objApiName} = options;
    let {
        pricing_mode, settlement_amount_per_period, pricing_period, service_start_time, service_end_time,
        settlement_period, settlement_rate, settlement_mode, settlement_cycle
    } = detailFieldMapping;
    objectDataList && objectDataList.length && objectDataList.forEach(objectData => {
        let {[pricing_mode]: pricingModel, dataIndex} = objectData;
        let isPeriodProduct = pricingModel === 'cycle';
        if (isPeriodProduct) {//周期性产品每期标准结算金额设置为必填
            let isSubProduct;
            let result = pluginApi && pluginApi.runPluginSync("bom.isSubProduct.sync", Object.assign({}, options, {objectData}));
            if (typeof result == 'boolean') {
                isSubProduct = result;
            }
            if (!isSubProduct) {
                dataUpdater && dataUpdater.setRequired && dataUpdater.setRequired({
                    objApiName,
                    dataIndex: dataIndex,
                    fieldName: [settlement_amount_per_period],
                    status: true,
                    biz: 'period_product',
                    priority: 11
                });
            }
        } else {//一次性产品相关字段只读
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex,
                fieldName: [pricing_period, service_start_time, service_end_time, settlement_period, settlement_rate, settlement_mode, settlement_cycle, settlement_amount_per_period],
                status: true,
                biz: 'period_product',
                priority: 11
            });
        }
    });
}

export function getObjectField(calculateField, detailFieldMapping) {
    if (isEmpty(calculateField) || isEmpty(detailFieldMapping)) {
        return;
    }
    let {service_start_time, service_end_time, pricing_period, settlement_period} = detailFieldMapping;
    return {
        serviceStartTime: service_start_time,
        serviceEndTime: service_end_time,
        pricingPeriod: pricing_period,
        settlementPeriod: settlement_period
    }[calculateField];
}

export function getCalculateField(fieldApiName, detailFieldMapping) {
    if (isEmpty(fieldApiName) || isEmpty(detailFieldMapping)) {
        return;
    }
    let {service_start_time, service_end_time, pricing_period, settlement_period} = detailFieldMapping;
    return {
        [service_start_time]: 'serviceStartTime',
        [service_end_time]: 'serviceEndTime',
        [pricing_period]: 'pricingPeriod',
        [settlement_period]: 'settlementPeriod',
    }[fieldApiName];
}

export function findCalculateField(objectData, detailFieldMapping) {
    if (isEmpty(objectData) || isEmpty(detailFieldMapping)) {
        return;
    }
    let {service_start_time, service_end_time, pricing_period} = detailFieldMapping;
    let {[service_start_time]: startTime, [service_end_time]: endTime, [pricing_period]: pricingPeriod} = objectData;
    let checkData = {serviceStartTime: startTime, serviceEndTime: endTime, pricingPeriod: pricingPeriod}
    let emptyFields = Object.values(checkData).filter(it => {
        return isEmpty(it);
    })
    if (emptyFields.length === 0) {//所有字段都有值，则计算期数
        return 'pricingPeriod';
    } else if (emptyFields.length === 1) {//只有一个字段无值，则计算无值的字段
        return Object.keys(checkData).find(it => {
            return isEmpty(checkData[it]);
        });
    } else {//否则不计算任何字段

    }
}

export function newCalculateArg(objectData, detailFieldMapping, calculateField, pricingPeriodDecimalPlaces, settlementPeriodDecimalPlaces) {
    if (isEmpty(objectData) || isEmpty(detailFieldMapping)) {
        return;
    }
    let {
        pricing_mode, service_start_time, service_end_time, pricing_period, product_id, pricing_cycle, pricing_rate,
        whole_period_sale, settlement_mode, settlement_cycle, settlement_rate
    } = detailFieldMapping;
    let {
        dataIndex, [pricing_mode]: pricingModel, [service_start_time]: serviceStartTime, [service_end_time]: serviceEndTime,
        [pricing_period]: pricingPeriod, [product_id]: productId, [`${product_id}__r`]: productName, [pricing_cycle]: pricingCycle,
        [pricing_rate]: pricingRate, [whole_period_sale]: wholePeriodSale, [settlement_mode]: settlementMode,
        [settlement_cycle]: settlementCycle, [settlement_rate]: settlementRate
    } = objectData;
    return {
        rowId: dataIndex,
        productId,
        productName,
        pricingModel,
        pricingCycle,
        pricingRate,
        wholePeriodSale,
        pricingPeriod,
        pricingPeriodDecimalPlaces,
        serviceStartTime,
        serviceEndTime,
        settlementMode,
        settlementCycle,
        settlementRate,
        calculateField,
        settlementPeriodDecimalPlaces
    }
}

export function periodicProductCalculate(objectDataList, calculateField, calculateFunction, options) {
    let {objectDescribe, detailFieldMapping} = options || {};
    if (!objectDataList || !objectDataList.length || isEmpty(detailFieldMapping)) {
        return Promise.resolve();
    }
    let params = [];
    let {pricing_mode, pricing_period, settlement_period} = detailFieldMapping;
    let pricingPeriodDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[pricing_period];
    let pricingPeriodDecimalPlaces = pricingPeriodDesc && pricingPeriodDesc.decimal_places || 0;
    let settlementPeriodDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[settlement_period];
    let settlementPeriodDecimalPlaces = settlementPeriodDesc && settlementPeriodDesc.decimal_places || 0;
    objectDataList.forEach(it => {
        if (it[pricing_mode] === 'cycle') {
            if (!calculateField) {
                calculateField = findCalculateField(it, detailFieldMapping);
            }
            if (calculateField) {
                params.push(newCalculateArg(it, detailFieldMapping, calculateField, pricingPeriodDecimalPlaces, settlementPeriodDecimalPlaces))
            }
        }
    });
    if (!params || !params.length) {
        return Promise.resolve();
    }
    return calculateFunction({dataList: params}).then(result => {
        let updateDataResult = {};
        result && result.dataList && result.dataList.forEach(it => {
            let {rowId, calculateField, settlementPeriod} = it;
            let calculateObjField = getObjectField(calculateField, detailFieldMapping);
            let calculateValue = it[calculateField];
            Object.assign(updateDataResult, {
                [rowId]: {
                    [calculateObjField]: calculateValue,
                    [settlement_period]: settlementPeriod
                }
            })
        });
        return updateDataResult;
    });
}