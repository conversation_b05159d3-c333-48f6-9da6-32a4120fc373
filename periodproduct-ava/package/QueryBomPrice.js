import {isEmpty} from '../../pluginbase-ava/package/pluginutils';
import {MdBatchAdd} from "./MdBatchAdd";
import {KEY_ORIGIN_PRICE_PER_SET, KEY_ORIGIN_PRICING_PERIOD, setFieldsStatus} from "./utils";

export class QueryBomPrice {

    constructor(context) {
        let {fieldMapping, pluginApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.mdBatchAdd = new MdBatchAdd(context);
    }

    bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objApiName, objectData} = options;
        if (!isEmpty(objectData)) {
            let {pricing_period, price_per_set, pricing_mode} = this.fieldMapping.getDetailFields(objApiName);
            let {[pricing_period]: pricingPeriod, [price_per_set]: pricePerSet, [pricing_mode]: pricingMode} = objectData;
            return Object.assign({}, preData, {
                pricingPeriod,//期数
                pricePerSet,//单套价格
                pricingMode//定价模式
            });
        }
    }

    bomParseBomData2DetailDataSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objApiName, subProduct} = options;
        if (!isEmpty(subProduct)) {
            let {price_per_set, pricing_period} = this.fieldMapping.getDetailFields(objApiName);
            let {price_per_set: pricePerSet, pricing_period: pricingPeriod} = subProduct;
            return Object.assign({}, preData,
                (!isEmpty(pricePerSet)) && {
                    [price_per_set]: pricePerSet,
                    [KEY_ORIGIN_PRICE_PER_SET]: pricePerSet
                },
                (!isEmpty(pricingPeriod)) && {
                    [pricing_period]: pricingPeriod,
                    [KEY_ORIGIN_PRICING_PERIOD]: pricingPeriod
                });
        }
    }

    bomParseDetailData2BomDataSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objectData} = options;
        let {pricing_period, price_per_set} = this.fieldMapping.getDetailFields(objectData.object_describe_api_name);
        let {[pricing_period]: pricingPeriod, [price_per_set]: pricePerSet} = objectData || {};
        return Object.assign({}, preData, {price_per_set: pricePerSet, pricing_period: pricingPeriod});
    }

    quoterParseQueryBomPriceParamsBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {advancePricingResultFields, objApiName} = options;
        let {price_per_set} = this.fieldMapping.getDetailFields(objApiName);
        let advancePricingPeriodFlag = advancePricingResultFields && advancePricingResultFields.includes(price_per_set);
        return Object.assign({}, preData, {advancePricingPeriodFlag});//高级公式修改单套价格
    }

    async bomQueryBomPriceEnd(pluginExecResult, options) {
        let {triggerType, dataIndex, objApiName} = options;
        if (triggerType === 'reconfiguration') {//bom二次配置后，计算子件的服务结束时间
            let subLinesList = this.pluginApi.runPluginSync('bom.getSubDetailsFromPkg.sync', Object.assign({}, options, {
                objApiName,
                dataIndex
            }));
            if (subLinesList && subLinesList.length) {//二次配置结束后，计算子件的服务结束时间
                let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
                setFieldsStatus(subLinesList, options, detailFieldMapping, this.pluginApi);
                await this.mdBatchAdd.periodicProductCalculate(subLinesList, 'serviceEndTime', options);
            }
        }
    }

    bomUpdateReadonlyFieldsBeforeSync(pluginExecResult, options) {
        let {objectData, readonlyFields} = options || {};
        if (isEmpty(objectData) || !readonlyFields) {
            return;
        }
        let isSubProduct;
        let result = this.pluginApi && this.pluginApi.runPluginSync("bom.isSubProduct.sync", Object.assign({}, options, {objectData}));
        if (typeof result == 'boolean') {
            isSubProduct = result;
        }
        if (isSubProduct) {//子产品
            let {product_price, pricing_period, pricing_mode, service_start_time, service_end_time, settlement_amount_per_period} = this.fieldMapping.getDetailFields(objectData.object_describe_api_name);
            let children = this.pluginApi && this.pluginApi.runPluginSync("bom.getChildren.sync", Object.assign({}, options, {objectData}));
            let hasSubLines = children && children.length;//是否有子件
            let isPricingPeriodProduct = objectData[pricing_mode] === 'cycle';//是否是周期性产品
            if (hasSubLines && isPricingPeriodProduct) {//有子件且是周期性产品，价格只读
                readonlyFields.push(product_price);
            }
            if (isPricingPeriodProduct) {//周期性子件，期数、服务开始时间、服务结束时间、 每期标准结算金额支持编辑
                let editableFields = [pricing_period, service_start_time, service_end_time, settlement_amount_per_period];
                let i = readonlyFields.length;
                while (i--) {
                    let field = readonlyFields[i];
                    if (editableFields.includes(field)) {
                        readonlyFields.splice(i, 1);
                    }
                }
            }
        }
    }

    bomCalcSubProductBeforeSync(pluginExecResult, options) {
        let {calcParam, objApiName, triggerType} = options;
        if (!objApiName) {
            objApiName = this.fieldMapping.getFirstDetailObjApiName();
        }
        let {
            whole_period_sale,
            service_start_time,
            service_end_time,
            pricing_period,
            pricing_rate,
            pricing_mode,
            pricing_cycle,
            settlement_period,
            settlement_rate,
            settlement_mode,
            settlement_cycle,
            price_per_set
        } = this.fieldMapping.getDetailFields(objApiName);
        let {changeFields, filterFields, extraFields} = calcParam || {};
        changeFields && (changeFields.push(pricing_period, price_per_set));
        if (triggerType === 'copy_mapping') {//复制映射场景，子件的期数，服务开始时间等字段保持原单据的值
            let _filterFields = [whole_period_sale, service_start_time, service_end_time, pricing_period, pricing_rate, pricing_mode, pricing_cycle, settlement_period, settlement_rate, settlement_mode, settlement_cycle];
            let objFilterFields = filterFields && filterFields[objApiName];
            if (objFilterFields) {
                objFilterFields.push(..._filterFields);
            }
            let objExtraFields = extraFields && extraFields[objApiName];
            if (objExtraFields && objExtraFields.length) {
                let i = objExtraFields.length;
                while (i--) {
                    let field = objExtraFields[i];
                    if (_filterFields.includes(field)) {
                        objExtraFields.splice(i, 1);
                    }
                }
            }
        }
    }

    bomCalcSubProductEnd(pluginExecResult, options) {
        let {dataUpdater, dataGetter, objApiName, modifiedDataIndexList} = options;
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {pricing_mode, service_start_time, service_end_time, pricing_period, settlement_period} = detailFieldMapping;
        if (modifiedDataIndexList && modifiedDataIndexList.length) {
            let detailDataList = dataGetter.getDetail(objApiName);
            let modifiedDataList = detailDataList && detailDataList.length && detailDataList.filter(it => {
                return modifiedDataIndexList.includes(it.dataIndex)
            })
            modifiedDataList && modifiedDataList.forEach(it => {
                let {[pricing_mode]: pricingMode} = it;
                if (pricingMode !== 'cycle') {//一次性产品
                    dataUpdater.updateDetail(objApiName, it.dataIndex, {
                        [service_start_time]: null,//强制清空
                        [service_end_time]: null,//强制清空
                        [pricing_period]: 1,//强制为1
                        [settlement_period]: 1//强制为1
                    });
                }
            })
        }
    }

    bomQueryBomPriceBefore(pluginExecResult, options) {
        let {triggerType, objApiName, changeData} = options;
        if (['modifyPackage', 'modifySubProduct'].includes(triggerType)) {
            let {pricing_period} = this.fieldMapping.getDetailFields(objApiName);
            let pricingPeriodChanged = !(typeof changeData[pricing_period] === 'undefined');
            if (pricingPeriodChanged) {
                return { forceQueryBomPrice: true, updateField: pricing_period };//期数变了，需要触发bom取价
            }
        }
    }
}