import {isEmpty, uniq, uuid} from "../../pluginbase-ava/package/pluginutils";
import {KEY_ORIGIN_PRICE_PER_SET, KEY_ORIGIN_PRICING_PERIOD, periodicProductCalculate, setFieldsStatus} from "./utils";

export class MdBatchAdd {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    mdBatchAddBefore(pluginExecResult, options) {
        let {objApiName, selectObjectParams, lookupField} = options;
        let fieldName = lookupField && lookupField.api_name;
        let {product_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName) || {};
        if (!([product_id, price_book_product_id].includes(fieldName))) {
            return;
        }
        let formObjectData = Object.assign({}, selectObjectParams.formObjectData, {
            selectSKUConfig: Object.assign({}, selectObjectParams.formObjectData && selectObjectParams.formObjectData.selectSKUConfig, {
                selectObjectType: 'sku',
                enablePeriodicProductPlugin: true,//启用了周期性产品插件
            })
        });
        Object.assign(selectObjectParams, {
            formObjectData,
            disableAdd: true,
            includeAssociated: true,
            useWx: true,
        });
    }

    mdBatchAddAfter(pluginExecResult, options) {
        let isOpenCPQ = this.bizStateConfig.isOpenCpq();
        let isOpenSimpleCPQ = this.bizStateConfig.isOpenSimpleCpq();
        if (isOpenCPQ || isOpenSimpleCPQ) {//启用了cpq插件，期数、单套价格字段不参与paas层计算，否则会覆盖配置页输入的期数
            //1、业务通过计算接口计算出值

            //2、回填期数、单套价格字段
            //3、paas层计算不参与计算
            // let {objApiName} = options || {};
            // let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
            // let {pricing_period, price_per_set} = fieldMapping;
            // let preData = pluginExecResult && pluginExecResult.preData;
            // let preObjectFilterFields = preData
            //     && preData.extraCalUiParams
            //     && preData.extraCalUiParams.filterFields
            //     && preData.extraCalUiParams.filterFields[objApiName];
            // return Object.assign({}, preData, {
            //     extraCalUiParams: Object.assign({}, preData && preData.extraCalUiParams, {
            //         filterFields: Object.assign({}, preData && preData.extraCalUiParams && preData.extraCalUiParams.filterFields,
            //             {[objApiName]: [...(preObjectFilterFields || []), pricing_period, price_per_set]}),
            //     })
            // })
        }
    }

    async mdBatchAddEnd(pluginExecResult, options) {
        let {lookupField, newDatas, objApiName, dataGetter, dataUpdater, formApis} = options || {};
        let fieldName = lookupField && lookupField.api_name;
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_product_id, price_per_set, pricing_period, pricing_mode, service_start_time, service_end_time, settlement_period} = detailFieldMapping;
        if (!newDatas || !newDatas.length || (![product_id, price_book_product_id].includes(fieldName))) {
            return;
        }
        setFieldsStatus(newDatas, options, detailFieldMapping, this.pluginApi);
        let modifiedIndexList = [];
        newDatas.forEach(it => {
            let updateData = {};
            let {[pricing_mode]: pricingMode} = it;
            if (pricingMode !== 'cycle') {//一次性产品
                Object.assign(updateData, {
                    [service_start_time]: null,//强制清空
                    [service_end_time]: null,//强制清空
                    [pricing_period]: 1,//强制为1
                    [settlement_period]: 1//强制为1
                })
            }
            let {[KEY_ORIGIN_PRICE_PER_SET]: originPricePerSet, [KEY_ORIGIN_PRICING_PERIOD]: originPricingPeriod, dataIndex} = it;
            if (!isEmpty(originPricePerSet)) {
                Object.assign(updateData, {[price_per_set]: originPricePerSet, [KEY_ORIGIN_PRICE_PER_SET]: null});
            }
            if (!isEmpty(originPricingPeriod)) {
                Object.assign(updateData, {[pricing_period]: originPricingPeriod, [KEY_ORIGIN_PRICING_PERIOD]: null});
            }
            if (!isEmpty(updateData)) {
                dataUpdater.updateDetail(objApiName, dataIndex, updateData);
                modifiedIndexList.push(dataIndex);
            }
        })
        if (modifiedIndexList && modifiedIndexList.length) {
            let filterCalcFields = {[objApiName]: [price_per_set]};
            await formApis.triggerCalAndUIEvent({
                objApiName: objApiName,
                modifiedDataIndexs: modifiedIndexList,
                changeFields: [price_per_set, pricing_period],
                filterFields: filterCalcFields,
            });
        }
        let newDataIndexList = newDatas.map(it => it.dataIndex);
        let detailDataList = dataGetter.getDetail(objApiName) || [];
        let objectDataList = detailDataList && detailDataList.filter(it => newDataIndexList.includes(it.dataIndex));
        await this.periodicProductCalculate(objectDataList, null, options);
    }

    async mdCloneEnd(pluginExecResult, options) {
        let {newDatas, objApiName} = options;
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        setFieldsStatus(newDatas, options, detailFieldMapping, this.pluginApi);
    }

    async periodicProductCalculate(objectDataList, calculateField, options) {
        let self = this;
        let {dataGetter, dataUpdater, objApiName, formApis, doNotUpdateData = false} = options || {};
        if (!objApiName) {
            objApiName = this.fieldMapping.getFirstDetailObjApiName();
        }
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let pageId = dataGetter.getPageId();
        let token = 'periodic_product_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let updateDataResult = await periodicProductCalculate(objectDataList, calculateField, async (params) => {
            return self.requestApi.calculate(params);
        }, {
            objectDescribe,
            detailFieldMapping
        }).then(result => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            return result;
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        })
        if (isEmpty(updateDataResult)) {
            return;
        }
        if (doNotUpdateData) {
            return updateDataResult;
        }
        let modifiedDataIndexs = [];
        let changeFields = [];
        Object.keys(updateDataResult).forEach(key => {
            let value = updateDataResult[key];
            if (!isEmpty(value)) {
                dataUpdater.updateDetail(objApiName, key, value);
                modifiedDataIndexs.push(key);
                changeFields.push(...Object.keys(value));
            }
        });
        await formApis.triggerCalAndUIEvent({
            objApiName: objApiName,
            modifiedDataIndexs,
            changeFields: uniq(changeFields),
        });
        return updateDataResult;
    }
}