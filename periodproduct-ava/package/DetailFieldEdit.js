import {i18n, isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";
import {getCalculateField, periodicProductCalculate} from "./utils";

export class DetailFieldEdit {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context;
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async fieldEditAfter(pluginExecResult, options) {
        let {objApiName, fieldName, changeData, dataIndex, dataGetter} = options;
        let fieldValue = changeData[fieldName];
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {pricing_period, service_start_time, service_end_time, pricing_mode, settlement_period, whole_period_sale, settlement_cycle, settlement_rate} = detailFieldMapping;
        if (![pricing_period, service_start_time, service_end_time, settlement_cycle, settlement_rate].includes(fieldName)) {
            return;
        }
        let objectData = dataGetter.getData(objApiName, dataIndex);
        let {
            [service_start_time]: startTime, [service_end_time]: endTime, [pricing_period]: pricingPeriod, [pricing_mode]: pricingModel,
            [whole_period_sale]: wholePeriodSale, [settlement_cycle]: settlementCycle, [settlement_rate]: settlementRate
        } = objectData;
        if ((pricingModel !== 'cycle') || (typeof fieldValue === 'undefined')) {
            return;
        }
        if (isEmpty(fieldValue)) {//修改的字段值为空，清空结算期数
            Object.assign(changeData, {[settlement_period]: null});
            return;
        }
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let wholePeriodSaleFieldDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[whole_period_sale];
        let serviceStartTimeFieldDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[service_start_time];
        let serviceEndTimeFieldDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[service_end_time];
        if (fieldName === pricing_period) {//修改期数
            if (wholePeriodSale === 'yes') {//整期售卖期数不允许为小数
                let values = fieldValue.split('.');
                if ((values.length > 1) && (values[1] != 0)) {//小数为0不校验
                    let lastValue = pricingPeriod;
                    Object.assign(changeData, {[pricing_period]: isEmpty(lastValue) ? null : lastValue});
                    let label = wholePeriodSaleFieldDesc && wholePeriodSaleFieldDesc.label || '';
                    this.pluginApi.showToast(i18n('ava.object_form.periodproduct.must_integer_tip', [label])/*产品的{0}属性为是，请输入整数*/);
                    return;
                }
            }
        }
        if (fieldName === service_end_time) {//修改服务结束时间
            if ((!isEmpty(startTime)) && (fieldValue < startTime)) {//服务结束时间>=服务开始时间
                delete changeData[fieldName];
                let label1 = serviceStartTimeFieldDesc && serviceStartTimeFieldDesc.label || '';
                let label2 = serviceEndTimeFieldDesc && serviceEndTimeFieldDesc.label || '';
                this.pluginApi.showToast(i18n('ava.object_form.periodproduct.cannot_earlier_tip', [label2, label1])/*{0}不能早于{1}*/);
                return;
            }
        }

        let calculateFieldName;
        if (fieldName === pricing_period) {//修改期数
            if (!isEmpty(startTime) && !isEmpty(endTime)) {//开始、结束时间都有值，计算结束时间
                calculateFieldName = service_end_time
            } else if (!isEmpty(endTime)) {//结束时间有值，计算开始时间
                calculateFieldName = service_start_time
            } else if (!isEmpty(startTime)) {//开始时间有值，计算结束时间
                calculateFieldName = service_end_time
            }
        } else if (fieldName === service_start_time) {//修改开始时间
            if (!isEmpty(endTime) && !isEmpty(pricingPeriod)) {//期数、结束时间都有值，计算结束时间
                calculateFieldName = service_end_time;
            } else if (!isEmpty(pricingPeriod)) {//期数有值，计算结束时间
                calculateFieldName = service_end_time;
            } else if (!isEmpty(endTime)) {//结算时间有值，计算期数
                calculateFieldName = pricing_period;
            }
        } else if (fieldName === service_end_time) {//修改结束时间
            if (!isEmpty(startTime) && !isEmpty(pricingPeriod)) {//期数、开始时间都有值，计算期数
                calculateFieldName = pricing_period
            } else if (!isEmpty(pricingPeriod)) {//期数有值，计算开始时间
                calculateFieldName = service_start_time
            } else if (!isEmpty(startTime)) {//开始时间有值，计算期数
                calculateFieldName = pricing_period
            }
        } else if (fieldName === settlement_cycle) {
            if (!isEmpty(settlementRate)) {
                calculateFieldName = settlement_period;
            }
        } else if (fieldName === settlement_rate) {
            if (!isEmpty(settlementCycle)) {
                calculateFieldName = settlement_period;
            }
        }
        let calculateField = getCalculateField(calculateFieldName, detailFieldMapping);
        if (!calculateField) {
            return;
        }
        let self = this;
        let pageId = dataGetter.getPageId();
        let token = 'periodic_product_' + uuid();
        let tempObjectData = Object.assign({}, objectData, changeData);
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let updateDataResult = await periodicProductCalculate([tempObjectData], calculateField, async (params) => {
            return self.requestApi.calculate(params);
        }, {
            objectDescribe,
            detailFieldMapping
        }).then(result => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            return result;
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        });
        if (isEmpty(updateDataResult)) {
            delete changeData[fieldName];
        } else {
            Object.assign(changeData, updateDataResult[dataIndex])
        }
    }

    async fieldEditEnd(pluginExecResult, options) {
        let {objApiName, changeData} = options;
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {pricing_period} = detailFieldMapping;
        if (!(typeof changeData[pricing_period] === 'undefined')) {//如果期数有变化，发出期数变更事件
            await this.pluginApi.runPlugin('period-product.pricing_period.change.end', Object.assign({}, options));
        }
    }
}