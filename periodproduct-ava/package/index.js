import log from "../../pluginbase-ava/package/log";
import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PeriodProductApi from "./PeriodProductApi";
import {FormRender} from './FormRender';
import {MdBatchAdd} from './MdBatchAdd';
import {DetailFieldEdit} from './DetailFieldEdit';
import {QueryBomPrice} from './QueryBomPrice';

/**
 * 该插件在bom插件前
 * 技术文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
 */
export default class PeriodProduct {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, params, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        let context = {
            fieldMapping: new FieldMapping(params, {
                masterFields: {
                    form_account_id: 'form_account_id',// 客户id
                },
                detailFields: {
                    product_price: 'product_price',// 价格
                    price_per_set: "price_per_set",//单套价格
                    subtotal: "subtotal",//小计
                    sales_price: "sales_price",//销售单价
                    product_id: "product_id",//产品id
                    price_book_product_id: "price_book_product_id",

                    whole_period_sale: "whole_period_sale__c",//整期售卖
                    service_start_time: "service_start_time",//服务开始日期
                    service_end_time: "service_end_time",//服务结束日期

                    pricing_period: "pricing_period",//期数
                    pricing_rate: "pricing_rate",//定价频率
                    pricing_mode: "pricing_mode",//定价模式
                    pricing_cycle: "pricing_cycle",//定价周期

                    settlement_period: "settlement_period",//结算期数
                    settlement_rate: "settlement_rate",//结算频率
                    settlement_mode: "settlement_mode",//结算模式
                    settlement_cycle: "settlement_cycle",//结算周期
                    settlement_amount_per_period: "settlement_amount_per_period",//每期标准结算金额
                }
            }),
            bizStateConfig: new BizStateConfig(bizStateConfig, pluginService.api.getPlugins()),
            pluginApi: new PluginApi(pluginService),
            requestApi: new PeriodProductApi(pluginService.api.request)
        }
        this.formRender = new FormRender(context);
        this.mdBatchAdd = new MdBatchAdd(context);
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.queryBomPrice = new QueryBomPrice(context);
    }

    formRenderEnd(pluginExecResult, options) {
        return this.formRender.formRenderEnd(pluginExecResult, options);
    }

    mdBatchAddBefore(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddBefore(pluginExecResult, options);
    }

    mdBatchAddAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddAfter(pluginExecResult, options);
    }

    mdBatchAddEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddEnd(pluginExecResult, options);
    }

    mdCloneEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneEnd(pluginExecResult, options);
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldEditEnd(pluginExecResult, options);
        }
    }

    bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options) {
        return this.queryBomPrice.bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options);
    }

    bomParseBomData2DetailDataSync(pluginExecResult, options) {
        return this.queryBomPrice.bomParseBomData2DetailDataSync(pluginExecResult, options);
    }

    bomParseDetailData2BomDataSync(pluginExecResult, options) {
        return this.queryBomPrice.bomParseDetailData2BomDataSync(pluginExecResult, options);
    }

    bomQueryBomPriceEnd(pluginExecResult, options) {
        return this.queryBomPrice.bomQueryBomPriceEnd(pluginExecResult, options);
    }

    bomUpdateReadonlyFieldsBeforeSync(pluginExecResult, options) {
        return this.queryBomPrice.bomUpdateReadonlyFieldsBeforeSync(pluginExecResult, options);
    }

    bomCalcSubProductBeforeSync(pluginExecResult, options) {
        return this.queryBomPrice.bomCalcSubProductBeforeSync(pluginExecResult, options);
    }

    quoterParseQueryBomPriceParamsBeforeSync(pluginExecResult, options) {
        return this.queryBomPrice.quoterParseQueryBomPriceParamsBeforeSync(pluginExecResult, options);
    }

    periodProductTriggerBatchCalc(pluginExecResult, options) {
        let {objectDataList, calculateField, doNotUpdateData = false} = options;
        return this.mdBatchAdd.periodicProductCalculate(objectDataList, calculateField, options);
    }

    bomCalcSubProductEnd(pluginExecResult, options) {
        return this.queryBomPrice.bomCalcSubProductEnd(pluginExecResult, options);
    }

    bomQueryBomPriceBefore(pluginExecResult, options) {
        return this.queryBomPrice.bomQueryBomPriceBefore(pluginExecResult, options);
    }

    apply() {
        return [{
            event: "form.render.end",
            functional: this.formRenderEnd.bind(this)
        }, {
            event: "md.batchAdd.before",
            functional: this.mdBatchAddBefore.bind(this)
        }, {
            event: "md.batchAdd.after",
            functional: this.mdBatchAddAfter.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.mdBatchAddEnd.bind(this)
        }, {
            event: "md.clone.end",
            functional: this.mdCloneEnd.bind(this)
        }, {
            event: "field.edit.after",
            functional: this.fieldEditAfter.bind(this)
        }, {
            event: "field.edit.end",
            functional: this.fieldEditEnd.bind(this)
        }, {
            event: "bom.queryBomPrice.parseParams.before",
            functional: this.bomQueryBomPriceParseParamsBeforeSync.bind(this)
        }, {
            event: "bom.parseBomData2DetailData.sync",
            functional: this.bomParseBomData2DetailDataSync.bind(this)
        }, {
            event: "bom.parseDetailData2BomData.sync",
            functional: this.bomParseDetailData2BomDataSync.bind(this)
        }, {
            event: "bom.queryBomPrice.end",
            functional: this.bomQueryBomPriceEnd.bind(this)
        }, {
            event: "bom.updateReadonlyFields.before.sync",
            functional: this.bomUpdateReadonlyFieldsBeforeSync.bind(this)
        }, {
            event: "bom.calcSubProduct.before.sync",
            functional: this.bomCalcSubProductBeforeSync.bind(this)
        }, {
            event: "quoter.parseQueryBomPriceParams.before.sync",
            functional: this.quoterParseQueryBomPriceParamsBeforeSync.bind(this)
        }, {
            event: "period-product.triggerBatchCalc",
            functional: this.periodProductTriggerBatchCalc.bind(this)
        }, {
            event: "bom.calcSubProduct.end",
            functional: this.bomCalcSubProductEnd.bind(this)
        }, {
            event: 'bom.queryBomPrice.before',
            functional: this.bomQueryBomPriceBefore.bind(this)
        }];
    }
}