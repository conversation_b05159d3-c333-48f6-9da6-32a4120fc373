## 甘特图

### 安装插件

执行以下命令：
```
npm install crm-biz-gantt
```

### 在项目中引入插件

```javascript
define(function (require, exports, module) {
    var util = CRM.util;
    return function (wrapper, tasks, options) {
        return new Promise((resolve, reject) => {
            require.async('vcrm/plugin/gantt', (Resource) => {
                let resource = new Resource(wrapper, tasks, options);
                resolve(resource);
            })
        })
    };
});

```
- **wrapper**：`document.getElementById('gantt-chart-wrapper')`；这是容纳插件的DOM元素。
- **tasks**：`[...]`；任务数据数组。
- **options**：
  - `taskList`：`[...]`；任务数据集合。
  - `stageList`：`[...]`；所有阶段数据集合。
  - `projectTask`：`{}`；当前对象上下文。

### 任务、阶段数据格式参考
```javascript
{
    // 以下字段是必填的
    "id": "66987fd890fa560007c2dafb",
    "name": "工时填报阶段",
    "start": "2024-07-01",
    "end": "2024-07-31",
    "progress": 50,  //任务完成度可以为空或者有值
    "biz_status": "unstarted",
    "actual_start_date":  "2024-08-01",
    "actual_end_date": "2024-08-05",
    "tasktype": "stage",
    "_has_actual_bar": false,
    "_is_actual_bar": false
},
```