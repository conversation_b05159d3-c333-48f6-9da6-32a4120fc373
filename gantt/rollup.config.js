import sass from 'rollup-plugin-sass';
import babel from '@rollup/plugin-babel';

export default {
    input: 'src/index.js',
    output: {
        name: 'Gantt',
        file: 'dist/index.js',
        format: 'umd',
        exports: 'auto',  // 修改为 auto，让 rollup 自动处理导出方式
    },
    plugins: [
        babel({
            babelHelpers: 'bundled',  // 显式声明 babelHelpers
            presets: ['@babel/preset-env'],
            exclude: 'node_modules/**'
        }),
        sass({
            insert: true
        })
    ]
}