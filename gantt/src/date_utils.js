const YEAR = "year";
const MONTH = "month";
const DAY = "day";
const HOUR = "hour";
const MINUTE = "minute";
const SECOND = "second";
const MILLISECOND = "millisecond";

const month_names = {
    en: [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ],
    es: [
        "Enero",
        "Febrero",
        "<PERSON>zo",
        "Abril",
        "Mayo",
        "Junio",
        "Julio",
        "Agosto",
        "Septiembre",
        "Octubre",
        "Noviembre",
        "Diciembre",
    ],
    ru: [
        "Январь",
        "Февраль",
        "Март",
        "Апрель",
        "Май",
        "Июнь",
        "Июль",
        "Август",
        "Сентябрь",
        "Октябрь",
        "Ноябрь",
        "Декабрь",
    ],
    ptBr: [
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "A<PERSON><PERSON>",
        "<PERSON>embro",
        "<PERSON><PERSON><PERSON>",
        "Novembro",
        "<PERSON><PERSON>mbro",
    ],
    fr: [
        "<PERSON>vier",
        "F<PERSON>vrier",
        "<PERSON>",
        "Avril",
        "<PERSON>",
        "<PERSON>in",
        "<PERSON>illet",
        "Ao<PERSON>t",
        "Septem<PERSON>",
        "Oct<PERSON>re",
        "<PERSON>em<PERSON>",
        "<PERSON><PERSON><PERSON>mbre",
    ],
    tr: [
        "<PERSON><PERSON>k",
        "<PERSON>ubat",
        "<PERSON>",
        "<PERSON>san",
        "Mayı<PERSON>",
        "<PERSON><PERSON>ran",
        "<PERSON>mmuz",
        "Ağustos",
        "Eylül",
        "Ekim",
        "Kasım",
        "Aralık",
    ],
    zh: [
        $t("el.datepicker.months.jan"),
        $t("el.datepicker.months.feb"),
        $t("el.datepicker.months.mar"),
        $t("el.datepicker.months.apr"),
        $t("el.datepicker.months.may"),
        $t("el.datepicker.months.jun"),
        $t("el.datepicker.months.jul"),
        $t("el.datepicker.months.aug"),
        $t("el.datepicker.months.sep"),
        $t("el.datepicker.months.oct"),
        $t("el.datepicker.months.nov"),
        $t("el.datepicker.months.dec"),
    ],
};

const week_names = {
    en: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
    zh: [
        $t("el.datepicker.weeks.sun"),
        $t("el.datepicker.weeks.mon"),
        $t("el.datepicker.weeks.tue"),
        $t("el.datepicker.weeks.wed"),
        $t("el.datepicker.weeks.thu"),
        $t("el.datepicker.weeks.fri"),
        $t("el.datepicker.weeks.sat"),
    ],
};

export default {
    parse(date, date_separator = "-", time_separator = /[.:]/) {
        if (date instanceof Date) {
            return date;
        }
        if (typeof date === 'string') {
            const [dateParts, timeParts] = date.split(' ');
            const parsedDate = dateParts.split(date_separator).map(val => parseInt(val, 10));
            const parsedTime = timeParts && timeParts.split(time_separator);

            // month is 0 indexed
            parsedDate[1] -= 1;

            if (parsedTime && parsedTime.length) {
                if (parsedTime.length === 4) {
                    parsedTime[3] = '0.' + parsedTime[3];
                    parsedTime[3] = parseFloat(parsedTime[3]) * 1000;
                }
                return new Date(...parsedDate, ...parsedTime);
            }

            return new Date(...parsedDate);
        }
    },

    to_string(date, with_time = false) {
        if (!(date instanceof Date)) {
            throw new TypeError("Invalid argument type");
        }
        const vals = this.get_date_values(date).map((val, i) => {
            if (i === 1) {
                // add 1 for month
                val += 1;
            }

            if (i === 6) {
                return padStart(val + "", 3, "0");
            }

            return padStart(val + "", 2, "0");
        });
        const date_string = `${vals[0]}-${vals[1]}-${vals[2]}`;
        const time_string = `${vals[3]}:${vals[4]}:${vals[5]}.${vals[6]}`;

        return date_string + (with_time ? " " + time_string : "");
    },

    format_week(day, lang = "en") {
        return week_names[lang][day];
    },

    format(date, format_string = "YYYY-MM-DD HH:mm:ss.SSS", lang = "zh") {
        const values = this.get_date_values(date).map((d) => padStart(d, 2, 0));
        const format_map = {
            YYYY: values[0],
            MM: padStart(+values[1] + 1, 2, 0),
            DD: values[2],
            HH: values[3],
            mm: values[4],
            ss: values[5],
            SSS: values[6],
            D: values[2],
            MMMM: month_names[lang][+values[1]],
            MMM: month_names[lang][+values[1]],
        };

        let str = format_string;
        const formatted_values = [];

        Object.keys(format_map)
            .sort((a, b) => b.length - a.length) // big string first
            .forEach((key) => {
                if (str.includes(key)) {
                    str = str.replace(key, `$${formatted_values.length}`);
                    formatted_values.push(format_map[key]);
                }
            });

        formatted_values.forEach((value, i) => {
            str = str.replace(`$${i}`, value);
        });

        return str;
    },

    diff(date_a, date_b, scale = DAY) {
        const milliseconds = date_a - date_b;
        const seconds = milliseconds / 1000;
        const minutes = seconds / 60;
        const hours = minutes / 60;
        const days = hours / 24;
        const months = days / 30;
        const years = months / 12;

        if (!scale.endsWith("s")) {
            scale += "s";
        }

        return Math.floor(
            {
                milliseconds,
                seconds,
                minutes,
                hours,
                days,
                months,
                years,
            }[scale]
        );
    },

    today() {
        const vals = this.get_date_values(new Date()).slice(0, 3);
        return new Date(...vals);
    },

    now() {
        return new Date();
    },

    add(date, qty, scale) {
        qty = parseInt(qty, 10);
        const [year, month, day, hour, minute, second, millisecond] = this.get_date_values(date);
        const updatedValues = [
            year + (scale === YEAR ? qty : 0),
            month + (scale === MONTH ? qty : 0),
            day + (scale === DAY ? qty : 0),
            hour + (scale === HOUR ? qty : 0),
            minute + (scale === MINUTE ? qty : 0),
            second + (scale === SECOND ? qty : 0),
            millisecond + (scale === MILLISECOND ? qty : 0),
        ];
        return new Date(...updatedValues);
    },

    start_of(date, scale) {
        const scores = {
            [YEAR]: 6,
            [MONTH]: 5,
            [DAY]: 4,
            [HOUR]: 3,
            [MINUTE]: 2,
            [SECOND]: 1,
            [MILLISECOND]: 0,
        };

        function should_reset(_scale) {
            return scores[_scale] <= scores[scale];
        }
        const [year, month, day, hour, minute, second, millisecond] = this.get_date_values(date);
        const updatedValues = [
            year,
            should_reset(YEAR) ? 0 : month,
            should_reset(MONTH) ? 1 : day,
            should_reset(DAY) ? 0 : hour,
            should_reset(HOUR) ? 0 : minute,
            should_reset(MINUTE) ? 0 : second,
            should_reset(SECOND) ? 0 : millisecond,
        ];

        return new Date(...updatedValues);
    },

    clone(date) {
        return new Date(...this.get_date_values(date));
    },

    get_date_values(date) {
        return [
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds(),
            date.getMilliseconds(),
        ];
    },

    get_days_in_month(date) {
        const no_of_days = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        const month = date.getMonth();

        if (month !== 1) {
            return no_of_days[month];
        }

        // Feb
        const year = date.getFullYear();
        if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {
            return 29;
        }
        return 28;
    },
};

// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/padStart
function padStart(str, targetLength, padString = ' ') {
    str = String(str);
    targetLength = targetLength >> 0;
    padString = String(padString);

    if (str.length > targetLength) {
        return str;
    }

    targetLength -= str.length;
    if (targetLength > padString.length) {
        padString += padString.repeat(targetLength / padString.length);
    }
    return padString.slice(0, targetLength) + str;
}
