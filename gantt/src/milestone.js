import { createSVG, animateSVG } from './svg_utils';

/**
 * 创建里程碑任务
 * @param {*} curTask 当前任务对象
 * @returns object
 */
export function createMile (curTask) {
    let polygon_left, polygon_right, polygon_none_left, polygon_none_right;
    polygon_left = createSVG('polygon', {
        points: `${curTask.x},${curTask.y + curTask.height / 2} ${curTask.x + curTask.polygon_w / 2},${curTask.y + curTask.height} ${curTask.x + curTask.polygon_w / 2},${curTask.y}`,
        class: `mile-polygon ${curTask.otherBar} ${curTask.milestoneBar}`,
        append_to: curTask.bar_group
    });
    polygon_right = createSVG('polygon', {
        points: `${curTask.x + curTask.width},${curTask.y + curTask.height / 2} ${curTask.x + curTask.width - curTask.polygon_w / 2},${curTask.y} ${curTask.x + curTask.width - curTask.polygon_w / 2},${curTask.y + curTask.height}`,
        class: `mile-polygon ${curTask.otherBar} ${curTask.milestoneBar}`,
        append_to: curTask.bar_group
    });
    if (!curTask.task._is_actual_bar) {
        polygon_none_left = createSVG('polygon', {
            points: `${curTask.x + curTask.polygon_w / 2},${curTask.y + curTask.height} ${curTask.x + curTask.polygon_w / 2},${curTask.y}`,
            class: 'mile-none-polygon left',
            append_to: curTask.bar_group
        });
        polygon_none_right = createSVG('polygon', {
            points: `${curTask.x + curTask.width - curTask.polygon_w / 2},${curTask.y} ${curTask.x + curTask.width - curTask.polygon_w / 2},${curTask.y + curTask.height}`,
            class: 'mile-none-polygon right',
            append_to: curTask.bar_group
        });
    };
    return {
        polygon_left,
        polygon_right,
        polygon_none_left,
        polygon_none_right
    }
}

/**
 * 绘制实际任务
 * @param {*} curTask 当前任务对象
 */0
export function draw_mile_progress_bar (curTask) {
    if (curTask.invalid || curTask.task._has_actual_bar) return;
    curTask.mile_group = createSVG('g', {
        class: 'mile-progress-group',
        append_to: curTask.bar_group
    });
    const width = curTask.$bar.getWidth() + curTask.polygon_w;
    const percent = width * (curTask.task.progress / 100) || 0;
    const { bar_width, poly_points_l, poly_points_r } = compute_position(curTask, width, percent);
    curTask.$bar_progress = createSVG('rect', {
        x: curTask.$bar.getX(),
        y: curTask.y,
        width: bar_width,
        height: curTask.height,
        class: 'mile-polygon-progress',
        append_to: curTask.mile_group
    });
    curTask.$polygon_progress_left = createSVG('polygon', {
        points: poly_points_l,
        class: 'mile-polygon-progress',
        append_to: curTask.mile_group
    });
    if (!poly_points_r) return;
    curTask.$polygon_progress_right = createSVG('polygon', {
        points: poly_points_r,
        class: 'mile-polygon-progress',
        append_to: curTask.mile_group
    });
    animateSVG(curTask.$bar_progress, 'width', 0, curTask.progress_width);
}

/**
 * 不同进度坐标计算
 * @param {*} curTask 当前任务对象
 * @param {*} allWidth 总任务条宽度
 * @param {*} percent 完成度所占比例
 * @returns object
 */
function compute_position (curTask, allWidth, percent) {
    let width = 0;
    const bar_w = curTask.$bar.getWidth() + curTask.polygon_w / 2;
    const polygon_x = curTask.$bar.getX() - curTask.polygon_w / 2;
    let points_l = `${polygon_x},${curTask.y + curTask.height / 2} ${curTask.$bar.getX()},${curTask.y + curTask.height} ${curTask.$bar.getX()},${curTask.y}`;
    let points_r = `${polygon_x + curTask.width},${curTask.y + curTask.height / 2} ${polygon_x + curTask.width - curTask.polygon_w / 2},${curTask.y} ${polygon_x + curTask.width - curTask.polygon_w / 2},${curTask.y + curTask.height}`;
    if (percent <= curTask.polygon_w / 2) {
        //左侧区域内
        width = 0;
        const disec = (curTask.gantt.options.bar_height * percent) / (curTask.polygon_w / 2);
        const y = (curTask.gantt.options.bar_height - disec) / 2;
        points_l = `${polygon_x},${curTask.y + curTask.height / 2} ${polygon_x + percent},${curTask.y + y + disec} ${polygon_x + percent},${curTask.y + y}`;
        points_r = null;
    } else if (percent > curTask.polygon_w / 2 && percent <= bar_w) {
        //矩形区域内
        width = percent - curTask.polygon_w / 2;
        points_r = null;
    } else if (percent > bar_w && percent <= allWidth) {
        //右侧区域内
        width = curTask.$bar.getWidth();
        const y = allWidth - percent;
        const disec_y = y * curTask.gantt.options.bar_height / (curTask.polygon_w / 2);
        const y1 = (curTask.gantt.options.bar_height - disec_y) / 2;
        points_l = `${polygon_x},${curTask.y + curTask.height / 2} ${curTask.$bar.getX()},${curTask.y + curTask.height} ${curTask.$bar.getX()},${curTask.y}`;
        points_r = `${polygon_x + bar_w},${curTask.y} ${polygon_x + percent},${curTask.y + y1} ${polygon_x + percent},${curTask.y + y1 + disec_y} ${polygon_x + bar_w},${curTask.y + curTask.gantt.options.bar_height}`
    }
    return {
        bar_width: width,
        poly_points_l: points_l,
        poly_points_r: points_r
    };
}

/**
 * 拖动三角形
 * @param {*} element svg标签
 * @param {*} direction 拖拽方向
 * @param {*} isNone 三角形竖线
 */
export function update_mile_position(polygonW, curBar, element, direction, isNone) {
    const x = direction === 'left' ? curBar.getX() - polygonW / 2 : curBar.getX() + curBar.getWidth() + polygonW / 2;
    const left_common_options = [
        x + polygonW / 2,
        curBar.getY() + curBar.getHeight(),
        x + polygonW / 2,
        curBar.getY()
    ];
    const right_common_options = [
        curBar.getX() + curBar.getWidth(),
        curBar.getY(),
        curBar.getX() + curBar.getWidth(),
        curBar.getY() + curBar.getHeight()
    ]
    const left_options = [
        x,
        curBar.getY() + curBar.getHeight() / 2
    ];
    const right_options = [
        x,
        curBar.getY() + curBar.getHeight() / 2
    ];
    const left_none_options = isNone ? left_common_options : (left_options.concat(left_common_options));
    const right_none_options = isNone ? right_common_options : (right_options.concat(right_common_options));
    element.setAttribute('points', direction === 'left' ? left_none_options : right_none_options);
}
