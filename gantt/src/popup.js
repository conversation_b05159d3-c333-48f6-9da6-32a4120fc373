export default class Popup {
    constructor(parent, custom_html) {
        this.parent = parent;
        this.custom_html = custom_html;
        this.make();
    }

    make () {
        this.parent.innerHTML = `
            <div class="title"></div>
            <div class="subtitle"></div>
            <div class="pointer"></div>
        `;

        this.hide();

        this.title = this.parent.querySelector('.title');
        this.subtitle = this.parent.querySelector('.subtitle');
        this.pointer = this.parent.querySelector('.pointer');
    }

    show(options = {}, objectDescribeDates = []) {
        const { isChangeLeft, isChangeRight, target_element, position, mousemove, drag, subtitle, data } = options;
        const start_date = subtitle ? subtitle.split('－')[0] : '';
        const end_date = subtitle ? subtitle.split('－')[1] : '';
        const barIsLine = target_element.classList.contains('bar-line');
        const days = mousemove ? ((new Date(end_date).getTime() - new Date(start_date).getTime()) / (1000 * 60 * 60 * 24) + 1) : null;

        let popperDragName = this.getPopperDragName(drag, isChangeLeft, isChangeRight, barIsLine, objectDescribeDates, start_date, end_date);

        if (this.custom_html) {
            const html = mousemove
                ? `<div class="popup-drag">${popperDragName} <span style="display:${(isChangeLeft || isChangeRight) ? 'inline-block' : 'none'}">,${$t('共计')}${days}${$t('天')}</span></div>`
                : this.custom_html(data);
            this.parent.innerHTML = html;
            this.pointer = this.parent.querySelector('.pointer');
        } else {
            this.title.innerHTML = options.title;
            this.subtitle.innerHTML = options.subtitle;
            this.parent.style.width = `${this.parent.clientWidth}px`;
        }

        this.setPosition(target_element, position, options.position_data);

        // Show popup
        this.parent.style.display = 'block';
        this.updatePosition(target_element);

        // 里程碑显示线条
        const milepostLine = document.querySelector(`.milepost-line[data-id="${data.task?.id}"]`);
        if (milepostLine) {
            milepostLine.style.opacity = 0.5;
        }
    }

    getPopperDragName(drag, isChangeLeft, isChangeRight, barIsLine, objectDescribeDates, start_date, end_date) {
        if (isChangeLeft) {
            return barIsLine ? `${objectDescribeDates[0]}:${start_date}` : `${objectDescribeDates[2]}:${start_date}`;
        } else if (isChangeRight) {
            return barIsLine ? `${objectDescribeDates[1]}:${end_date}` : `${objectDescribeDates[3]}:${end_date}`;
        } else if (drag && barIsLine) {
            return `${objectDescribeDates[0]}－${objectDescribeDates[1]}：${start_date} ~ ${end_date}`;
        } else {
            return `${objectDescribeDates[2]}－${objectDescribeDates[3]}：${start_date} ~ ${end_date}`;
        }
    }
    setPosition(targetElement, position, positionData) {
        const positionMeta = targetElement.getBoundingClientRect();

        if (position === 'left') {
            this.parent.style.position = 'fixed';
            this.parent.style.left = `${positionMeta.left}px`;
            this.parent.style.top = `${positionMeta.bottom}px`;
        } else if (position === 'follow' && positionData) {
            this.parent.style.position = 'fixed';
            this.parent.style.left = `${positionData.left}px`;
            this.parent.style.top = `${positionData.top}px`;
        }
    }

    updatePosition(targetElement) {
        const cw = document.documentElement.clientWidth;
        const ch = document.documentElement.clientHeight;
        const rect = this.parent.getBoundingClientRect();

        if (rect.right > cw) {
            this.parent.style.left = `${rect.left - rect.width}px`;
        }

        if (rect.bottom > ch) {
            this.parent.style.top = `${rect.top - rect.height - targetElement.getBoundingClientRect().height}px`;
        }
    }

    hide () {
        this.parent.style.display = 'none';
        //里程碑隐藏线条
        let milepostLine = document.querySelector('.milepost-line');
        if (milepostLine) {
            milepostLine.style.opacity = 0;
        }
    }
}
