import { $, createSVG } from './svg_utils';

export default class Arrow {
	constructor(gantt, from_task, to_task, dependenciesType) {
		this.gantt = gantt;
		this.from_task = from_task;
		this.to_task = to_task;
		this.dependenciesType = dependenciesType;
		this.dependenciesTypeLabel = [$t('结束后开始'), $t('结束后结束'), $t('开始后开始'), $t('开始后结束')];
		this.calculate_path();
		this.draw();
		this.bind();
	}

	calculate_path() {
		switch (+this.dependenciesType) {
			case 1:
				this.calculate_path_end_start()
				break;
			case 2:
				this.calculate_path_end_end()
				break;
			case 3:
				this.calculate_path_start_start()
				break;
			case 4:
				this.calculate_path_start_end()
				break;
			default:
				break;
		}

		this.path = this.path.replace(/(^\s*)|(\s*$)|(\n\s*)/g, ' ');
	}

	//结束后开始1
	calculate_path_end_start() {
		let start_x = this.from_task.$bar.getX() + this.from_task.$bar.getWidth() + (this.from_task.task.is_milestone ? this.from_task.polygon_w/2 : 0);
		const start_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.from_task.task._index +
			this.gantt.options.padding;
		const end_x = this.to_task.$bar.getX() - (this.to_task.task.is_milestone ? this.to_task.polygon_w/2 : 0);
		const end_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.to_task.task._index +
			this.gantt.options.padding;
		const curve = this.gantt.options.arrow_curve;
		const space = this.gantt.options.arrow_space;
		const from_is_below_to = this.from_task.task._index > this.to_task.task._index;
		let flag = start_x + space * 2 > end_x;

		// from_taks在to_task下面
		if (from_is_below_to) {
			let line_path = flag ?
				`
                    v -${space}
                    a ${curve} ${curve} 0 0 0 -${curve} -${curve}
                    H ${end_x - space}
                    a ${curve} ${curve} 0 0 1 -${curve} -${curve}
                ` :
				'';

			this.path = `
                M ${start_x} ${start_y}
                h ${space} 0
                a ${curve} ${curve} 0 0 0 ${curve} -${curve}
                ${line_path}
                V ${end_y + curve}
                a ${curve} ${curve} 0 0 1 ${curve} -${curve}
                H ${end_x}
                ${this.getArrowPath('right')}
            `;
		} else {
			let line_path = flag ?
				`
                    v ${space}
                    a ${curve} ${curve} 0 0 1 -${curve} ${curve}
                    H ${end_x - space}
                    a ${curve} ${curve} 0 0 0 -${curve} ${curve}
                ` :
				'';

			this.path = `
                M ${start_x} ${start_y}
                h ${space} 0
                a ${curve} ${curve} 0 0 1 ${curve} ${curve}
                ${line_path}
                V ${end_y - curve}
                a ${curve} ${curve} 0 0 0 ${curve} ${curve}
                H ${end_x}
                ${this.getArrowPath('right')}
            `;
		}
	}

	//结束后结束2
	calculate_path_end_end() {
		let start_x = this.from_task.$bar.getX() + this.from_task.$bar.getWidth() + (this.from_task.task.is_milestone ? this.from_task.polygon_w/2 : 0);
		const start_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.from_task.task._index +
			this.gantt.options.padding;
		const end_x = this.to_task.$bar.getX() + this.to_task.$bar.getWidth() + (this.to_task.task.is_milestone ? this.to_task.polygon_w/2 : 0);
		const end_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.to_task.task._index +
			this.gantt.options.padding;
		const curve = this.gantt.options.arrow_curve;
		const space = this.gantt.options.arrow_space;
		const from_is_below_to = this.from_task.task._index > this.to_task.task._index;
		let flag = start_x < end_x;

		// from_taks在to_task下面
		if (from_is_below_to) {
			let line_path = flag ? `H ${end_x + space}` : `h ${space} 0`;

			this.path = `
                M ${start_x} ${start_y}
                ${line_path}
                a ${curve} ${curve} 0 0 0 ${curve} -${curve}
                V ${end_y + curve}
                a ${curve} ${curve} 0 0 0 -${curve} -${curve}
                H ${end_x}
                ${this.getArrowPath('left')}
            `;
		} else {
			let line_path = flag ? `H ${end_x + space}` : `h ${space} 0`;

			this.path = `
                M ${start_x} ${start_y}
                ${line_path}
                a ${curve} ${curve} 0 0 1 ${curve} ${curve}
                V ${end_y - curve}
                a ${curve} ${curve} 0 0 1 -${curve} ${curve}
                H ${end_x}
                ${this.getArrowPath('left')}
            `;
		}
	}
	//开始后开始3
	calculate_path_start_start() {
		let start_x = this.from_task.$bar.getX() - (this.from_task.task.is_milestone ? this.from_task.polygon_w/2 : 0);
		const start_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.from_task.task._index +
			this.gantt.options.padding;
		const end_x = this.to_task.$bar.getX() - (this.to_task.task.is_milestone ? this.to_task.polygon_w/2 : 0);
		const end_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.to_task.task._index +
			this.gantt.options.padding;
		const curve = this.gantt.options.arrow_curve;
		const space = this.gantt.options.arrow_space;
		const from_is_below_to = this.from_task.task._index > this.to_task.task._index;
		let flag = start_x < end_x;
		let line_path = flag ? `h -${space} 0` : `H ${end_x - space}`;

		// from_taks在to_task下面
		if (from_is_below_to) {
			this.path = `
                M ${start_x} ${start_y}
                ${line_path}
                a ${curve} ${curve} 0 0 1 -${curve} -${curve}
                V ${end_y + curve}
                a ${curve} ${curve} 0 0 1 ${curve} -${curve}
                H ${end_x}
                ${this.getArrowPath('right')}
            `;
		} else {
			this.path = `
                M ${start_x} ${start_y}
                ${line_path}
                a ${curve} ${curve} 0 0 0 -${curve} ${curve}
                V ${end_y - curve}
                a ${curve} ${curve} 0 0 0 ${curve} ${curve}
                H ${end_x}
                ${this.getArrowPath('right')}
            `;
		}
	}
	//开始后结束4
	calculate_path_start_end() {
		let start_x = this.from_task.$bar.getX() - (this.from_task.task.is_milestone ? this.from_task.polygon_w/2 : 0);
		const start_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.from_task.task._index +
			this.gantt.options.padding;
		const end_x = this.to_task.$bar.getX() + this.to_task.$bar.getWidth() + (this.to_task.task.is_milestone ? this.to_task.polygon_w/2 : 0);
		const end_y = (this.gantt.options.padding + this.gantt.options.bar_height) *
			this.to_task.task._index +
			this.gantt.options.padding;
		const curve = this.gantt.options.arrow_curve;
		const space = this.gantt.options.arrow_space;
		const from_is_below_to = this.from_task.task._index > this.to_task.task._index;
		let flag = start_x + space * 2 > end_x;

		// from_taks在to_task下面
		if (from_is_below_to) {
			let line_path = !flag ?
				`
                    v -${space}
                    a ${curve} ${curve} 0 0 1 ${curve} -${curve}
                    H ${end_x + space}
                    a ${curve} ${curve} 0 0 0 ${curve} -${curve}
                ` :
				'';

			this.path = `
                M ${start_x} ${start_y}
                h -${space} 0
                a ${curve} ${curve} 0 0 1 -${curve} -${curve}
                ${line_path}
                V ${end_y + curve}
                a ${curve} ${curve} 0 0 0 -${curve} -${curve}
                H ${end_x}
                ${this.getArrowPath('left')}
            `;
		} else {
			let line_path = !flag ?
				`
                    v ${space}
                    a ${curve} ${curve} 0 0 0 ${curve} ${curve}
                    H ${end_x + space}
                    a ${curve} ${curve} 0 0 1 ${curve} ${curve}
                ` :
				'';

			this.path = `
                M ${start_x} ${start_y}
                h -${space} 0
                a ${curve} ${curve} 0 0 0 -${curve} ${curve}
                ${line_path}
                V ${end_y - curve}
                a ${curve} ${curve} 0 0 1 -${curve} ${curve}
                H ${end_x}
                ${this.getArrowPath('left')}
            `;
		}
	}

	getArrowPath(type) {
		switch (type) {
            case 'left':
                return `m 5 5 l -5 -5 l 5 -5`;
            case 'right':
                return `m -5 -5 l 5 5 l -5 5`;
            default:
                return '';
        }
	}

	draw() {
		this.element = createSVG('path', {
			d: this.path || '',
			'data-from': this.from_task.task.id,
			'data-to': this.to_task.task.id,
		});
	}

	update() {
		this.calculate_path();
		this.element.setAttribute('d', this.path);
	}

	bind() {
		if (!this.element) return;
		this.setup_click_event();
	}

	setup_click_event() {
		let popup_off = this.gantt.options.popup_off;

		if (popup_off) {
			$.on(this.element, popup_off, e => {
				clearTimeout(this.gantt.hide_popup_timer);
				this.gantt.hide_popup_timer = setTimeout(() => {
					this.gantt.hide_popup('arrow');
				}, 300);
				this.gantt.setZindex(this.element, 'before', 'arrow', 'progress');
			});
		}

		$.on(this.element, 'focus ' + this.gantt.options.popup_trigger, e => {
			this.show_popup(e);
			this.gantt.setZindex(this.element, 'top', 'arrow');
		})
	}

	show_popup(e) {
		this.gantt.show_popup('arrow', {
			target_element: this.element,
			position: 'follow',
			position_data: {
				left: e.clientX,
				top: e.clientY + 2
			},
			data: {
				from_task: this.from_task.task,
				to_task: this.to_task.task,
				dependencies_type: this.dependenciesTypeLabel[this.dependenciesType - 1]
			}
		});
	}
}
