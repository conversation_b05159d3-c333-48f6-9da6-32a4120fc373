{"imports": {"projects": []}, "exports": {"dirs": [{"name": "orderobj-ava", "type": "subPackage", "exportName": "objformplugin-salesorder"}, {"name": "attribute-ava", "type": "subPackage", "exportName": "objformplugin-attribute"}, {"name": "mccurrency-ava", "type": "subPackage", "exportName": "objformplugin-mccurrency"}, {"name": "multiunit-ava", "type": "subPackage", "exportName": "objformplugin-multiunit"}, {"name": "bom-ava", "type": "subPackage", "exportName": "objformplugin-bom"}, {"name": "priceservice-ava", "type": "subPackage", "exportName": "objformplugin-priceservice"}, {"name": "datasource-ava", "type": "subPackage", "exportName": "objformplugin-datasource"}, {"name": "pluginbase-ava", "type": "mainPackage"}, {"name": "manual-gift-ava-plugin", "type": "subPackage", "exportName": "objformplugin-manualgift"}, {"name": "changeprice-ava", "type": "subPackage", "exportName": "objformplugin-changeprice"}, {"name": "rebate-coupon-ava-plugin", "type": "subPackage", "exportName": "objformplugin-rebate-coupon"}, {"name": "price-policy-ava-plugin", "type": "subPackage", "exportName": "objformplugin-pricepolicy"}, {"name": "<PERSON><PERSON><PERSON><PERSON>a<PERSON>", "type": "subPackage", "exportName": "objformplugin-quote"}, {"name": "quoter-ava", "type": "subPackage", "exportName": "objformplugin-quoter"}, {"name": "dynamicamount-ava", "type": "subPackage", "exportName": "objformplugin-dynamicamount"}, {"name": "asynccommit-ava", "type": "subPackage", "exportName": "objformplugin-asynccommit"}, {"name": "periodproduct-ava", "type": "subPackage", "exportName": "objformplugin-periodproduct"}, {"name": "memberpoint-ava", "type": "subPackage", "exportName": "objformplugin-memberpoint"}]}}