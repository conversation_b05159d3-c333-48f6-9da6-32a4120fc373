import {QuoterPropBuilder} from "./QuoterPropBuilder";
import {QuoterCalculate} from "./QuoterCalculate";

export class MasterFieldEdit {

    constructor(context) {
        let {fieldMapping} = context || {};
        this.fieldMapping = fieldMapping;
        this.quoterPropBuilder = new QuoterPropBuilder(context);
        this.quoterCalculate = new QuoterCalculate(context);
    }

    async fieldEditAfter(pluginExecResult, options) {
        let {fieldName, changeData, dataGetter} = options;
        let {attribute_constraint_id, quoter_json, quoter_label} = this.fieldMapping.getMasterFields();
        if (fieldName === attribute_constraint_id) {
            let attributeConstraintId = changeData[attribute_constraint_id];
            if (!(typeof attributeConstraintId === 'undefined')) {//更换了属性级联约束
                let quoterData = await this.quoterPropBuilder.queryAttributeConstraintData(attributeConstraintId, 0, dataGetter);
                Object.assign(changeData, {
                    [quoter_json]: JSON.stringify(quoterData),
                    [quoter_label]: null
                });
            }
        }
    }

    async fieldEditEnd(pluginExecResult, options) {
        let {fieldName, dataUpdater, dataGetter} = options;
        let {attribute_constraint_id, quoter_json, quoter_label} = this.fieldMapping.getMasterFields();
        if (fieldName !== attribute_constraint_id) {
            let updateMaster = options && options.calUiRst && options.calUiRst.updateMaster;
            let attributeConstraintId = updateMaster && updateMaster[attribute_constraint_id];
            if (!(typeof attributeConstraintId === 'undefined')) {//更换了属性级联约束
                let quoterData = await this.quoterPropBuilder.queryAttributeConstraintData(attributeConstraintId, 0, dataGetter);
                dataUpdater.updateMaster({
                    [quoter_json]: JSON.stringify(quoterData),
                    [quoter_label]: null
                })
            }
        }
        await this.quoterCalculate.masterFieldEditEnd(options);
    }
}