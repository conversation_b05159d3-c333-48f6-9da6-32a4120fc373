const FIELD_MAP = Object.freeze({
    masterFields: {
        form_account_id: 'form_account_id',// 客户id
        form_partner_id: 'form_partner_id',// 合作伙伴id
        form_price_book_id: 'form_price_book_id',// 价目表id
    },

    detailFields: {
        price: 'product_price',// 价格
        quantity: 'quantity',// 数量
        product_id: 'product_id',// 产品
        price_book_product_id: 'price_book_product_id',//价目表产品
        price_book_id: "price_book_id",//价目表
        unit: 'unit',//单位(基准单位)，引用字段：引用产品对象的单位字段
        actual_unit: 'actual_unit',// 单位
        conversion_ratio: 'conversion_ratio',// 转换比例
        base_unit_count: 'base_unit_count', // 基准单位数量
        stat_unit_count: 'stat_unit_count',// 统计单位数量
        other_unit: 'other_unit',// 其他单位
        other_unit_quantity: 'other_unit_quantity',// 其他单位数量
        is_multiple_unit: 'is_multiple_unit',//是否是多单位
        price_book_price: 'price_book_price'
    }
});

export class FieldConvert {

    constructor(pluginParam) {
        let {fieldMapping: masterFieldMap, details} = pluginParam && pluginParam.params || {};
        this.masterFieldMap = masterFieldMap;
        this.detailFieldMap = {};
        details && details.length && details.forEach(detail => {
            let {objectApiName, fieldMapping} = detail;
            if (objectApiName) {
                this.detailFieldMap[objectApiName] = fieldMapping;
            }
        })
    }

    getMasterFields() {
        return Object.assign({}, FIELD_MAP.masterFields, this.masterFieldMap)
    }

    getDetailFields(objectApiName) {
        return Object.assign({}, FIELD_MAP.detailFields, this.detailFieldMap[objectApiName])
    }

    getDetailObjApiNames() {
        return this.detailFieldMap && Object.keys(this.detailFieldMap);
    }
}