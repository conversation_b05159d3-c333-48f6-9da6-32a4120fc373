/**
 * @desc: 客户财务从对象
 * @author: zhongly
 * @date: 2022/6/6
 */

 import Base from 'plugin_base'
 
 export default class AccountFinInfoObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.fieldMapping = this.getAllFields();
    }

    _mdCopyAfter(plugin, param) {
        param.dataIndex.forEach((id) => {
            let _data = param.dataGetter.getData('', id);
            _data.is_default && (_data.is_default = false);
        })
    }

    getHook() {
        return [
            {
                event: 'md.copy.after',
                functional: this._mdCopyAfter.bind(this)
            }
        ];
    }
}
 