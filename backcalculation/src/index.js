/**
 * @desc: 反算插件
 * @author: wangshaoh
 * @date: 2/21/22
 */

import PPM from 'plugin_public_methods'

export default class BackCalculation {

    constructor(pluginService, pluginParam) {
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
         //记录主对象最后编辑的【额外调整】还是【整单折扣】
        this.lastEditDiscount = null; 
    }

    getFieldDescribe(field,targetObj="mdDescribe") {
        return this[targetObj][field];
    }

    getDataByRowIds(param, mdApiName, rowIds) {
        rowIds = Array.isArray(rowIds) ? rowIds : [rowIds];
        return rowIds.map(rowId => {
            return param.dataGetter.getData(mdApiName, rowId);
        });
    }

    async _mdEditAfter(plugin, param) {  
        let mdApiName = param.objApiName;
        let field = param.fieldName;
        this.mdDescribe = param.dataGetter.getDescribe(mdApiName).fields || {};
        this.backField = null;
        this.filterField = [];
        this.recursionCalculate(field, mdApiName, param);
        if (this.backField) {
            let r = await param.triggerCalAndUIEvent({
                // noMerge: true,
                changeFields: [this.backField],
                operateType: 'mdEdit',
                dataIndex: param.dataIndex,
                objApiName: mdApiName,
                filterFields: {
                    [mdApiName]: this.filterField
                },
                // masterData: {},
                // details: {},
                // extraFields: {[mdApiname]: needCalFields},
            });
            // if (r && r.Value && r.Value.calculateResult) {
            //     // param.end();
            // }
        }
    }

    getExpByField(field, expList, targetObj="mdDescribe"){
        let des = this.getFieldDescribe(field, targetObj);
        return expList.find(item => {
            let defVal = item.default_value;
            return defVal.includes(des.default_value)
        });
    }

    // 获取反算公式；
    getPre_expression(field, exp, targetObj="mdDescribe"){
        if(exp.pre_expressionMap){
            let des = this.getFieldDescribe(field, targetObj);
            return exp.pre_expressionMap[des.default_value];
        }else{
            return exp.pre_expression
        }
    }

    /**
     * @desc  处理反算
     * @param field 修改字段
     * @param mdApiName 从对象apiname
     * @param param
     */
    recursionCalculate(field = '', mdApiName = '', param) {
        let formula = PPM.getFormulaConfig(mdApiName);
        if (!formula) return;
        // 不需要反算的字段 return
        let expList = formula.filter(f => f.data === field);
        if (!expList || !expList.length) return;
        // 改默认值公式的不反算
        let exp = this.getExpByField(field, expList);
        if (!exp) return;
        let datas = this.getDataByRowIds(param, mdApiName, param.dataIndex);
        let backField = exp.pre_expression_key;
        datas.forEach(item => {
            let changeField = param.changeData[item.rowId];
            let changeVal = changeField.hasOwnProperty(field) ? changeField[field] : item[field];
            // if (PPM.hasValue(changeVal)) item[field] = changeVal;
            item[field] = changeVal;
            let pe = this.getPre_expression(field, exp);
            let val = PPM.doFrontCount(backField, pe, item, this.mdDescribe);
            if (PPM.hasValue(val)) param.dataUpdater.updateData(mdApiName, item.rowId, {
                [backField]: val
            });
        });
        this.backField = backField;
        this.filterField.push(field);
        this.recursionCalculate(backField, mdApiName, param);
    }

    /**
     * @desc
     * @param rowIds 行id
     * @param pre_expression 计算公式
     * @param backField 要反算的字段
     * @param changeField  变化的字段
     * @param changeVal  变化的字段值
     * @param mdApiName  从对象apiname
     * @param param  底层提供的方法集
     * @private
     */
    _calculate({data = [], pre_expression = '', backField = '', changeField = '', changeVal, mdApiName = ''} = {}, param = {}) {
        let mdDescribe = param.dataGetter.getDescribe(mdApiName).fields || {};
        data.forEach(item => {
            if (PPM.hasValue(changeVal)) item[changeField] = changeVal;
            let val = PPM.doFrontCount(backField, pre_expression, item, mdDescribe);
            if (PPM.hasValue(val)) {
                item[backField] = val;
                param.dataUpdater.updateData(mdApiName, item.rowId, {
                    [backField]: val
                });
            }
        });
        if (this.filterField) this.filterField.push(changeField);
    }

    // 向外提供反算方法。不能自动连续反算
    async _backcalculation_calculate(plugin, obj) {
        this.filterField = [];
        let {rowIds = [], pre_expression = '', backField = '', changeField = '', changeVal, mdApiName = '', noCalculate = false, param} = obj;
        let data = this.getDataByRowIds(param, mdApiName, rowIds);
        await this._calculate({data, pre_expression, backField, changeField, changeVal, mdApiName}, param);
        if (noCalculate) return;
        await param.triggerCalAndUIEvent({
            changeFields: [backField],
            operateType: 'mdEdit',
            dataIndex: rowIds,
            objApiName: mdApiName,
            filterFields: {
                [mdApiName]: this.filterField
            },
        });
    }

    // 向外提供反算方法，直接算数据本体，数据可能还没添加到从对象
    _backcalculation_calculateMeta(plugin, obj) {
        let {data = [], field = '', mdApiName = '', param} = obj;
        let formula = PPM.getFormulaConfig(mdApiName);
        if (!formula) return;
        // 不需要反算的字段 return
        let expList = formula.filter(f => f.data === field);
        if (!expList || !expList.length) return;
        // 改默认值公式的不反算
        this.mdDescribe = param.dataGetter.getDescribe(mdApiName).fields || {};
        let exp = this.getExpByField(field, expList);
        if (!exp) return;
        let backField = exp.pre_expression_key;
        data = data.map(item => {
            let fd = param.dataGetter.getData(mdApiName, item.rowId) || {};
            return Object.assign(fd, item)
        });
        data.forEach(item => {
            let pe = this.getPre_expression(field, exp);
            let val = PPM.doFrontCount(backField, pe, item, this.mdDescribe);
            if (PPM.hasValue(val)) {
                item[backField] = val;
                param.dataUpdater.updateData(mdApiName, item.rowId, {
                    [backField]: val
                });
            }
        });
        this._backcalculation_calculateMeta(backField, mdApiName, param);
    }

    //主对象字段反算
    _formDataChangeAfter(plugin, param){
        if(!this.isDynamicAmortize()){  
            return;
        }
        const {masterObjApiName ,changeData={}}=param;
        
        const formula = this.getFormulaConfig(masterObjApiName);
        if (!formula) return;

        this.backCalFiledArr = [];
        this.masterDescribe = this.masterDescribe ?? (param.dataGetter.getDescribe(masterObjApiName)?.fields || {});
        
        const changeKeys = Object.keys(changeData); 
        changeKeys.forEach( key => {
            this.recursionBackCal(key, masterObjApiName, param,"master",formula);
        })

        if(this.backCalFiledArr.length){
           this.updateParam(param,masterObjApiName,[...this.backCalFiledArr,...changeKeys]);
        }

        this.cacheUserDiscount(changeKeys,changeData);
    }

    //缓存用户编辑的整单折扣
    cacheUserDiscount(changeKeys,changeData){
        if(changeKeys.includes("discount")){
            this.lastEditDiscount = changeData["discount"];
        }else if(changeKeys.includes("dynamic_amount")){
            this.lastEditDiscount = null;
        }
    }

    isDynamicAmortize(){
        this.dynamicAllowAmortize = this.dynamicAllowAmortize??(this.pluginParam.bizStateConfig['dynamic_allow_amortize']=="1");
        return this.dynamicAllowAmortize;
    }

    getFormulaConfig(apiName) {
        return PPM.getFormulaConfig(apiName);
    }
    
    recursionBackCal(field = '', apiName = '', param,target="md", formula) {
        // 改默认值公式的不反算
        let exp = this.findFormula(field,formula,`${target}Describe`)
        if (!exp) return;
        
        let backField = exp.pre_expression_key;
        if(target=="md"){
            this.updateDetailData(param, field, backField, exp, apiName) 
        }else{
            this.updateMasterData(param, field, backField, exp);
        }

        this.backCalFiledArr.push(backField);
        this.recursionBackCal(backField, apiName, param, target,formula);
    }

    findFormula(field,formula,targetDescribe){
        let expList = formula.filter(f => f.data === field);
        if (!expList || !expList.length) return null;
        return this.getExpByField(field, expList, targetDescribe);
    }

    /**
     * 主对象数据编辑触发反算 & 更新 
     */
    updateMasterData(param, field, backField, exp) {
        let data = param.dataGetter.getMasterData();
        data[field] = param.changeData.hasOwnProperty(field) ?param.changeData[field]:data[field];
        let pe = this.getPre_expression(field, exp);
        let val = PPM.doFrontCount(backField, pe, data, this.masterDescribe);
        if (PPM.hasValue(val)) {
            param.dataUpdater.updateMaster({ [backField]: val });
        }
    }

    /**
     * 从对象数据编辑触发反算 & 更新 
     * field：编辑的字段
     * backField：反算的字段
     */
    updateDetailData(param, field, backField, exp, mdApiName) {
        let datas = this.getDataByRowIds(param, mdApiName, param.dataIndex);
        datas.forEach(item => {
            let changeField = param.changeData[item.rowId];
            let changeVal = changeField.hasOwnProperty(field) ? changeField[field] : item[field];
            item[field] = changeVal;
            let pe = this.getPre_expression(field, exp);
            let val = PPM.doFrontCount(backField, pe, item, this.mdDescribe);
            if (PPM.hasValue(val)) {
                param.dataUpdater.updateData(mdApiName, item.rowId, { [backField]: val });
            }
        });
    }

    updateParam(param,apiName,modifyFields){
        param.extraFields=param.extraFields||{};
        param.filterFields = param.filterFields||{};
        param.filterFields[apiName] = [...(param.filterFields[apiName] || []), ...modifyFields];
        
        const triggerCalFields = param.dataGetter.getCalculateFieldsByFieldName(modifyFields, false, apiName),
            calFields = PPM.mergeArrayObj(param.extraFields,triggerCalFields);
        
        //过滤反算的字段
        calFields[apiName]=(calFields[apiName]||[]).filter(f=>!modifyFields.includes(f));
        param.extraFields = calFields;
    }

    //从对象数据单元格编辑，平铺页面编辑，单元格粘贴
    async _mdDataChange(plugin,param){
        await this.checkTriggerAmortize(param);
    }

    //从对象数据增、删、行复制
    async _mdRowChange(plugin,param){
        await this.resetMasterDynamic(param);
    }

    // 业务逻辑编辑了明细：bom二次配置，从历史订单添加
    async _mdBusinessDataChange(plugins, obj){
        await this.resetMasterDynamic(obj.param);
    }

     //检查从对象编辑是否触发整单产品合计计算，如果触发则清空额外调整
     async checkTriggerAmortize(param){
        if(!this.isDynamicAmortize()){
            return;
        }
        const changeInfo = param.collectChange();
        if(changeInfo.masterUpdate.hasOwnProperty("product_amount")){
            await this.resetMasterDynamic(param,true);
        }
    }

    //保留整单折扣，计算对应的额外调整值
    async resetMasterDynamic(param,reset){
        // 如果强制重算或需要动态分摊，则重算 dynamic_amount
        if (reset || this.isDynamicAmortize()) {
            if(this.lastEditDiscount !== null ){
                const dynamic = await this.convertDiscountToDynamic(param);
                param.dataUpdater.updateMaster({ dynamic_amount: dynamic });
                await param.triggerCal({
                    changeFields: ["dynamic_amount"],
                    operateType: 'masterEdit',
                    objApiName: param.masterObjApiName
                });
            }
        }
    };

    //调用接口，将整单折扣转换成额外调整处理后续逻辑
    async convertDiscountToDynamic(param){
        const args={
            master:param.dataGetter.getMasterData(),
            details:param.dataGetter.getDetail(param.objApiName),
            beforeDiscount:this.lastEditDiscount
        },
        url = "FHH/EM1HNCRM/API/v1/object/price_policy/service/calculate_dynamic_amount",
        result = await PPM.ajax(this.pluginService.api.request, url, args);
        return result?.dynamicAmount??0;
    }
    
    apply() {
        return [
            {
                event: 'md.edit.after',
                functional: this._mdEditAfter.bind(this)
            }, {
                event: 'backcalculation.calculate',
                functional: this._backcalculation_calculate.bind(this)
            }, {
                event: 'backcalculation.calculateMeta',
                functional: this._backcalculation_calculateMeta.bind(this)
            }, {
                event: "form.dataChange.after",
                functional: this._formDataChangeAfter.bind(this)
            },{
                event: "form.uiEvent.before",
                functional: this._mdDataChange.bind(this)
            },{
                event: "bom.twiceConfig.after",  //bom二次配置
                functional: this._mdBusinessDataChange.bind(this)
            }, {
                event: "salesOrderHistory.calculate.end", //从历史订单添加，计算结束后
                functional: this._mdBusinessDataChange.bind(this)
            }
        ];
    }

    destroy() {

    }
}
