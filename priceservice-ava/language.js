const info = {
    "ava.object_form.onsale.pricebook_not_avaliable_tip_info": "当前价目表不可用已清空，请知悉！",
    "ava.object_form.plese_select_first": "请先选择{0}",
    "ava.object_form.onsale.change_pricebook_message": "更换价目表，不在新价目表允销范围的产品将会被删除，是否继续?",
    "ava.object_form.onsale.change_field_tip_1": "更换{0}将清空已选价目表和产品，确认更换？",
    "AccountObj.attribute.self.display_name": "客户",
    "ava.object_form.onsale.validate_account_tip": "已选价目表不适用于当前用户或客户，无法添加产品",
    "ava.object_form.onsale.getrealprice.batch_add_changed_tip": "{0}，已选数量不在当前价目表阶梯范围内，回填的价目表已更新，请知悉！",
    "ava.object_form.add_from_param": "从{0}添加",
    "ava.object_form.onsale.getrealprice.new_price_tip_1": "当前产品价格为可售范围对应的价目表的最新价格！",
    "ava.object_form.onsale.getrealprice.new_price_tip_2": "当前产品价格为可售范围对应的产品的最新价格！",
    "ava.object_form.onsale.getrealprice.product_pkg_tip": "组合产品须点击“配置”获取最新配置价格！",
    "ava.object_form.onsale.getrealprice.delete_tip": "产品：{0} 未在该客户可售范围中，已移除！",
    "ava.object_form.close": "关闭",
    "advanced.pricing.unlimited.text": "不限",
    "ava.object_form.priceservice.stratified_pricing_detail": "阶梯价格明细",
    "ava.object_form.priceservice.range": "数量范围",
    "ava.object_form.priceservice.priceBookPrice": "价目表价格",
    "ava.object_form.priceservice.priceBookSellingPrice": "价目表售价",
    "ava.object_form.priceservice.discount": "折扣",
    "ava.object_form.priceservice.tiered_price_tip": "备注：展示的是对应【分层价目表明细】的原始层级价格"
}

//固定格式，fssub依赖 -start
module.exports = {
    info
}
//固定格式，fssub依赖 -end