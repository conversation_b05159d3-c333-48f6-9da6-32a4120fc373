import Clone from "../package/getprice/Clone";
import {checkPriceBook, isNeedCheckPriceBook, key_last_selected_price_book_id} from "./utils";
import {getPriceBookParamAndSimplifyDetails, i18n, isEmpty} from "../../pluginbase-ava/package/pluginutils";

export class MasterFieldEdit {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.calcByRealPrice = new Clone(context);
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    masterFieldEditBefore(pluginExecResult, options) {
        let {form_price_book_id} = this.fieldMapping.getMasterFields();
        if (options.fieldName === form_price_book_id) {
            return this.priceBookEditBefore(options);
        }
    }

    masterFieldEditAfter(pluginExecResult, options) {
        let {fieldName, masterObjApiName} = options;
        let {form_account_id, form_partner_id, form_price_book_id} = this.fieldMapping.getMasterFields();
        let priceBookValidField = this.bizStateConfig.getMatchPriceBookValidField(masterObjApiName);
        if (fieldName === form_account_id) {//更换客户
            return this.accountEditAfter(options);
        } else if ([form_partner_id, priceBookValidField].includes(fieldName)) {//更换合作伙伴、自定义日期字段
            return this.priceBookValidEditAfter(options);
        } else if (fieldName === form_price_book_id) {//更换价目表
            return this.priceBookEditAfter(options);
        }
    }

    masterFieldEditEnd(pluginExecResult, options) {

    }

    priceBookEditBefore(options) {
        let {form_account_id} = this.fieldMapping.getMasterFields();
        let {formApis, objApiName, selectObjectParams, dataGetter} = options;
        let masterData = dataGetter.getMasterData();
        let {[form_account_id]: accountId} = masterData || {};
        if (!accountId) {
            let layoutFields = dataGetter.getLayoutFields(objApiName);
            let accountIdField = layoutFields && layoutFields[form_account_id];
            let accountLabel = accountIdField && accountIdField.label || i18n('AccountObj.attribute.self.display_name')/*客户*/;
            let msg = i18n('ava.object_form.plese_select_first', [accountLabel])/*请先选择{0}*/;
            formApis && formApis.focusShowMasterFieldError && formApis.focusShowMasterFieldError(form_account_id, msg, true);
            return {consumed: true};
        } else {
            Object.assign(selectObjectParams, {
                disableAdd: true,
            });
            return {selectObjectParams};
        }
    }

    accountEditAfter(options) {
        let {changeData, dataGetter} = options;
        let {form_price_book_id} = this.fieldMapping.getMasterFields();
        let masterData = dataGetter.getMasterData();
        Object.assign(masterData, changeData);
        let {[form_price_book_id]: priceBookId} = masterData;
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        let preFunction;
        if (openPriceBook && !priceBookId) {//开启价目表且价目表无值，则获取默认价目表
            preFunction = this.getDefaultPriceBook(options)
                .then(priceBookData => {
                    Object.assign(changeData, !isEmpty(priceBookData) && {
                        [form_price_book_id]: priceBookData._id,
                        [`${form_price_book_id}__r`]: priceBookData.name__r || priceBookData.name,
                    });
                })
        } else {
            preFunction = Promise.resolve();
        }
        return preFunction.then(() => {
            return this.priceBookValidEditAfter(options);
        })
    }

    priceBookValidEditAfter(options) {
        let {fieldName, changeData} = options;
        if (isEmpty(changeData)) {
            return Promise.resolve();
        }
        return this.checkPriceBook(options)
            .then(available => {
                if (!available) {
                    let {form_price_book_id} = this.fieldMapping.getMasterFields();
                    Object.assign(changeData, {
                        [form_price_book_id]: null,
                        [`${form_price_book_id}__r`]: null
                    })
                    this.pluginApi.showToast(i18n('ava.object_form.onsale.pricebook_not_avaliable_tip_info')/*当前价目表不可用已清空，请知悉！*/);
                }
                this.resetNeedCheckPriceBookFlag(options.dataUpdater);
                return this.calcByRealPrice.calcPriceByFieldChanged(options, fieldName, changeData);
            })
    }

    priceBookEditAfter(options) {
        let {fieldName, changeData} = options;
        if (isEmpty(changeData)) {
            return Promise.resolve();
        }
        let priceBookChanged = !(typeof changeData[fieldName] === 'undefined');
        if (priceBookChanged) {
            this.resetNeedCheckPriceBookFlag(options.dataUpdater);
            return this.calcByRealPrice.calcPriceByFieldChanged(options, fieldName, changeData);
        }
        return Promise.resolve();
    }

    getDefaultPriceBook(options) {
        let isOpenPriceBook = this.bizStateConfig.isOpenPriceBook();
        if (!isOpenPriceBook) {
            return Promise.resolve();
        }
        let {form_account_id, form_price_book_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {masterObjApiName, dataGetter, changeData} = options;
        let masterData = dataGetter.getMasterData();
        Object.assign(masterData, changeData);
        let {[form_account_id]: accountId, [form_price_book_id]: priceBookId, [form_partner_id]: partnerId} = masterData || {};
        let masterLayout = dataGetter.getLayoutFields && dataGetter.getLayoutFields(masterObjApiName);
        let priceBookField = masterLayout && masterLayout[form_price_book_id];
        let isRequired = priceBookField && priceBookField.is_required;
        if (!isRequired || !accountId || priceBookId) {
            return Promise.resolve();
        }
        let lastPriceBookId = this.pluginApi.getLocal(key_last_selected_price_book_id);
        let detailObjApiName = this.fieldMapping.getFirstDetailObjApiName();
        let params = {
            account_id: accountId,
            price_book_id: lastPriceBookId,
            partner_id: partnerId,
            object_data: masterData,
            details: getPriceBookParamAndSimplifyDetails(true, masterObjApiName, detailObjApiName, form_price_book_id, dataGetter)
        };
        return this.requestApi.getDefaultPriceBook(params).catch(err => {
            this.pluginApi.showToast(err);
        });
    }

    checkPriceBook(options) {
        let {changeData, dataGetter} = options;
        let masterData = dataGetter.getMasterData();
        Object.assign(masterData, changeData);
        let {form_price_book_id} = this.fieldMapping.getMasterFields();
        let {[form_price_book_id]: priceBookId} = masterData;
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        if (!openPriceBook || !priceBookId) {//未开启价目表，或者未选择价目表
            return Promise.resolve(true);
        }
        return checkPriceBook({//选择了价目表，要校验价目表
            fieldMapping: this.fieldMapping,
            pluginApi: this.pluginApi,
            requestApi: this.requestApi
        }, options)
    }

    resetNeedCheckPriceBookFlag(dataUpdater) {
        dataUpdater && dataUpdater.updateMaster && dataUpdater.updateMaster({[isNeedCheckPriceBook]: false});
    }
}