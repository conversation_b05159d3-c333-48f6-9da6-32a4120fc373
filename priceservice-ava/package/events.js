//向外抛出的事件
export const emitEvent = Object.freeze({
    priceService_batchSelectSkuConfig_sync: "price-service.batchSelectSkuConfig.sync",//批量选sku时，添加配置信息
    priceService_batchAdd_isTriggerGetPrice_sync: "price-service.batchAdd.isTriggerGetPrice.sync",//批量选sku后，是否触发取价
    priceService_batchAdd_getPriceParam_sync: "price-service.batchAdd.getPriceParam.sync",//批量选sku后，处理取价接口入参

    priceService_form_detailDataDoNotCalcPrice_sync: "price-service.form.detailDataDoNotCalcPrice.sync",//该明细不参与取价
    priceService_form_parseFullProduct_sync: "price-service.form.parseFullProduct.sync",
    priceService_form_getPriceParam_sync: "price-service.form.getPriceParam.sync",//在表单页面取价时，处理取价接口入参。（复制映射、更换主对象客户、更换从对象价目表等入口触发的取价）
    priceService_getBackFills_before_sync: "price-service.getBackFills.before.sync",//根据取价结果获取回填的字段
    priceService_filterProduct_before_sync: "price-service.filterProduct.before.sync",//选产品时，是否过滤当前产品，true：过滤
    priceService_triggerCalc_before_sync: "price-service.triggerCalc.before.sync",//触发取价前钩子
    priceService_triggerCalc_end_sync: "price-service.triggerCalc.end.sync",//触发取价后钩子（页面数据已更新完毕）
    priceService_deleteDetail_after_sync: "price-service.deleteDetail.after.sync",//取价插件业务中删除明细后置动作钩子（触发计算前）
    priceService_form_ignoreCalcResult_sync: "price-service.form.ignoreCalcResult.sync",//当前明细数据不使用取价结果
});