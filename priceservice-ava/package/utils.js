import {each, getPriceBookParamAndSimplifyDetails, isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";

export const getRealPriceResult = 'get_real_price_result';//getRealPrice接口返回的明细数据
export const isNeedCheckPriceBook = "key_is_need_check_price_book";//选产品时是否需要校验价目表，进入新建编辑页面时有价目表id需要校验
export const key_last_selected_price_book_id = "key_last_selected_price_book_id";//上一次选择的价目表明细id

export function filterMasterObjectData(masterObjectData) {
    if (!masterObjectData) {
        return;
    }
    let result = {};
    let filterFields = ['_id', 'record_type', 'object_describe_api_name', 'object_describe_id'];
    each(masterObjectData, (value, key) => {
        if (!filterFields.includes(key)) {
            result[key] = value;
        }
    });
    return result;
}

export function appendFilters2Where(filters, wheres) {
    if (!filters || !filters.length) {
        return wheres;
    }
    if (wheres && wheres.length) {
        wheres.forEach(where => {
            if (where.filters) {
                where.filters = where.filters.concat(filters);
            } else {
                where.filters = filters;
            }
        });
    } else {
        wheres = [];
        wheres.push({
            filters: filters
        });
    }
    return wheres;
}

export function checkPriceBook(pluginContext, pluginOptions) {
    let {fieldMapping, pluginApi, requestApi} = pluginContext;
    let {form_account_id, form_partner_id, form_price_book_id} = fieldMapping.getMasterFields();
    let detailObjApiName = fieldMapping.getFirstDetailObjApiName();
    let {masterObjApiName, dataGetter, changeData} = pluginOptions || {};
    let masterData = dataGetter.getMasterData && dataGetter.getMasterData();
    if (!isEmpty(changeData)) {
        Object.assign(masterData, changeData);
    }
    let {[form_account_id]: accountId, [form_partner_id]: partnerId, [form_price_book_id]: priceBookId} = masterData || {};
    let pageId = dataGetter.getPageId();
    let token = 'price_service_' + uuid();
    pluginApi.showSingletonLoading(token, {}, pageId);
    return requestApi.validateAccountPriceBook({
        account_id: accountId,
        price_book_id: priceBookId,
        partner_id: partnerId,
        object_data: masterData,
        details: getPriceBookParamAndSimplifyDetails(true, masterObjApiName, detailObjApiName, form_price_book_id, dataGetter)
    }).then(available => {
        pluginApi.hideSingletonLoading(token, pageId);
        return available;
    }).catch(err => {
        pluginApi.hideSingletonLoading(token, pageId);
        pluginApi.showToast(err);
    })
}

export function batchAddFormSku(fieldName, detailFieldMapping) {
    let {product_id, price_book_product_id} = detailFieldMapping || {};
    return [product_id, price_book_product_id].includes(fieldName);
}

export function getRowId(objectData, isRegenerate) {
    let rowId;
    if (isRegenerate) {
        rowId = uuid();
    } else {
        rowId = objectData.key_row_id || uuid();
    }
    objectData.key_row_id = rowId;
    return rowId;
}

export function isTriggerGetRealPrice(bizStateConfig, triggerGetRealPriceResult) {
    let openPriceBook = bizStateConfig && bizStateConfig.isOpenPriceBook();
    let openPriceBookPriority = bizStateConfig && bizStateConfig.isOpenPriceBookPriority();
    let openPriceBookProductValidPeriod = bizStateConfig && bizStateConfig.isOpenPriceBookProductValidPeriod();
    let openPriceBookProductTieredPrice = bizStateConfig && bizStateConfig.isOpenPriceBookProductTieredPrice();
    let openStratifiedPricing = bizStateConfig && bizStateConfig.isOpenStratifiedPricing();
    let isTrigger = !isEmpty(triggerGetRealPriceResult);
    return (openPriceBook && openPriceBookPriority) || openPriceBookProductValidPeriod || openPriceBookProductTieredPrice || openStratifiedPricing ||isTrigger;
}

export function triggerPriceTieredRecordChangedEvent(pluginApi, priceTieredRecord, dataIndex) {
    return pluginApi.executePluginCommandSync('price-service.priceTieredRecord.changed.end.sync', {
        priceTieredRecord,
        dataIndex
    });
}