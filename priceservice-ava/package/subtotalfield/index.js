import fieldcell_b from "../../../objformmain/base/form/fieldcell/fieldcell_b";
import { isEmpty } from "../../../pluginbase-ava/package/pluginutils";
import stratifiedPricingPopModule from "../../../pluginbase-ava/package/modules/StratifiedPricingPopModule";
import fsapi from 'fs-hera-api'
import { fns } from 'ava-metadata-lib/formatDefObj';

Component({
  options: {
    addGlobalClass: true,
  },

  behaviors: [fieldcell_b],

  data: {
    dHooks: undefined,
    dShowIcon: false,
    dPriceTieredRecord: undefined,

    dataIndex: undefined,
  },

  methods: {

    clickIcon() {
      let priceTieredRecord = this.data.dPriceTieredRecord;
      if (isEmpty(priceTieredRecord)) {
        return;
      }
      priceTieredRecord = typeof priceTieredRecord === 'string' ? JSON.parse(priceTieredRecord) : priceTieredRecord
      let { name, id, startCount = [], endCount = [], priceBookPrice = [], priceBookSellingPrice = [], discount = [] } = priceTieredRecord;
      let recordList = [];
      for (let i = 0; i < startCount.length; i++) {
        let start = startCount[i];
        let end = endCount[i];
        if (isEmpty(start)) {
          start = fsapi.i18n.get('advanced.pricing.unlimited.text')/*不限*/;
        }
        if (isEmpty(end)) {
          end = fsapi.i18n.get('advanced.pricing.unlimited.text')/*不限*/;
        }
        recordList.push({
          range: start + '-' + end,
          priceBookPrice: fns.currency(priceBookPrice[i] || '', { api_name: 'price_book_price' }, {}),
          priceBookSellingPrice: fns.currency(priceBookSellingPrice[i] || '', { api_name: 'price_book_selling_price' }, {}),
          discount: fns.percentile(discount[i] || '', { api_name: 'discount' }, {})
        })
      }
      stratifiedPricingPopModule.show({
        title: fsapi.i18n.get('ava.object_form.priceservice.stratified_pricing_detail')/*阶梯价格明细*/,
        fieldLabel: {
          range: fsapi.i18n.get('ava.object_form.priceservice.range')/*数量范围*/,
          priceBookPrice: fsapi.i18n.get('ava.object_form.priceservice.priceBookPrice')/*价目表价格*/,
          priceBookSellingPrice: fsapi.i18n.get('ava.object_form.priceservice.priceBookSellingPrice')/*价目表售价*/,
          discount: fsapi.i18n.get('ava.object_form.priceservice.discount')/*折扣*/
        },
        priceTieredRecord: {
          name,
          id,
          recordList
        }
      });
    },

    onPropChanged(opt) {
      let { getFormContext, getDataDetail, objApiName } = this.data.item;
      let formContext = getFormContext();
      let detail = getDataDetail && getDataDetail() || {};
      let { dData: objectData } = detail || {};
      let fieldMapping = formContext && formContext.catchRunPluginHookSync('price-service.getFieldMapping.sync', {});
      let { price_tiered_record } = fieldMapping.getDetailFields(objApiName) || {};
      let { [price_tiered_record]: priceTieredRecord, dataIndex } = objectData || {};
      this.data.dPriceTieredRecord = priceTieredRecord;
      this.data.dataIndex = dataIndex;
      let showIcon = !isEmpty(priceTieredRecord);
      this.setData({
        dShowIcon: showIcon,
      });
      let self = this;
      let pluginService = formContext.getPluginService();
      pluginService && pluginService.registerCommand('price-service.priceTieredRecord.changed.end.sync', (context, data) => {
        let { priceTieredRecord, dataIndex } = data || {};
        if (dataIndex !== self.data.dataIndex) {
          return;
        }
        self.data.dPriceTieredRecord = priceTieredRecord;
        let showIcon = !isEmpty(priceTieredRecord);
        self.setData({
          dShowIcon: showIcon,
        });
      });
    },
  },

  lifetimes: {
    attached() {
      this.setData({
        dHooks: {
          onPropChanged: this.onPropChanged.bind(this),
        }
      })
    }
  }
});
