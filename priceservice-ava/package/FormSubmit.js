import {key_last_selected_price_book_id} from './utils'

export class FormSubmit {

    constructor(context) {
        let {pluginApi, fieldMapping} = context || {};
        this.pluginApi = pluginApi;
        this.fieldMapping = fieldMapping;
    }

    formSubmitAfter(pluginExecResult, options) {
        let objectData = options.result && options.result.objectData;
        if (objectData) {
            let {form_price_book_id} = this.fieldMapping.getMasterFields();
            let {[form_price_book_id]: price_book_id} = objectData;
            this.pluginApi.setLocal(key_last_selected_price_book_id, price_book_id);
        }
    }
}