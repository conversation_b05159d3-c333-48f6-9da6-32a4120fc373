import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import log from "../../pluginbase-ava/package/log";
import PriceServiceApi from "./PriceServiceApi";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import {FormRender} from "./FormRender";
import {FormSubmit} from "./FormSubmit";
import {MdRender} from "./MdRender";
import {MdBatchAdd} from "./MdBatchAdd";
import {MasterFieldEdit} from "./MasterFieldEdit";
import {DetailFieldEdit} from "./DetailFieldEdit";
import {isEmpty} from "../../pluginbase-ava/package/pluginutils";
import Clone from "./getprice/Clone";

/**
 * 取价插件：http://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
 */
export default class PriceService {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, params, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        this.fieldMapping = new FieldMapping(params, {
            masterFields: {
                form_account_id: 'form_account_id',// 客户id
                form_partner_id: 'form_partner_id',// 合作伙伴id
                form_price_book_id: 'form_price_book_id',// 价目表id
            },
            detailFields: {
                product_price: 'product_price',// 价格
                discount: 'discount',// 折扣
                sales_price: 'sales_price',// 销售单价
                quantity: 'quantity',// 数量
                subtotal: 'subtotal',// 小计
                product_id: 'product_id',// 产品名称
                price_book_product_id: 'price_book_product_id', // 价目表产品
                price_book_id: 'price_book_id',// 价目表id
                price_book_price: 'price_book_price',// 价目表价格
                price_book_discount: 'price_book_discount',// 价目表折扣
                unit: "unit",//单位，引用字段：引用产品对象的单位字段
                actual_unit: 'actual_unit',
                price_tiered_record: 'price_tiered_record',//价格阶梯记录:jsonb
                price_book_subtotal: 'price_book_subtotal',//价目表小计
            }
        })
        let context = {
            fieldMapping: this.fieldMapping,
            bizStateConfig: this.bizStateConfig,
            pluginApi: new PluginApi(pluginService),
            requestApi: new PriceServiceApi(pluginService.api.request),
        };
        this.formRender = new FormRender(context, pluginParam);
        this.formSubmit = new FormSubmit(context);
        this.mdRender = new MdRender(context);
        this.mdBatchAdd = new MdBatchAdd(context);
        this.masterFieldEdit = new MasterFieldEdit(context);
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.calcByRealPrice = new Clone(context);
    }

    formFetchDescribeLayoutAfter(pluginExecResult, options) {
        let {masterObjApiName, describeLayout} = options || {};
        let {layout, objectDescribe} = describeLayout || {};
        let objectApiName = masterObjApiName || (objectDescribe && objectDescribe.api_name);
        if (isEmpty(objectApiName) || objectApiName.endsWith("__c")) {
            return;
        }
        let layoutOwner = null;
        layout && layout.components && layout.components.forEach(com => {
            let {type, field_section} = com || {};
            if (type === "form") {
                field_section && field_section.length && field_section.forEach(section => {
                    section && section.form_fields && section.form_fields.forEach(field => {
                        if (field.field_name === "owner") {
                            layoutOwner = field;
                        }
                    })
                })
            }
        })
        let ownerDescribe = objectDescribe && objectDescribe.fields && objectDescribe.fields.owner;
        let isRequired = ownerDescribe && ownerDescribe.is_required;
        if (isEmpty(layoutOwner) && !isRequired) {// 1. layout中没有owner字段并且owner字段不是必填时，进行上报
            let content = JSON.stringify({object_describe_api_name: objectApiName});
            log.kLog("sfa", 'ownerFieldNotInLayoutAndNotRequired', content);
        }
    }

    formRenderBefore(pluginExecResult, options) {
        return this.formRender.formRenderBefore(pluginExecResult, options)
    }

    formRenderAfter(pluginExecResult, options) {
        return this.formRender.formRenderAfter(pluginExecResult, options)
    }

    formRenderEnd(pluginExecResult, options) {
        return this.formRender.formRenderEnd(pluginExecResult, options)
    }

    formSubmitAfter(pluginExecResult, options) {
        return this.formSubmit.formSubmitAfter(pluginExecResult, options)
    }

    mdRenderBefore(pluginExecResult, options) {
        return this.mdRender.mdRenderBefore(pluginExecResult, options)
    }

    mdBatchAddBefore(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddBefore(pluginExecResult, options)
    }

    mdBatchAddAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddAfter(pluginExecResult, options)
    }

    mdBatchAddEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddEnd(pluginExecResult, options)
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneAfter(pluginExecResult, options)
    }

    mdCloneEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneEnd(pluginExecResult, options)
    }

    fieldEditBefore(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditBefore(pluginExecResult, options);
        } else {
            return this.detailFieldEdit.detailFieldEditBefore(pluginExecResult, options);
        }
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditAfter(pluginExecResult, options);
        } else {
            return this.detailFieldEdit.detailFieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditEnd(pluginExecResult, options);
        }
    }

    updateForcePriority(pluginExecResult, options) {
        let {forcePriority} = options || {};
        if (!isEmpty(forcePriority)) {
            let result = forcePriority ? 1 : 0;
            this.bizStateConfig && this.bizStateConfig.updateBizStateConfig('enforce_priority', result);
        }
    }

    triggerBatchCalc(pluginExecResult, options) {
        let {triggerType = 'triggerBatchCalc', requestSource = null} = options;
        return this.calcByRealPrice.calcPrice(options, requestSource, triggerType);
    }

    getFieldMapping(pluginExecResult, options) {
        return this.fieldMapping;
    }

    apply() {
        return [
            {
                event: "form.fetchDescribeLayout.after",
                functional: this.formFetchDescribeLayoutAfter.bind(this)
            }, {
                event: "form.render.before",
                functional: this.formRenderBefore.bind(this)
            }, {
                event: "form.render.after",
                functional: this.formRenderAfter.bind(this)
            }, {
                event: "form.render.end",
                functional: this.formRenderEnd.bind(this)
            }, {
                event: "form.submit.after",
                functional: this.formSubmitAfter.bind(this)
            }, {
                event: "md.render.before",
                functional: this.mdRenderBefore.bind(this)
            }, {
                event: "md.batchAdd.before",
                functional: this.mdBatchAddBefore.bind(this)
            }, {
                event: "md.batchAdd.after",
                functional: this.mdBatchAddAfter.bind(this)
            }, {
                event: "md.batchAdd.end",
                functional: this.mdBatchAddEnd.bind(this)
            }, {
                event: "md.clone.after",
                functional: this.mdCloneAfter.bind(this)
            }, {
                event: "md.clone.end",
                functional: this.mdCloneEnd.bind(this)
            }, {
                event: "field.edit.before",
                functional: this.fieldEditBefore.bind(this)
            }, {
                event: "field.edit.after",
                functional: this.fieldEditAfter.bind(this)
            }, {
                event: "field.edit.end",
                functional: this.fieldEditEnd.bind(this)
            }, {
                event: "dht.priceBookPriority.update.before",
                functional: this.updateForcePriority.bind(this)
            }, {
                event: "price-service.triggerBatchCalc",
                functional: this.triggerBatchCalc.bind(this)
            }, {
                event: "price-service.getFieldMapping.sync",
                functional: this.getFieldMapping.bind(this)
            }]
    }
}