import {cloneDeep, isEmpty, uuid} from "../../../pluginbase-ava/package/pluginutils";

export default class CalcQuantity {

    constructor(priceServiceContext) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = priceServiceContext || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async process(masterObjectData, detailApiName, skuDataList, newobjectDatas, options) {
        if (!newobjectDatas || !newobjectDatas.length) {
            return;
        }
        let detailDataMapObj = {};
        newobjectDatas = cloneDeep(newobjectDatas);
        newobjectDatas && newobjectDatas.forEach(it => {
            let dataIndex = it.dataIndex;
            if (isEmpty(dataIndex)) {
                dataIndex = options.formApis && options.formApis.createNewDataIndex && options.formApis.createNewDataIndex() || uuid();
                it.dataIndex = dataIndex
            }
            detailDataMapObj[dataIndex] = it;
        });
        let newDataIndexs = newobjectDatas.map(it => it.dataIndex);
        skuDataList.forEach((lookupData, index) => lookupData.dataIndex = newDataIndexs[index]);//为lookupData的dataIndex赋值
        let {quantity} = this.fieldMapping.getDetailFields(detailApiName);
        let pageId = options.dataGetter.getPageId();
        let token = 'price_service_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let calcResult = await this.requestApi.batchCalculate({
            masterObjectApiName: masterObjectData.object_describe_api_name,
            masterData: masterObjectData,
            detailDataMap: {
                [detailApiName]: detailDataMapObj
            },
            modifiedObjectApiName: detailApiName,
            modifiedDataIndexList: newDataIndexs,
            calculateFields: {//计算数量字段
                [detailApiName]: [{
                    fieldName: quantity,
                    order: 1
                }]
            },
            seriesId: options.seriesId
        }).then(rst => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            return rst;
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        });
        let detailResult = calcResult && calcResult[detailApiName];
        if (isEmpty(detailResult)) {
            return;
        }
        return skuDataList.map(lookupData => {
            let dataIndex = lookupData.dataIndex;
            let result = detailResult[dataIndex];//通过dataIndex取结果
            let quantityValue = result && result[quantity] || 1;
            lookupData._selected_num = quantityValue;
            return {[quantity]: quantityValue}
        })
    }
}