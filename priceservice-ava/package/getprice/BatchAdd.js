import {batchAddForm<PERSON>ku, getRealPriceResult, getRowId, isTriggerGetRealPrice} from "../utils";
import {getPriceBookParamAndSimplifyDetails, isEmpty, uuid} from '../../../pluginbase-ava/package/pluginutils'
import {emitEvent} from "../events";

export default class BatchAdd {

    constructor(priceServiceContext) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = priceServiceContext || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    calcPrice(options) {
        let {dataGetter, lookupField, lookupDatas, objApiName, seriesId} = options || {};
        let fieldName = lookupField && lookupField.api_name;
        let detailFields = this.fieldMapping.getDetailFields(objApiName);
        if (!batchAddFormSku(fieldName, detailFields)) {
            return;
        }
        let triggerGetRealPriceResult = this.pluginApi.runPluginSync(emitEvent.priceService_batchAdd_isTriggerGetPrice_sync, Object.assign({}, options));
        if (!isTriggerGetRealPrice(this.bizStateConfig, triggerGetRealPriceResult)) {
            return;
        }
        let {getMasterData} = dataGetter || {};
        let masterData = getMasterData && getMasterData();
        let params = this.getParams(masterData, lookupDatas, seriesId, options);
        if (isEmpty(params)) {
            return;
        }
        this.pluginApi.runPluginSync(emitEvent.priceService_batchAdd_getPriceParam_sync, Object.assign({}, options, {
            params,
            lookupDatas
        }));
        let pageId = dataGetter.getPageId();
        let token = 'price_service_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        return this.requestApi.getRealPrice(params)
            .then(result => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                return this.getRealPriceBackFills(lookupDatas, result, objApiName);
            })
            .catch(err => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                this.pluginApi.showToast(err);
            })
    }

    getParams(masterData, selectedList, seriesId, options) {
        let masterFields = this.fieldMapping.getMasterFields();
        let {form_account_id, form_partner_id} = masterFields;
        let {[form_account_id]: account_id, [form_partner_id]: partner_id} = masterData || {};
        if (!account_id || !selectedList || !selectedList.length) {
            return;
        }
        let fullProductList = selectedList.filter(selected => {
            let {object_describe_api_name, _id, product_id} = selected;
            let productId = (object_describe_api_name === 'ProductObj') ? _id : product_id;
            return !isEmpty(productId);
        }).map(selected => {
            let {object_describe_api_name, _id, product_id, pricebook_id, price_book_id, _selected_num, unit, unit__v} = selected;
            let isProduct = object_describe_api_name === 'ProductObj';
            let productId = isProduct ? _id : product_id;
            let priceBookId = pricebook_id || price_book_id;
            let amount = _selected_num || 1;
            let baseUnit = isProduct ? unit : unit__v;
            let rowId = getRowId(selected, true);
            return {
                productId,
                priceBookId,
                baseUnit: baseUnit,
                amount,
                rowId
            }
        });
        if (!fullProductList || !fullProductList.length) {
            return;
        }
        let object_api_name = masterData && masterData.object_describe_api_name;
        let {masterObjApiName, dataGetter, newDatas} = options || {};
        let detailObjApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {price_book_id} = this.fieldMapping.getDetailFields(detailObjApiName);
        let details = getPriceBookParamAndSimplifyDetails(false, masterObjApiName, detailObjApiName, price_book_id, dataGetter);
        if (!isEmpty(details)) {
            let detailDataList = details[detailObjApiName] || [];
            detailDataList.push(...(newDatas || []));
            details[detailObjApiName] = detailDataList;
        }
        return {
            accountId: account_id,
            partnerId: partner_id,
            requestSource: null,
            fullProductList,
            object_data: masterData,
            object_api_name,
            details,
            seriesId
        };
    }

    getRealPriceBackFills(skuDataList, realPriceResult, objApiName) {
        let backFills = [];
        let changedPriceBookProducts = [];
        let detailFields = this.fieldMapping.getDetailFields(objApiName);
        let {product_price, discount, price_book_product_id, price_book_id, price_book_price, price_book_discount, price_tiered_record, price_book_subtotal} = detailFields || {};
        skuDataList && skuDataList.forEach(skuData => {
            let calcPriceResult = this.getRealPriceResult(skuData, realPriceResult) || {};
            skuData[getRealPriceResult] = calcPriceResult;
            let backFill = this.pluginApi.runPluginSync(emitEvent.priceService_getBackFills_before_sync, {
                objApiName,
                getRealPriceResult: calcPriceResult
            });
            let openPriceBook = this.bizStateConfig.isOpenPriceBook();
            let {_id, name, selling_price, discount: _discount, detail_discount: _detail_discount, pricebook_id, pricebook_id__r, pricebook_price, stand_price, price_tiered_record: priceTieredRecord, price_book_subtotal: priceBookSubtotal} = calcPriceResult;
            let discountValue = isEmpty(_detail_discount) ? _discount : _detail_discount;
            let priceBookRelatedFields = openPriceBook ? Object.assign({
                [discount]: discountValue,
                [price_book_product_id]: _id,
                [`${price_book_product_id}__r`]: name,
                [price_book_id]: pricebook_id,
                [`${price_book_id}__r`]: pricebook_id__r,
                [price_book_price]: pricebook_price,
                [price_book_discount]: _discount,
                basic_price_backup: stand_price,
                [price_tiered_record]: priceTieredRecord
            }, (!isEmpty(priceBookSubtotal)) && {
                [price_book_subtotal]: priceBookSubtotal
            }) : {};
            backFills.push(Object.assign({[product_price]: selling_price}, priceBookRelatedFields, backFill));
            let {object_describe_api_name, _id: orgId} = skuData;
            if (object_describe_api_name === 'PriceBookProductObj' && orgId !== _id) {
                changedPriceBookProducts.push(skuData);
            }
        });
        return {
            backFills,
            changedPriceBookProducts
        }
    }

    getRealPriceResult(selected, realPriceResult) {
        if (!selected || !realPriceResult || !realPriceResult.newRst || !realPriceResult.newRst.length) {
            return;
        }
        let orgRowId = getRowId(selected);
        return realPriceResult.newRst.find(newData => {
            let {rowId} = newData || {};
            return orgRowId === rowId;
        });
    }
}