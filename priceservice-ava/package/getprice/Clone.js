import {getRowId, triggerPriceTieredRecordChangedEvent} from "../utils";
import {emitEvent} from "../events";
import {
    equals,
    formatDataDecimalPlaces,
    getPriceBookParamAndSimplifyDetails,
    i18n,
    isEmpty,
    uuid
} from "../../../pluginbase-ava/package/pluginutils";

export default class Clone {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    calcPrice(options, requestSource, triggerType) {
        let {dataGetter, seriesId, masterData, triggerDataIndexList, triggerObjectDataList, doNotUpdateData = false, fieldMapping} = options;
        if (isEmpty(masterData)) {
            masterData = dataGetter && dataGetter.getMasterData && dataGetter.getMasterData();
        }
        let masterFields = this.getMasterFields(fieldMapping);
        let {form_account_id} = masterFields;
        let {[form_account_id]: accountId} = masterData;
        if (!accountId) {
            return Promise.resolve();
        }
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailDataList;
        if (triggerObjectDataList && triggerObjectDataList.length) {
            detailDataList = triggerObjectDataList;
        } else {
            detailDataList = dataGetter && dataGetter.getDetail && dataGetter.getDetail(objApiName);
            if (triggerDataIndexList && triggerDataIndexList.length) {
                detailDataList = detailDataList.filter(detailData => {
                    return triggerDataIndexList.includes(detailData.dataIndex)
                })
            }
        }
        let params = this.buildParams(objApiName, requestSource, masterData, detailDataList, seriesId, options, triggerType);
        if (params) {
            return this.getRealPrice(params, objApiName, options, detailDataList, triggerType, doNotUpdateData);
        }
        return Promise.resolve();
    }

    calcPriceByFieldChanged(options, fieldName, changeData) {
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        if (!openPriceBook) {//未开启价目表，批量添加产品，更换客户、价目表、日期字段，不走取价逻辑
            return Promise.resolve();
        }
        let {dataGetter, seriesId, fieldMapping} = options;
        let masterData = dataGetter && dataGetter.getMasterData && dataGetter.getMasterData();
        let masterFields = this.getMasterFields(fieldMapping);
        let {form_account_id, form_price_book_id} = masterFields;
        Object.assign(masterData, changeData || {});
        let {[form_account_id]: accountId} = masterData;
        if (!accountId) {
            return Promise.resolve();
        }
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailDataList = dataGetter && dataGetter.getDetail && dataGetter.getDetail(objApiName);
        let params = this.buildParams(objApiName, null, masterData, detailDataList, seriesId, options, 'masterFieldEdit', changeData);
        if (params) {
            return this.getRealPrice(params, objApiName, options, detailDataList, 'masterFieldEdit');
        }
        return Promise.resolve();
    }

    buildParams(objApiName, requestSource, masterData, detailDataList, seriesId, options, triggerType, changeData) {
        let {form_account_id, form_partner_id} = this.getMasterFields(options.fieldMapping);
        let {product_id, price_book_id, quantity, unit, product_price, discount} = this.getDetailFields(objApiName, options.fieldMapping);
        let {[form_account_id]: accountId, [form_partner_id]: partnerId, object_describe_api_name} = masterData || {};
        let fullProductList = detailDataList && detailDataList.length && detailDataList.filter(it => {
            let doNotCalcPrice = this.detailDataDoNotCalcPrice(it, options, objApiName, triggerType);
            return !doNotCalcPrice;
        }).map(it => {
            let {
                [product_id]: productId, [price_book_id]: priceBookId, [quantity]: _quantity, [`${unit}__v`]: baseUnit,
                [product_price]: price, [discount]: _discount
            } = it;
            let fullProductInfo = this.pluginApi.runPluginSync(emitEvent.priceService_form_parseFullProduct_sync, Object.assign({}, options, {
                objApiName,
                detailData: it,
                type: triggerType
            }));
            let priceBookChanged = false;
            let changedPriceBookId;
            if (!isEmpty(changeData)) {
                changedPriceBookId = changeData[price_book_id]
                priceBookChanged = !(typeof changedPriceBookId === 'undefined');
            }
            let rowId = getRowId(it, true);
            return Object.assign({
                productId: productId,
                priceBookId: priceBookChanged ? changedPriceBookId : priceBookId,
                baseUnit: baseUnit,
                amount: _quantity,
                discount: _discount,
                price: price,
                rowId,
            }, fullProductInfo);
        });
        if (!fullProductList || !fullProductList.length) {
            return;
        }
        let {masterObjApiName, dataGetter} = options;
        let params = {
            accountId: accountId,
            partnerId: partnerId,
            requestSource: requestSource,
            fullProductList,
            object_data: masterData,
            object_api_name: object_describe_api_name,
            details: getPriceBookParamAndSimplifyDetails(false, masterObjApiName, objApiName, price_book_id, dataGetter),
            seriesId
        };
        this.pluginApi.runPluginSync(emitEvent.priceService_form_getPriceParam_sync, Object.assign({}, options, {
            objApiName,
            params
        }));
        return params;
    }

    async getRealPrice(params, objApiName, options, detailDataList, triggerType, doNotUpdateData) {
        let pageId = options.dataGetter.getPageId();
        let token = 'price_service_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let calcResult = await this.requestApi.getRealPrice(params)
            .then(result => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                return result;
            }).catch(err => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                this.pluginApi.showToast(err);
            });
        let updateResult = await this.handleUpdate(objApiName, options, calcResult, detailDataList, triggerType, doNotUpdateData).catch(err => {
            this.pluginApi.showToast(err);
        });
        let deleteResult = await this.handleDelete(objApiName, options, calcResult, detailDataList, triggerType, doNotUpdateData).catch(err => {
            this.pluginApi.showToast(err);
        });
        if (!doNotUpdateData) {
            this.showHandledMessage(Object.assign({}, updateResult, deleteResult));
        }
        return {getRealPriceResult: calcResult, updateResult, deleteResult};
    }

    handleUpdate(objApiName, options, calcResult, detailDataList, triggerType, doNotUpdateData) {
        let {applicablePriceSystem, calculatePrice} = calcResult || {};
        if (!applicablePriceSystem) {//不使用取价接口返回的价格，不处理，直接返回
            return Promise.resolve({
                priceChanged: false,//价格是否有变化
                hasPackageProduct: false//是否有产品包数据
            });
        }
        let detailFields = this.getDetailFields(objApiName, options.fieldMapping);
        let {price_book_id, product_price, discount, sales_price, subtotal, price_book_price, price_book_discount, price_book_product_id, price_tiered_record, price_book_subtotal} = detailFields;
        let {dataGetter, dataUpdater, formApis} = options;
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        let openStratifiedPricing = this.bizStateConfig.isOpenStratifiedPricing();
        let priceChanged = false;
        let modifiedIndexList = [];
        let updateResult = {};
        detailDataList && detailDataList.length && detailDataList.forEach(detail => {
            let {[product_price]: orgPrice, dataIndex, [price_book_id]: priceBookId} = detail;
            let doNotCalcPrice = this.detailDataDoNotCalcPrice(detail, options, objApiName, triggerType);
            if (doNotCalcPrice) {
                return;
            }
            let result = this.getRealPriceResult(objApiName, detail, calcResult);
            if (!result) {
                return;
            }
            let ignoreCalcResult = this.ignoreCalcResult(detail, result, options, objApiName, triggerType);
            if (ignoreCalcResult) {
                return;
            }
            let {_id, name, discount: _discount, detail_discount: _detail_discount, pricebook_id, pricebook_id__r, pricebook_price, selling_price: sellingPrice, price_tiered_record: priceTieredRecord, price_book_subtotal: priceBookSubtotal} = result;
            let discountValue = isEmpty(_detail_discount) ? _discount : _detail_discount;
            let orgPriceNum = orgPrice ? Number(orgPrice) : 0;
            let sellingPriceNum = sellingPrice ? Number(sellingPrice) : 0;
            let needUpdatePrice = true;
            if (triggerType === 'masterFieldEdit') {
                needUpdatePrice = (!equals(priceBookId, pricebook_id));
            }
            if (needUpdatePrice && (orgPriceNum != sellingPriceNum)) {
                priceChanged = true;
            }
            let updateObj = Object.assign({}, needUpdatePrice && {
                [product_price]: sellingPrice
            }, openPriceBook && {
                [price_book_price]: pricebook_price,
                [price_book_discount]: _discount,
                [price_book_product_id]: _id,
                [`${price_book_product_id}__r`]: name,
                [discount]: discountValue,
                [price_book_id]: pricebook_id,
                [`${price_book_id}__r`]: pricebook_id__r,
                [price_tiered_record]: priceTieredRecord,
            }, (!isEmpty(priceBookSubtotal)) && {
                [price_book_subtotal]: priceBookSubtotal
            });
            if (openPriceBook) {
                triggerPriceTieredRecordChangedEvent(this.pluginApi, priceTieredRecord, dataIndex);
            }
            let backFill = this.pluginApi.runPluginSync(emitEvent.priceService_getBackFills_before_sync, Object.assign({}, options, {
                objApiName,
                detailData: detail,
                getRealPriceResult: result
            }));
            Object.assign(updateObj, backFill);
            formatDataDecimalPlaces(updateObj, objectDescribe);
            Object.assign(detail, updateObj);
            Object.assign(updateResult, {[dataIndex]: updateObj});
            if (!doNotUpdateData) {
                dataUpdater && dataUpdater.updateDetail(objApiName, dataIndex, updateObj);
                modifiedIndexList.push(dataIndex);
            }
        })
        if ((!doNotUpdateData) && modifiedIndexList.length) {
            let openPricePolicy = this.bizStateConfig.isOpenPricePolicy();
            let changeFields = openPricePolicy ? [product_price, price_book_price, price_book_discount] : [product_price, discount, sales_price, subtotal];
            let extraFields = [sales_price, subtotal];//销售单价、小计也参与计算
            if (calculatePrice) {//重新计算价格、折扣
                extraFields.push(product_price, discount);
            }
            let filterFields;
            if (openStratifiedPricing) {//开启了分层定价，价目表小计不参与计算
                filterFields = {[objApiName]: [price_book_subtotal]};
            }
            return formApis.triggerCalAndUIEvent({
                objApiName: objApiName,
                extraFields: {[objApiName]: extraFields},
                modifiedDataIndexs: modifiedIndexList,
                changeFields,
                filterFields,
            }).then(() => {
                return {
                    priceChanged: priceChanged,//价格是否有变化
                    hasPackageProduct: false,//是否有产品包数据
                    updateResult
                }
            })
        }
        return Promise.resolve({
            priceChanged: false,//价格是否有变化
            hasPackageProduct: false,//是否有产品包数据
            updateResult
        });
    }

    handleDelete(objApiName, options, calcResult, detailDataList, triggerType, doNotUpdateData) {
        let {applicablePriceSystem} = calcResult || {};
        if (!applicablePriceSystem) {//不使用取价接口返回的价格，不处理，直接返回
            return Promise.resolve({});
        }
        let removedProductNames = [];
        let deleteDataIndexList = [];
        let {product_id} = this.getDetailFields(objApiName, options.fieldMapping);
        detailDataList && detailDataList.length && detailDataList.forEach(detailData => {
            let doNotCalcPrice = this.detailDataDoNotCalcPrice(detailData, options, objApiName, triggerType);
            if (doNotCalcPrice) {
                return;
            }
            let result = this.getRealPriceResult(objApiName, detailData, calcResult);
            if (!result) {
                let {[`${product_id}__r`]: productName} = detailData || {};
                removedProductNames.push(productName);
                deleteDataIndexList.push(detailData.dataIndex);
            }
        })
        let {dataUpdater, formApis} = options;
        if (deleteDataIndexList.length) {
            let result = this.pluginApi.runPluginSync(emitEvent.priceService_deleteDetail_after_sync, Object.assign({}, options, {
                objApiName,
                deleteDataIndexList,//将要删除的明细的dataIndex列表
            }));
            if (!isEmpty(result)) {
                let {deleteDataIndexs} = result;
                if (deleteDataIndexs && deleteDataIndexs.length) {
                    deleteDataIndexList.push(...deleteDataIndexs);
                }
            }
            if (!doNotUpdateData) {
                let delDatas = dataUpdater && dataUpdater.del(objApiName, deleteDataIndexList);
                return formApis.triggerCalAndUIEvent({
                    objApiName,
                    delDatas
                }).then(() => {
                    return {removedProductNames, deleteDataIndexList};
                })
            }
        }
        return Promise.resolve({removedProductNames, deleteDataIndexList});
    }

    showHandledMessage(handleResult) {
        let jsonString = this.pluginApi.getCloudCtrl("disableGetRealPriceTip");
        let result = jsonString && JSON.parse(jsonString);
        let disableTip = result && result.enable;
        if (disableTip) {
            return;
        }
        let tips = [];
        let {priceChanged, hasPackageProduct, removedProductNames} = handleResult || {};
        if (priceChanged) {
            let openPriceBook = this.bizStateConfig.isOpenPriceBook();
            let tip1 = i18n('ava.object_form.onsale.getrealprice.new_price_tip_1')/*当前产品价格为可售范围对应的价目表的最新价格！*/;
            let tip2 = i18n('ava.object_form.onsale.getrealprice.new_price_tip_2')/*当前产品价格为可售范围对应的产品的最新价格！*/;
            tips.push(openPriceBook ? tip1 : tip2);
        }
        if (hasPackageProduct) {
            let tip = i18n('ava.object_form.onsale.getrealprice.product_pkg_tip')/*组合产品须点击“配置”获取最新配置价格！*/;
            tips.push(tip);
        }
        if (removedProductNames && removedProductNames.length) {
            let removedInfo = removedProductNames.join('；');
            let tip = i18n('ava.object_form.onsale.getrealprice.delete_tip', [removedInfo])/*产品：{0} 未在该客户可售范围中，已移除！*/;
            tips.push(tip);
        }
        if (tips.length) {
            let message = '';
            tips.forEach(tip => {
                message += (tip + '\n');
            });
            this.pluginApi.alert(message);
        }
    }

    getRealPriceResult(objApiName, detailObjectData, realPriceResult) {
        if (!detailObjectData || !realPriceResult || !realPriceResult.newRst || !realPriceResult.newRst.length) {
            return;
        }
        let orgRowId = getRowId(detailObjectData);
        return realPriceResult.newRst.find(newData => {
            let {rowId} = newData || {};
            return orgRowId === rowId;
        });
    }

    detailDataDoNotCalcPrice(detailData, options, objApiName, triggerType) {
        let {product_id} = this.getDetailFields(objApiName, options.fieldMapping);
        let productId = detailData[product_id];
        if (isEmpty(productId)) {//产品id为空不参与取价
            return true;
        }
        let doNotCalcPriceResult = this.pluginApi.runPluginSync(emitEvent.priceService_form_detailDataDoNotCalcPrice_sync, Object.assign({}, options, {
            objApiName,
            detailData,
            action: triggerType || 'clone'
        }));//{cpq:true}
        return !isEmpty(doNotCalcPriceResult);
    }

    /**
     * 当前明细数据不使用取价结果
     */
    ignoreCalcResult(detailData, calcResult, options, objApiName, triggerType) {
        let result = this.pluginApi.runPluginSync(emitEvent.priceService_form_ignoreCalcResult_sync, Object.assign({}, options, {
            objApiName,
            detailData,
            calcResult,
            action: triggerType
        }));
        if (typeof result === "boolean") {
            return result;
        }
        return false;
    }

    getMasterFields(fieldMapping) {
        let defMasterFields = this.fieldMapping.getMasterFields();
        return Object.assign({}, defMasterFields, fieldMapping && fieldMapping.masterFields);
    }

    getDetailFields(objApiName, fieldMapping) {
        let defDetailFields = this.fieldMapping.getDetailFields(objApiName);
        return Object.assign({}, defDetailFields, fieldMapping && fieldMapping.detailFields);
    }

}