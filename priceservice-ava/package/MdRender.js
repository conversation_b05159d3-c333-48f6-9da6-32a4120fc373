import {getEa, i18n, isEmpty} from "../../pluginbase-ava/package/pluginutils";
import log from "../../pluginbase-ava/package/log";

export class MdRender {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.supportManualAdd = undefined;//是否支持手动添加
        this.isBatchAddFieldsUseLayoutConfig = {};//是否走特殊添加入口逻辑
    }

    mdRenderBefore(pluginExecResult, options) {
        let self = this;
        let preData = pluginExecResult && pluginExecResult.preData;
        let preHandleNormalButtons = preData && preData.handleNormalButtons || [];
        let handleBatchButtons = preData && preData.handleBatchButtons || [];
        let handleSingleButtons = preData && preData.handleSingleButtons || [];
        return Object.assign({}, preData, {
            handleNormalButtons: [...preHandleNormalButtons, (opt) => {
                opt = opt || {};
                let {objApiName, dataGetter} = options;
                let openPriceBook = self.bizStateConfig.isOpenPriceBook();
                let openPriceBookPriority = self.bizStateConfig.isOpenPriceBookPriority();
                let {product_id, price_book_product_id} = self.fieldMapping.getDetailFields(objApiName);
                let supportManualAdd = self.isSupportManualAdd();
                let supportFieldName = openPriceBook ? (openPriceBookPriority ? product_id : price_book_product_id) : undefined;
                //先保留订单产品从销售合同产品添加入口、销售合同明细从报价单明细添加入口，后面把这个逻辑迁移到对象插件上
                let objFields = {
                    "SalesOrderProductObj": ['sale_contract_line_id'],
                    "SaleContractLineObj": ['quote_line_id']
                }[objApiName];
                opt.buttons = (opt.buttons || []).filter(it => {
                    if (it.action === "Single_Add") {
                        return supportManualAdd || !openPriceBook;//开启价目表不支持手动添加;
                    } else if (it.action === "Batch_Lookup_Add") {
                        if (objFields && objFields.includes(it.lookup_field_name)) {
                            return true;
                        }
                        if (supportFieldName) {
                            return it.lookup_field_name === supportFieldName;
                        }
                        return true;
                    }
                });
                let entrySource = dataGetter.getEntrySource && dataGetter.getEntrySource();
                let flowForm = ['BpmEditForm', 'ApproveEditForm', 'StageEditForm'].includes(entrySource);
                if (openPriceBook && !flowForm) {//开了价目表并且不是流程布局
                    let layoutFields = dataGetter.getLayoutFields && dataGetter.getLayoutFields(objApiName, opt.recordType);
                    let productField = layoutFields && layoutFields[product_id];
                    let targetFieldName = openPriceBookPriority ? product_id : price_book_product_id;
                    let hasTargetButton = opt.buttons.find(it => it.lookup_field_name === targetFieldName);
                    let useLayoutConfig = self.isOrderProductBatchAddFieldsUseLayoutConfig(objApiName);
                    if (!hasTargetButton && productField && !useLayoutConfig) {//未找到按钮且布局中有产品字段，则特殊添加一个入口
                        log.kLog("sfa", 'o2c-log:', 'addPriceBookProductFieldForBatchAdd');
                        let describe = dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
                        let targetFieldDesc = describe && describe.fields && describe.fields[targetFieldName];
                        let fieldLabel = targetFieldDesc && targetFieldDesc.label
                        opt.buttons.push({
                            lookup_field_name: targetFieldName,
                            label: i18n('ava.object_form.add_from_param', [fieldLabel])/*从{0}添加*/,
                            action: "Batch_Lookup_Add",
                            api_name: `Batch_Lookup_Add_button_${targetFieldName}`
                        })
                    }
                }
                opt.buttons.forEach(it => {
                    it.notFilterReadOnly = true;
                })
                return opt;
            }],

            handleBatchButtons: [...handleBatchButtons, opt => {
                let allowOrderProductCopy = self.bizStateConfig.isAllowOrderProductCopy();
                opt.buttons = (opt.buttons || []).filter(it => {
                    return it.action === "Clone" ? allowOrderProductCopy : true;
                });
                return opt;
            }],

            handleSingleButtons: [...handleSingleButtons, opt => {
                let allowOrderProductCopy = self.bizStateConfig.isAllowOrderProductCopy();
                opt.buttons = (opt.buttons || []).filter(it => {
                    return it.action === "Clone" ? allowOrderProductCopy : true;
                });
                return opt;
            }]
        })
    }

    isSupportManualAdd() {
        let supportManualAdd = this.supportManualAdd;
        if (!isEmpty(supportManualAdd)) {
            return supportManualAdd;
        }
        let obj = this.pluginApi.getCloudCtrl("onSaleDetailObjSupportManualAdd");
        let result = obj && JSON.parse(obj);
        supportManualAdd = result && result.enable || false;
        this.supportManualAdd = supportManualAdd;
        return supportManualAdd;
    }

    /**
     * 订单产品批量添加入口是否走布局配置（如果走布局配置，就不走写死的业务逻辑）
     * @return {boolean} true:走布局配置
     */
    isOrderProductBatchAddFieldsUseLayoutConfig(objApiName) {
        let ea = getEa();
        let isBatchAddFieldsUseLayoutConfig = this.isBatchAddFieldsUseLayoutConfig;
        let preResult = isBatchAddFieldsUseLayoutConfig[ea];
        if (!isEmpty(preResult)) {
            return preResult;
        }

        let jsonString = this.pluginApi.getCloudCtrl('isOrderProductBatchAddFieldsUseLayoutConfig');
        let obj = jsonString && JSON.parse(jsonString);
        let eaList = obj && obj.eaList;
        let isAllEa = eaList && eaList.includes('all');
        if (isAllEa) {
            isBatchAddFieldsUseLayoutConfig[ea] = true;
            return true;
        }
        let isWhiteEa = eaList && eaList.includes(ea) || false;
        isBatchAddFieldsUseLayoutConfig[ea] = isWhiteEa;
        return isWhiteEa;
    }
}