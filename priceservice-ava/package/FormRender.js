import Clone from '../package/getprice/Clone'
import {checkPriceBook, isNeedCheckPriceBook, key_last_selected_price_book_id} from './utils'
import {
    getPriceBookParamAndSimplifyDetails,
    i18n,
    isClone,
    isConvert,
    isDraft,
    isEdit,
    isEmpty,
    uuid
} from "../../pluginbase-ava/package/pluginutils";
import {emitEvent} from "./events";

export class FormRender {

    constructor(context, pluginParam) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.calcByRealPrice = new Clone(context);
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
        this.pluginParam = pluginParam;
    }

    formRenderBefore(pluginExecResult, options) {
        let openStratifiedPricing = this.bizStateConfig.isOpenStratifiedPricing();
        if (!openStratifiedPricing) {//未开启分层定价，不走分层定价组件
            return;
        }   
        let self = this;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let { subtotal } = this.fieldMapping.getDetailFields(objApiName);
        return {
            detail_field_components: {
                [objApiName]: {
                    all: {
                        [subtotal]: {
                            resource: "objformplugin-priceservice/package/subtotalfield/index",
                            prop: {
                                pluginInfo: self.pluginParam.describe,//固定传
                            }
                        }
                    }
                }
            }
        }
    }

    async formRenderAfter(pluginExecResult, options) {
        this.initIsNeedCheckPriceBook(options);
        await this.checkPriceBookOrGetDefault(options);
        await this.triggerGetRealPrice(options);
    }

    formRenderEnd(pluginExecResult, options) {
        this.setDetailDataReadonlyFields(options);
        //插件服务未请求这个配置，单独请求下
        this.requestApi.getUserConfig('whether_filter_order_select_product').then(rst => {
            this.bizStateConfig.updateBizStateConfig('whether_filter_order_select_product', rst);
        }).catch(err => {
            this.pluginApi.showToast(err);
        })
    }

    initIsNeedCheckPriceBook(options) {
        let {dataGetter, dataUpdater} = options;
        let masterData = dataGetter && dataGetter.getMasterData && dataGetter.getMasterData();
        let {form_price_book_id} = this.fieldMapping.getMasterFields();
        let {[form_price_book_id]: price_book_id} = masterData || {};
        price_book_id && dataUpdater && dataUpdater.updateMaster && dataUpdater.updateMaster({
            [isNeedCheckPriceBook]: true
        });
    }

    setDetailDataReadonlyFields(options) {
        let readonlyFields = [];
        let {dataUpdater} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {product_id, price_book_product_id, price_book_id} = this.fieldMapping.getDetailFields(objApiName);
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        let openPriceBookPriority = this.bizStateConfig.isOpenPriceBookPriority();
        openPriceBook && readonlyFields.push(product_id);//开启价目表，产品名称字段只读
        openPriceBookPriority && readonlyFields.push(price_book_product_id, price_book_id);//强制执行价目表优先级时，价目表产品、价目表字段只读
        dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
            objApiName,
            dataIndex: 'all',
            fieldName: readonlyFields,
            biz: 'price-service',
            priority: 11
        });
    }

    checkPriceBookOrGetDefault(options) {
        let {dataGetter, dataUpdater, triggerCalFields} = options;
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        if (!sourceAction) {
            return Promise.resolve();
        }
        let {getMasterData} = dataGetter || {};
        let masterData = getMasterData && getMasterData();
        let {form_account_id, form_price_book_id} = this.fieldMapping.getMasterFields();
        let {[form_account_id]: account_id, [form_price_book_id]: price_book_id} = masterData;
        let isOpenPriceBook = this.bizStateConfig.isOpenPriceBook();
        if (!isOpenPriceBook || !account_id) {
            return Promise.resolve();
        }
        if (price_book_id) {//价目表字段有值，校验价目表
            return checkPriceBook({
                fieldMapping: this.fieldMapping,
                pluginApi: this.pluginApi,
                requestApi: this.requestApi
            }, options).then(available => {
                let updateData = {
                    [isNeedCheckPriceBook]: false
                };
                if (!available) {
                    Object.assign(updateData, {
                        [form_price_book_id]: null,
                        [`${form_price_book_id}__r`]: null,
                    })
                    this.pluginApi.showToast(i18n('ava.object_form.onsale.pricebook_not_avaliable_tip_info')/*当前价目表不可用已清空，请知悉！*/);
                    triggerCalFields && triggerCalFields.push(form_price_book_id);
                }
                dataUpdater && dataUpdater.updateMaster && dataUpdater.updateMaster(updateData);
            })
        }
        let isEditMode = isEdit(sourceAction);
        return isEditMode
            ? Promise.resolve()//编辑时，价目表字段无值，不需要获取默认价目表
            : this.getDefaultPriceBook(options).then(result => {//新建时价目表字段无值，获取默认价目表
                if (!isEmpty(result)) {
                    let {_id, name, name__r} = result;
                    dataUpdater && dataUpdater.updateMaster && dataUpdater.updateMaster({
                        [form_price_book_id]: _id,
                        [`${form_price_book_id}__r`]: name__r || name,
                        [isNeedCheckPriceBook]: false
                    });
                    triggerCalFields && triggerCalFields.push(form_price_book_id);
                }
            });
    }

    triggerGetRealPrice(options) {
        let triggerCalcResult = this.pluginApi.runPluginSync(emitEvent.priceService_triggerCalc_before_sync, Object.assign({}, options, {
            scene: 'formRender'
        }));
        if (triggerCalcResult && triggerCalcResult.doNotTrigger) {
            return Promise.resolve();
        }
        let {dataGetter, masterObjApiName} = options;
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        let masterData = dataGetter && dataGetter.getMasterData && dataGetter.getMasterData();
        let isFromShoppingCart = masterData && masterData.form_source === 'dht_cart_obj';//从购物车进订单，不走取价
        let isEditMode = isEdit(sourceAction);
        if (!sourceAction || isEditMode || isFromShoppingCart) {
            return Promise.resolve();
        }
        let jsonString = this.pluginApi.getCloudCtrl("ConvertNotNeedCalculate");
        let result = jsonString && JSON.parse(jsonString);
        let convertNotNeedCalculate = result && result.notNeedCalculate;
        if (isConvert(sourceAction) && convertNotNeedCalculate) {
            return Promise.resolve();
        }
        let requestSource;
        let isCloneMode = isClone(sourceAction);
        let isDraftMode = isDraft(sourceAction);
        if (isCloneMode || isDraftMode) {
            requestSource = 'copy';
        } else if (isConvert(sourceAction)) {
            requestSource = 'convert';
        }
        if (!requestSource) {
            return Promise.resolve();
        }
        let applicablePriceSystem = this.bizStateConfig.applicablePriceSystem(requestSource, masterObjApiName);
        console.log(`price-service: requestSource:${requestSource} masterObjApiName:${masterObjApiName} applicablePriceSystem:${applicablePriceSystem}`)
        if (!applicablePriceSystem) {
            return Promise.resolve();
        }
        return this.calcByRealPrice.calcPrice(options, requestSource, requestSource).then(calcResult => {
            this.pluginApi.runPluginSync(emitEvent.priceService_triggerCalc_end_sync, Object.assign({}, options, {
                calcResult,
                scene: 'formRender'
            }));
            return calcResult;
        });
    }

    getDefaultPriceBook(options) {
        let {masterObjApiName, dataGetter} = options;
        let {getMasterData, getLayoutFields} = dataGetter || {};
        let layoutFields = getLayoutFields && getLayoutFields(masterObjApiName);
        let {form_account_id, form_price_book_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let priceBookField = layoutFields[form_price_book_id];
        let isRequired = priceBookField && priceBookField.is_required;
        if (isRequired) {//价目表字段必填，获取默认价目表
            let masterData = getMasterData && getMasterData();
            let lastPriceBookId = this.pluginApi.getLocal(key_last_selected_price_book_id)
            let detailObjApiName = this.fieldMapping.getFirstDetailObjApiName();
            let params = {
                account_id: masterData[form_account_id],
                price_book_id: lastPriceBookId,
                partner_id: masterData[form_partner_id],
                object_data: masterData,
                details: getPriceBookParamAndSimplifyDetails(true, masterObjApiName, detailObjApiName, form_price_book_id, dataGetter)
            };
            let pageId = dataGetter.getPageId();
            let token = 'price_service_' + uuid();
            this.pluginApi.showSingletonLoading(token, {}, pageId);
            return this.requestApi.getDefaultPriceBook(params)
                .then(result => {
                    this.pluginApi.hideSingletonLoading(token, pageId);
                    return result;
                }).catch(err => {
                    this.pluginApi.hideSingletonLoading(token, pageId);
                    this.pluginApi.showToast(err);
                });
        }
        return Promise.resolve();
    }
}