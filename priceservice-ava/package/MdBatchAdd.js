import BatchAdd from "../package/getprice/BatchAdd";
import {emitEvent} from "./events";
import {
    appendFilters2Where,
    batchAddFormSku,
    checkPriceBook,
    filterMasterObjectData,
    isNeedCheckPriceBook,
    isTriggerGetRealPrice
} from "./utils";
import {
    cloneDeep,
    each,
    formatDataDecimalPlaces,
    formatValueDecimalPlaces,
    i18n,
    isEmpty
} from '../../pluginbase-ava/package/pluginutils'
import CalcQuantity from "../package/getprice/CalcQuantity";

export class MdBatchAdd {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.calcByRealPrice = new BatchAdd(context);
        this.calcQuantity = new CalcQuantity(context);
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async mdBatchAddBefore(pluginExecResult, options) {
        let {masterObjApiName, dataGetter, dataUpdater, formApis, objApiName, selectObjectParams, recordType, lookupField, addOpt} = options;
        let fieldName = lookupField && lookupField.api_name;
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        if (!batchAddFormSku(fieldName, fieldMapping)) {
            return;
        }
        let {getLayoutFields, getMasterData, getDetail} = dataGetter || {};
        let masterLayoutFields = getLayoutFields && getLayoutFields(masterObjApiName);
        let masterData = getMasterData && getMasterData();
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        let openAvailableRange = this.bizStateConfig.isOpenAvailableRange();
        let {form_account_id, form_price_book_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {[form_price_book_id]: priceBookField, [form_account_id]: accountField} = masterLayoutFields || {};
        let priceBookFieldRequired = priceBookField && priceBookField.is_required;
        let {
            [form_account_id]: accountId, [form_price_book_id]: priceBookId, [`${form_price_book_id}`]: priceBookName, [form_partner_id]: partner_id,
            [isNeedCheckPriceBook]: needCheckPriceBook
        } = masterData;
        if (openPriceBook && priceBookFieldRequired) {//开启了价目表并且价目表必填需要先选价目表
            if (!priceBookId) {
                let msg = i18n('ava.object_form.plese_select_first', [priceBookField.label])/*请先选择{0}*/;
                formApis && formApis.focusShowMasterFieldError && formApis.focusShowMasterFieldError(form_price_book_id, msg, true);
                return {consumed: true};
            }
        }
        if (accountField && (openPriceBook || openAvailableRange)) {//开启了价目表或者可售范围需要先选客户
            if (!accountId) {
                let msg = i18n('ava.object_form.plese_select_first', [accountField.label])/*请先选择{0}*/;
                formApis && formApis.focusShowMasterFieldError && formApis.focusShowMasterFieldError(form_account_id, msg, true);
                return {consumed: true};
            }
        }
        if (openPriceBook && needCheckPriceBook) {
            dataUpdater && dataUpdater.updateMaster && dataUpdater.updateMaster({[isNeedCheckPriceBook]: false});
            let available = await checkPriceBook({
                fieldMapping: this.fieldMapping,
                pluginApi: this.pluginApi,
                requestApi: this.requestApi
            }, options);
            if (!available) {
                this.pluginApi.showToast(i18n('ava.object_form.onsale.validate_account_tip')/*已选价目表不适用于当前用户或客户，无法添加产品*/);
                return {consumed: true};
            }
        }
        let {product_price, product_id, quantity, price_book_id} = fieldMapping;
        let isSelectProduct = fieldName === product_id;
        let detailDataList = getDetail && getDetail(objApiName);
        let selectedProductIds = recordType && detailDataList && detailDataList.filter(detail => {
            let {record_type} = detail || {};
            let isFilter = true;
            let result = this.pluginApi.runPluginSync(emitEvent.priceService_filterProduct_before_sync, {
                objectData: detail,
                objApiName
            });
            if (typeof result == 'boolean') {
                isFilter = result;
            }
            return record_type === recordType && isFilter;
        }).map(it => it[product_id]) || [];
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let priceBookFieldDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[price_book_id];
        let detailLayoutFields = getLayoutFields && getLayoutFields(objApiName, recordType);
        let {[product_price]: productPriceField, [quantity]: quantityField} = detailLayoutFields || {};
        let {default_is_expression, default_value, is_readonly, step_value} = quantityField || {};
        if (isEmpty(default_value) || default_value <= 0) {
            default_value = 1;
        }
        let chooseBySpu = await this.chooseSPU();
        let multiSpecDisplayStyle = this.bizStateConfig.multiSpecDisplayStyle();
        let enableRecentOrder = this.bizStateConfig.enableRecentOrder();
        let enableInputCustomFields = this.bizStateConfig.enableInputCustomFields();
        let openPriceBookPriority = this.bizStateConfig.isOpenPriceBookPriority();
        let enableSelectSameProduct = this.bizStateConfig.enableSelectSameProductInDifferentPriceBooks();
        let mobileBottomSummarySetting = this.bizStateConfig.mobileBottomSummarySetting();
        let isOpenPricePolicy = this.bizStateConfig.isOpenPricePolicy();
        let useShopCategory = this.bizStateConfig.useShopCategory();
        let isSalesOrder = masterObjApiName === 'SalesOrderObj';//是否是销售订单
        let describeLayout = dataGetter.getDescribeLayout()
        const formLayoutApiName = describeLayout && describeLayout.layout && describeLayout.layout.api_name;
        const formLayoutDisplayName = describeLayout && describeLayout.layout && describeLayout.layout.display_name;

        let selectSKUConfig = {
            enforcePriority: openPriceBookPriority,//是否强制执行价目表优先级
            isOpenPriceBook: openPriceBook,//是否开启了价目表
            isOpenPricePolicy,
            selectBySPU: chooseBySpu,//是否通过商品选产品
            multiSpecDisplayStyle,//多规格三样式模式
            showQuantityInput: true,//是否展示数量输入框
            isCreateScene: true,//新建订单or编辑订单，get_available_price_book_list接口会用到
            enableChangePriceBook: true,//是否支持切换价目表
            selectedProductIds: selectedProductIds,//已选的产品id，bom约束关系接口会用到
            isShowPackagePrice: !isEmpty(productPriceField),//是否展示产品包的价格
            enableRecentOrder: enableRecentOrder && isSalesOrder,//是否添加最近订购
            enableInputCustomFields: enableInputCustomFields && isSalesOrder,//是否支持输入字段
            detailObjectRecordType: recordType,
            enableSelectSameProduct,//是否允许选择相同的产品
            mobileBottomSummarySetting,
            stepperConfig: {
                min: 0,
                max: is_readonly ? (default_is_expression ? 1 : default_value) : 10000000000000,
                step: 1,
                starting: default_is_expression ? 1 : (default_value || 1),
                inputEditable: !is_readonly
            },
            isDetailPriceBookFieldHasFilters: !!(priceBookFieldDesc && priceBookFieldDesc.wheres && priceBookFieldDesc.wheres.length),
            useShopCategory: useShopCategory && isSalesOrder,
            selectObjectType: 'sku',
            formLayoutApiName,
            formLayoutDisplayName
        };
        let result = this.pluginApi.runPluginSync(emitEvent.priceService_batchSelectSkuConfig_sync, Object.assign({}, options));
        Object.assign(selectSKUConfig, result || {});
        let masterObjectData = Object.assign({}, selectObjectParams.masterObjectData, {
            account_id: accountId,
            partner_id: partner_id,
            price_book_id: priceBookId,
            price_book_id__r: priceBookName
        });
        let associatedObjectData = Object.assign({}, {pricebook_id: priceBookId}, chooseBySpu ? {
            is_real_lookup: false,
            pricebook_open: !isSelectProduct,
            product_target_related_list_name: selectObjectParams.lookupRelatedListName
        } : null);
        let extraData = this.getExtraData(addOpt, isSelectProduct);
        let formObjectData = Object.assign({}, selectObjectParams.formObjectData, associatedObjectData, {selectSKUConfig}, filterMasterObjectData(masterData));
        let isFilterSelectedProducts = this.bizStateConfig.isFilterSelectedProducts();
        let wheres = isFilterSelectedProducts && selectedProductIds && selectedProductIds.length ? appendFilters2Where([{
            field_name: isSelectProduct ? '_id' : 'product_id',
            field_values: selectedProductIds,
            operator: 'NIN'
        }], cloneDeep(selectObjectParams.wheres)) : selectObjectParams.wheres;
        Object.assign(selectObjectParams, {
            masterObjectData,
            formObjectData,
            wheres,
            disableAdd: true,
            includeAssociated: true,
            useWx: true,
            extraInfo: Object.assign({}, selectObjectParams.extraInfo, {extraData})
        }, chooseBySpu && {
            lookupRelatedListName: 'SPUObj_salesorderproduct_list',
            targetApiName: 'SPUObj',
        });
        return {
            consumed: false,
            selectObjectParams
        }
    }

    async mdBatchAddAfter(pluginExecResult, options) {
        let {dataGetter, objApiName, recordType, lookupDatas, lookupField, newDatas} = options || {};
        let fieldName = lookupField && lookupField.api_name;
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        if (!batchAddFormSku(fieldName, fieldMapping)) {
            return;
        }
        this.formatQuantityDecimalPlaces(options);
        let {quantity, product_price, discount} = fieldMapping;
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let quantityField = objectDescribe && objectDescribe.fields && objectDescribe.fields[quantity];
        let {default_is_expression} = quantityField || {};
        let layoutFields = dataGetter.getLayoutFields(objApiName, recordType);
        let isReadonly = layoutFields && layoutFields[quantity] && layoutFields[quantity].is_readonly;
        let calcQuantity = default_is_expression && isReadonly;//字段默认值是公式且只读才计算数量
        let masterData = dataGetter.getMasterData();
        let normalBackFills = this.normalBackFill(options);
        let calcPriceResult = await this.calcByRealPrice.calcPrice(options);
        let {backFills: getRealPriceBackFills, changedPriceBookProducts} = calcPriceResult || {};
        let quantityBackFills = undefined;
        if (calcQuantity) {
            //批量选产品要先走取价，再走计算数量逻辑，因为数量可以配置默认值是价目表明细的字段，不先走取价，价目表明细无值
            let cloneNewDatas = cloneDeep(newDatas);
            cloneNewDatas && cloneNewDatas.forEach((it, index) => {
                let normalBackFill = normalBackFills && normalBackFills.length && normalBackFills[index];
                let getRealPriceBackFill = getRealPriceBackFills && getRealPriceBackFills.length && getRealPriceBackFills[index];
                Object.assign(it, normalBackFill, getRealPriceBackFill);
            });
            quantityBackFills = await this.calcQuantity.process(masterData, objApiName, lookupDatas, cloneNewDatas, options);
        }
        for (let i = 0; i < newDatas.length; i++) {
            let newData = newDatas[i];
            let normalBackFill = normalBackFills && normalBackFills.length && normalBackFills[i];
            let getRealPriceBackFill = getRealPriceBackFills && getRealPriceBackFills.length && getRealPriceBackFills[i];
            let quantityBackFill = quantityBackFills && quantityBackFills.length && quantityBackFills[i];
            let updateData = Object.assign({}, normalBackFill, getRealPriceBackFill, quantityBackFill);
            formatDataDecimalPlaces(updateData, objectDescribe);
            Object.assign(newData, updateData);
        }
        if (changedPriceBookProducts && changedPriceBookProducts.length) {
            let changedInfo = changedPriceBookProducts.map(it => it.product_id__r).join('，');
            let tip = i18n('ava.object_form.onsale.getrealprice.batch_add_changed_tip', [changedInfo])/*{0}，已选数量不在当前价目表阶梯范围内，回填的价目表已更新，请知悉！*/;
            this.pluginApi.alert(tip);
        }
        return this.getNoCalFieldsBatchAdd(pluginExecResult, options)
    }

    mdBatchAddEnd(pluginExecResult, options) {
        this.setFieldReadonly(options);
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.getNoCalFieldsBatchAdd(pluginExecResult, options)
    }

    mdCloneEnd(pluginExecResult, options) {
        this.setFieldReadonly(options);
    }

    setFieldReadonly(options) {
        let readonlyFields = [];
        let {dataUpdater, objApiName} = options || {};
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_product_id, price_book_id} = fieldMapping;
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        let openPriceBookPriority = this.bizStateConfig.isOpenPriceBookPriority();
        openPriceBook && readonlyFields.push(product_id);//开启价目表，产品名称字段只读
        openPriceBookPriority && readonlyFields.push(price_book_product_id, price_book_id);//强制执行价目表优先级时，价目表产品、价目表字段只读
        dataUpdater && dataUpdater.setReadOnly({
            objApiName,
            dataIndex: 'all',
            fieldName: readonlyFields,
            biz: 'price-service',
            priority: 11
        });
    }

    formatQuantityDecimalPlaces(options) {
        let {dataGetter, objApiName, lookupDatas} = options || {};
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let quantityField = objectDescribe && objectDescribe.fields && objectDescribe.fields[fieldMapping.quantity];
        let {decimal_places = 0} = quantityField || {};
        lookupDatas.forEach(it => {
            it._selected_num = formatValueDecimalPlaces(it._selected_num, decimal_places);
        });
    }

    normalBackFill(options) {
        let {dataGetter, objApiName, lookupDatas} = options || {};
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {quantity, product_id, price_book_product_id, price_book_id, product_price} = fieldMapping;
        let detailDescribe = dataGetter.getDescribe(objApiName);
        let productField = detailDescribe && detailDescribe.fields && detailDescribe.fields[product_id];
        let productOpenDisplayName = productField && productField.is_open_display_name;
        return lookupDatas && lookupDatas.length && lookupDatas.map(skuData => {
            let {
                _id, name, display_name, product_id: productId, product_id__r: productName, pricebook_id, pricebook_id__r, _selected_num, object_describe_api_name,
                key_select_sku_input_data: inputData, price, pricebook_sellingprice
            } = skuData;
            let isPriceBookProduct = object_describe_api_name === 'PriceBookProductObj';
            return Object.assign({
                [quantity]: _selected_num || 1,
                [product_price]: isPriceBookProduct ? pricebook_sellingprice : price,//默认回填价格
                ...(inputData || {})//输入的必填的自定义字段
            }, isPriceBookProduct ? {
                [product_id]: productId,
                [`${product_id}__r`]: productName,
                [price_book_product_id]: _id,
                [`${price_book_product_id}__r`]: name,
                [price_book_id]: pricebook_id,
                [`${price_book_id}__r`]: pricebook_id__r,
            } : {
                [product_id]: _id,
                [`${product_id}__r`]: productOpenDisplayName ? display_name : name
            });
        });
    }

    getExtraData(addOpt, isSelectProduct) {
        let {isQuoterGenerateDetails, selectedAttributeValues, selectedNoStandardAttributeValues, attributeFieldNum} = addOpt || {};
        if (isQuoterGenerateDetails) {
            let standardAttributeFilters = [];
            if (!isEmpty(selectedAttributeValues)) {
                each(selectedAttributeValues, (value, key) => {
                    let fieldNum = attributeFieldNum && attributeFieldNum[key];
                    if (!isEmpty(fieldNum) && value && value.length) {
                        standardAttributeFilters.push({
                            field_name: `attribute${fieldNum}`,
                            operator: 'HASANYOF',
                            field_Values: value
                        })
                    }
                })
            }
            let nonstandardAttributeFilters = [];
            if (!isEmpty(selectedNoStandardAttributeValues)) {
                each(selectedNoStandardAttributeValues, (value, key) => {
                    if (value && value.length) {
                        nonstandardAttributeFilters.push({
                            field_name: key,
                            operator: 'HASANYOF',
                            field_Values: value
                        })
                    }
                })
            }
            return Object.assign({
                is_from_quoter: true,
            }, nonstandardAttributeFilters && nonstandardAttributeFilters.length && {
                nonstandard_attribute_query_info: JSON.stringify({filters: nonstandardAttributeFilters})
            }, standardAttributeFilters && standardAttributeFilters.length && {
                standard_attribute_query_info: JSON.stringify({filters: standardAttributeFilters})
            })
        }
    }

    getNoCalFieldsBatchAdd(pluginExecResult, options) {
        let {objApiName} = options || {};
        let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {quantity, product_price, discount, price_book_subtotal} = fieldMapping;
        let preData = pluginExecResult && pluginExecResult.preData;
        let preObjectFilterFields = preData
            && preData.extraCalUiParams
            && preData.extraCalUiParams.filterFields
            && preData.extraCalUiParams.filterFields[objApiName];
        let noCalFields = [quantity];//数量不需要计算，取选产品页面输入的值;
        let triggerGetRealPriceResult = this.pluginApi.runPluginSync(emitEvent.priceService_batchAdd_isTriggerGetPrice_sync, Object.assign({}, options));
        if (isTriggerGetRealPrice(this.bizStateConfig, triggerGetRealPriceResult)) {//调用了取价接口，价格、折扣不参与计算
            noCalFields.push(product_price);//调用了取价接口，价格、折扣不参与计算
            let openPriceBook = this.bizStateConfig.isOpenPriceBook();
            if (openPriceBook) {
                noCalFields.push(discount);
            }
            let openStratifiedPricing = this.bizStateConfig.isOpenStratifiedPricing();
            if (openStratifiedPricing) {
                noCalFields.push(price_book_subtotal);
            }
        }
        return Object.assign({}, preData, {
            extraCalUiParams: Object.assign({}, preData && preData.extraCalUiParams, {
                filterFields: Object.assign({}, preData && preData.extraCalUiParams && preData.extraCalUiParams.filterFields,
                    {[objApiName]: [...(preObjectFilterFields || []), ...noCalFields]}),
            })
        })
    }

    async chooseSPU() {
        this.pluginApi.showLoading();
        return await this.requestApi.chooseSPU()
            .then(rst => {
                this.pluginApi.hideLoading();
                return rst;
            }).catch(() => {
                this.pluginApi.hideLoading();
                return false;
            });
    }

}