import {emitEvent} from "./events";
import {appendFilters2Where, triggerPriceTieredRecordChangedEvent} from "./utils";
import {
    cloneDeep,
    formatDataDecimalPlaces,
    getPriceBookParamAndSimplifyDetails,
    isEdit,
    isEmpty,
    simplifyDetails,
    uuid,
    equals
} from '../../pluginbase-ava/package/pluginutils'

const key_selected_price_book_product = 'key_selected_price_book_product';//选择的价目表明细数据

export class DetailFieldEdit {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    detailFieldEditBefore(pluginExecResult, options) {
        let {objApiName, fieldName} = options;
        let detailFields = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_product_id, price_book_id} = detailFields || {};
        if (fieldName === product_id) {
            return this.productEditBefore(options);
        } else if (fieldName === price_book_product_id) {
            return this.priceBookProductEditBefore(options);
        } else if (fieldName === price_book_id) {
            return this.priceBookEditBefore(options);
        }
    }

    detailFieldEditAfter(pluginExecResult, options) {
        let {objApiName, fieldName} = options;
        let {quantity, price_book_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName);
        if (fieldName === price_book_product_id) {
            return this.priceBookProductEditAfter(options);
        } else if (fieldName === price_book_id) {
            return this.priceBookEditAfter(options);
        } else if (fieldName === quantity) {
            return this.quantityEditAfter(options);
        }
    }

    productEditBefore(options) {
        let {form_account_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {objApiName, fieldName, selectObjectParams, dataGetter} = options;
        let masterData = dataGetter.getMasterData();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId} = masterData || {};
        let detailFields = this.fieldMapping.getDetailFields(objApiName);
        let {product_id} = detailFields || {};
        let formObjectData = selectObjectParams.formObjectData;
        let {[product_id]: curProductId} = formObjectData || {};
        let details = dataGetter.getDetail && dataGetter.getDetail(objApiName);
        let selectedProductIds = details && details.filter(detail => {
            let {[product_id]: productId} = detail;
            let isFilter = true;
            let result = this.pluginApi.runPluginSync(emitEvent.priceService_filterProduct_before_sync, {
                objectData: detail,
                objApiName
            });
            if (typeof result == 'boolean') {
                isFilter = result;
            }
            return productId !== curProductId && isFilter;
        }).map(it => it[product_id]);
        let isFilterSelectedProducts = this.bizStateConfig.isFilterSelectedProducts();
        let wheres = isFilterSelectedProducts && selectedProductIds && selectedProductIds.length ? appendFilters2Where([{
            field_name: fieldName === product_id ? '_id' : 'product_id',
            field_values: selectedProductIds,
            operator: 'NIN'
        }], cloneDeep(selectObjectParams.wheres)) : selectObjectParams.wheres;
        Object.assign(selectObjectParams, {
            formObjectData: Object.assign({}, masterData, selectObjectParams.formObjectData, {
                account_id: accountId,
                partner_id: partnerId,
            }),
            disableAdd: true,
            wheres
        })
        return {selectObjectParams};
    }

    priceBookProductEditBefore(options) {
        let {form_account_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {objApiName, fieldName, selectObjectParams, dataGetter} = options;
        let masterData = dataGetter.getMasterData();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId} = masterData || {};
        let detailFields = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_id, quantity} = detailFields || {};
        let formObjectData = selectObjectParams.formObjectData;
        let {[product_id]: curProductId, [quantity]: _quantity} = formObjectData || {};
        let details = dataGetter.getDetail && dataGetter.getDetail(objApiName);
        let selectedProductIds = details && details.filter(detail => {
            let {[product_id]: productId} = detail;
            let isFilter = true;
            let result = this.pluginApi.runPluginSync(emitEvent.priceService_filterProduct_before_sync, {
                objectData: detail,
                objApiName
            });
            if (typeof result == 'boolean') {
                isFilter = result;
            }
            return productId !== curProductId && isFilter;
        }).map(it => it[product_id]);
        let wheres = selectedProductIds && selectedProductIds.length ? appendFilters2Where([{
            field_name: fieldName === product_id ? '_id' : 'product_id',
            field_values: selectedProductIds,
            operator: 'NIN'
        }], cloneDeep(selectObjectParams.wheres)) : selectObjectParams.wheres;
        let priceBookId = selectObjectParams.formObjectData && selectObjectParams.formObjectData[price_book_id];
        let enableSelectSameProduct = this.bizStateConfig.enableSelectSameProductInDifferentPriceBooks();
        Object.assign(selectObjectParams, {
            formObjectData: Object.assign({}, masterData, selectObjectParams.formObjectData, {
                account_id: accountId,
                partner_id: partnerId,
                pricebook_id: priceBookId,
                filter_quantity: _quantity
            }),
            disableAdd: true,
            wheres: enableSelectSameProduct ? selectObjectParams.wheres : wheres
        })
        return {selectObjectParams};
    }

    priceBookEditBefore(options) {
        let self = this;
        let {form_account_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {objApiName, selectObjectParams, dataGetter} = options;
        let masterData = dataGetter.getMasterData();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId} = masterData || {};
        let detailFields = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_product_id, price_book_id, quantity} = detailFields || {};
        let {formObjectData, details} = selectObjectParams;
        let {[product_id]: curProductId, [price_book_product_id]: priceBookProductId, [quantity]: _quantity} = formObjectData || {};
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        let describe = dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let priceBookField = describe && describe.fields && describe.fields[price_book_id];
        let hasFilters = !!(priceBookField && priceBookField.wheres && priceBookField.wheres.length);
        let currentDetails = {[objApiName]: details && details[objApiName]};
        let isEditMode = isEdit(sourceAction);
        return {
            selectObjectParams: {
                selectedPriceBookProductId: priceBookProductId,//已选的价目表产品id
                accountId: accountId,//客户id
                customerId: accountId,
                partnerId: partnerId,//合作伙伴id
                productId: curProductId,//产品id
                enableValidorder: isEditMode,//true:编辑时下发,禁用和过期的也可以出现，false:新建时用,不出现禁用和过期的
                masterData,
                masterObjectData: masterData,
                quantity: _quantity,
                details: simplifyDetails(hasFilters, currentDetails),
                enableHugeData: true
            },
            selectObject(params) {
                return new Promise((resolve, reject) => {
                    let config = {
                        name: "ava://object_list/object_list_sfa_o2c/pages/select_price_book_product/index",
                        params,
                        onSuccess: (res) => {
                            let objectDataList = [];
                            let priceBookProduct = res && res.selectedData;
                            if (priceBookProduct) {
                                let {pricebook_id = null, pricebook_id__r = null} = priceBookProduct;
                                objectDataList.push({
                                    _id: pricebook_id,
                                    name: pricebook_id__r,
                                    [key_selected_price_book_product]: priceBookProduct
                                })
                            }
                            resolve({objectDataList, forceTriggerChange: true});
                        },
                        onFail: (error) => {
                            reject(error);
                        },
                    };
                    self.pluginApi.openPage(config);
                });
            }
        }
    }

    async priceBookProductEditAfter(options) {
        let { lookupData } = options;
        await this.handlePriceBookProductEditAfter(options, lookupData);
    }

    async priceBookEditAfter(options) {
        let { lookupData } = options;
        let priceBookProductData = lookupData && lookupData[key_selected_price_book_product];
        await this.handlePriceBookProductEditAfter(options, priceBookProductData);
    }

    async handlePriceBookProductEditAfter(options, priceBookProductData) {
        let {masterObjApiName, objApiName, changeData, dataGetter, dataIndex, seriesId} = options;
        let {quantity, product_id, price_book_product_id, price_book_id, discount, product_price, actual_unit, unit, price_tiered_record, price_book_subtotal, price_book_price, price_book_discount} = this.fieldMapping.getDetailFields(objApiName);
        let { _id = null, name = null, product_id: productId = null, product_id__r = null, pricebook_id = null, pricebook_id__r = null, actual_unit: actualUnit } = priceBookProductData || {};
        Object.assign(changeData, {
            [product_id]: productId,
            [`${product_id}__r`]: product_id__r,
            [price_book_product_id]: _id,
            [`${price_book_product_id}__r`]: name,
            [actual_unit]: actualUnit,
            [price_book_id]: pricebook_id,
            [`${price_book_id}__r`]: pricebook_id__r,
        });
        let masterData = dataGetter.getMasterData();
        let {form_account_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId} = masterData || {};
        if (!accountId || !productId) {//产品id为空，不走取价
            return;
        }
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let {[quantity]: amount, [`${unit}__v`]: baseUnit, discount: _discount} = objectData;
        let object_api_name = masterData && masterData.object_describe_api_name;
        let details = getPriceBookParamAndSimplifyDetails(false, masterObjApiName, objApiName, price_book_id, dataGetter);
        if (!isEmpty(details)) {
            let sortDetailDatas = [];
            let detailDataList = details[objApiName] || [];
            let modifiedData = detailDataList && detailDataList.find(it => it.dataIndex === dataIndex);
            if (modifiedData) {
                sortDetailDatas.push(modifiedData);//当前操作的数据放到首位
            }
            detailDataList && detailDataList.forEach(detailData => {
                if (detailData.dataIndex !== dataIndex) {
                    sortDetailDatas.push(detailData)
                }
            });
            details[objApiName] = sortDetailDatas;
        }
        let detailData = Object.assign({}, objectData, changeData);
        let fullProductInfo = this.pluginApi.runPluginSync(emitEvent.priceService_form_parseFullProduct_sync, Object.assign({}, options, {
            detailData,
            type: 'detailFieldEdit'
        }));
        let param = {
            accountId: accountId,
            partnerId: partnerId,
            requestSource: null,
            fullProductList: [Object.assign({
                productId: productId,
                priceBookId: pricebook_id,
                baseUnit: baseUnit,
                amount: amount,
                discount: _discount,
            }, fullProductInfo)],
            object_data: masterData,
            object_api_name,
            details,
            seriesId
        };
        this.pluginApi.runPluginSync(emitEvent.priceService_form_getPriceParam_sync, Object.assign({}, options, {
            objApiName,
            params: param
        }));
        let pageId = dataGetter.getPageId();
        let token = 'price_service_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let result = await this.requestApi.getRealPrice(param).then(rst => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            return rst;
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        });
        let calcResult = result && result.newRst && result.newRst[0];
        if (calcResult) {
            let backFill = this.pluginApi.runPluginSync(emitEvent.priceService_getBackFills_before_sync, {
                objApiName,
                detailData,
                getRealPriceResult: calcResult
            });
            let {selling_price, discount: _discount, detail_discount: _detail_discount, price_tiered_record: priceTieredRecord, price_book_subtotal: priceBookSubtotal, pricebook_price} = calcResult;
            let discountValue = isEmpty(_detail_discount) ? _discount : _detail_discount;
            let updateData = Object.assign({}, {
                [product_price]: selling_price,
                [discount]: discountValue,
                [price_tiered_record]: priceTieredRecord,
                [price_book_price]: pricebook_price,
                [price_book_discount]: _discount,
            }, (!isEmpty(priceBookSubtotal)) && {
                [price_book_subtotal]: priceBookSubtotal
            }, backFill);
            let objectDescribe = dataGetter.getDescribe(objApiName);
            formatDataDecimalPlaces(updateData, objectDescribe);
            Object.assign(changeData, updateData);
            triggerPriceTieredRecordChangedEvent(this.pluginApi, priceTieredRecord, dataIndex);
        }
    }

    async quantityEditAfter(options) {
        let openPriceBookProductTieredPrice = this.bizStateConfig.isOpenPriceBookProductTieredPrice();
        let isOpenStratifiedPricing = this.bizStateConfig.isOpenStratifiedPricing();
        if (!openPriceBookProductTieredPrice && !isOpenStratifiedPricing) {
            return;
        }
        let {masterObjApiName, objApiName, changeData, dataGetter, dataIndex, seriesId} = options;
        let masterData = dataGetter.getMasterData();
        let {form_account_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId} = masterData || {};
        if (!accountId) {
            return;
        }
        let {quantity, product_id, price_book_id, price_book_product_id, discount, product_price, unit, price_tiered_record, price_book_subtotal, price_book_price, price_book_discount} = this.fieldMapping.getDetailFields(objApiName);
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let { [product_id]: productId, [product_price]: price, [discount]: _discount, [quantity]: amount, [`${unit}__v`]: baseUnit, [price_book_id]: _price_book_id, [price_book_product_id]: _price_book_product_id,
            [price_tiered_record]: orgPriceTieredRecord } = tempData;
        if (!productId) {//产品id为空，不走取价
            return;
        }
        let doNotCalcPriceResult = this.pluginApi.runPluginSync(emitEvent.priceService_form_detailDataDoNotCalcPrice_sync, Object.assign({}, options, {
            objApiName,
            detailData: tempData,
            action: 'fieldEditAfter'
        }));//{cpq:true}
        if (!isEmpty(doNotCalcPriceResult)) {
            return;
        }
        let object_api_name = masterData && masterData.object_describe_api_name;
        let fullProductInfo = this.pluginApi.runPluginSync(emitEvent.priceService_form_parseFullProduct_sync, Object.assign({}, options, {
            detailData: tempData,
            type: 'detailFieldEdit'
        }));
        let param = {
            accountId: accountId,
            partnerId: partnerId,
            requestSource: null,
            fullProductList: [Object.assign({
                productId: productId,
                amount: amount,
                baseUnit: baseUnit,
                price,
                discount: _discount,
                priceBookId: _price_book_id,
                priceBookProductId: _price_book_product_id,
            }, fullProductInfo)],
            object_data: masterData,
            object_api_name,
            details: getPriceBookParamAndSimplifyDetails(false, masterObjApiName, objApiName, price_book_id, dataGetter),
            seriesId
        };
        this.pluginApi.runPluginSync(emitEvent.priceService_form_getPriceParam_sync, Object.assign({}, options, {
            objApiName,
            params: param
        }));
        let pageId = dataGetter.getPageId();
        let token = 'price_service_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let result = await this.requestApi.getRealPrice(param).then(rst => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            return rst;
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        });
        let calcResult = result && result.newRst && result.newRst[0];
        if (calcResult) {
            let backFill = this.pluginApi.runPluginSync(emitEvent.priceService_getBackFills_before_sync, {
                objApiName,
                detailData: tempData,
                getRealPriceResult: calcResult
            });
            let {selling_price, discount: _discount, detail_discount: _detail_discount, pricebook_id, pricebook_id__r, _id, name, price_tiered_record: priceTieredRecord, price_book_subtotal: priceBookSubtotal, pricebook_price} = calcResult;
            let isPriceTieredProduct = !isEmpty(orgPriceTieredRecord);
            let updatePriceBookData = isPriceTieredProduct ? true : (equals(_price_book_product_id, _id) ? false : true);
            let discountValue = isEmpty(_detail_discount) ? _discount : _detail_discount;
            let updateData = Object.assign({}, updatePriceBookData && {
                [product_price]: selling_price,
                [discount]: discountValue,
                [price_book_id]: pricebook_id,
                [`${price_book_id}__r`]: pricebook_id__r,
                [price_book_product_id]: _id,
                [`${price_book_product_id}__r`]: name,
                [price_book_price]: pricebook_price,
                [price_book_discount]: _discount,
            }, {
                [price_tiered_record]: priceTieredRecord,
            }, (!isEmpty(priceBookSubtotal)) && {
                [price_book_subtotal]: priceBookSubtotal
            }, backFill);
            let objectDescribe = dataGetter.getDescribe(objApiName);
            formatDataDecimalPlaces(updateData, objectDescribe);
            Object.assign(changeData, updateData);
            triggerPriceTieredRecordChangedEvent(this.pluginApi, priceTieredRecord, dataIndex);
        }
    }
}