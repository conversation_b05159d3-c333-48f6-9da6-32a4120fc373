import {request} from "../../pluginbase-ava/package/pluginutils";

export default class PriceServiceApi {

    constructor(http) {
        this.http = http;
    }

    getRealPrice(params) {
        return request(this.http, {
            url: 'FHH/EM1HNCRM/API/v1/object/available_range/service/get_real_price',
            data: params
        });
    }

    getDefaultPriceBook(params) {
        return request(this.http, {
            url: 'FHE/EM1ANCRM/API/v1/object/pricebook/service/pickone_for_sales_order',
            data: params
        }).then(res => {
            return res ? res.result : {};
        })
    }

    validateAccountPriceBook(params) {
        return request(this.http, {
            url: 'FHE/EM1ANCRM/API/v1/object/pricebook/service/validate_account_pricebook',
            data: params
        }).then(res => {
            return res && res.result;
        })
    }

    batchCalculate(opts) {
        let {masterObjectApiName, masterData, detailDataMap, modifiedObjectApiName, modifiedDataIndexList, calculateFields} = opts;
        return request(this.http, {
            url: '/FHH/EM1HNCRM/API/v1/object/calculate/service/batchCalculate',
            data: {
                masterObjectApiName: masterObjectApiName,
                masterData: masterData,
                detailDataMap: detailDataMap,
                modifiedObjectApiName: modifiedObjectApiName,
                modifiedDataIndexList: modifiedDataIndexList,
                calculateFields: calculateFields
            }
        }).then(res => {
            return res && res.calculateResult;
        })
    }

    chooseSPU() {
        return request(this.http, {
            url: 'FHE/EM1ANCRM/API/v1/object/spu_sku_choose/service/choose_spu',
            data: {}
        }).then(res => {
            let chooseSpu = res && res.result;
            return (typeof chooseSpu === 'string') ? (chooseSpu !== 'false') : chooseSpu;
        })
    }

    getUserConfig(userConfigKey) {
        if (!userConfigKey) {
            return Promise.resolve();
        }
        return request(this.http, {
            url: `FHE/EM1ANCRM/API/v1/object/biz_config/service/get_set_user_config`,
            data: {key: userConfigKey},
            cacheRule: {type: "app"}
        }).then(res => {
            return res && res.value;
        });
    }
}