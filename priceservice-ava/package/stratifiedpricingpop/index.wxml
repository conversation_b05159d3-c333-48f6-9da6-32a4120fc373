<!-- 价格表格组件 -->
<fs-confirm show="{{dShow}}" mask="{{true}}" title="{{dTitle}}" confirmBtn="{{dConfirmBtn}}" bindonClose="onClose">
  <view slot="body" style="display: flex; flex-direction: column;">
    <view class="price-table">
    <!-- 表头 -->
      <view class="table-header">
        <text class="header-cell">{{dFieldLabel.range}}</text>
        <view class="line"></view>
        <text class="header-cell">{{dFieldLabel.priceBookPrice}}</text>
        <view class="line"></view>
        <text class="header-cell">{{dFieldLabel.priceBookSellingPrice}}</text>
        <view class="line"></view>
        <text class="header-cell">{{dFieldLabel.discount}}</text>
      </view>
      <scroll-view scroll-y style="width: 100%; height: 200px">
          <!-- 表格内容 -->
          <block wx:for="{{dPriceList}}" wx:key="index">
            <view class="table-row">
              <text class="table-cell">{{item.range}}</text>
              <text class="table-cell">{{item.priceBookPrice}}</text>
              <text class="table-cell">{{item.priceBookSellingPrice}}</text>
              <text class="table-cell">{{item.discount}}</text>
            </view>
          </block>
      </scroll-view>
    </view>
    <text class="price-table-tip">{{dTip}}</text>  
  </view> 
</fs-confirm>