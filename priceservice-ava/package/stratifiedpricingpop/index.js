import StratifiedPricingPopModule from "../../../pluginbase-ava/package/modules/StratifiedPricingPopModule";
import fsapi from 'fs-hera-api'

Component({

  data: {
    dShow: false,
    dTitle: '',
    dFieldLabel: {},
    dPriceList: [],
    dTip: ''
  },

  methods: {
    onClose() {
      this.setData({
        dShow: false
      })
    }
  },

  lifetimes: {
    attached() {
      let self = this;
      this.triggerEvent("attached");
      let pageId = this.getPageId();
      StratifiedPricingPopModule.onShow(params => {
        let { title, fieldLabel, priceTieredRecord } = params;
        self.setData({
          dShow: true,
          dTitle: title,
          dFieldLabel: fieldLabel,
          dConfirmBtn: {
            text: fsapi.i18n.get('ava.object_form.close')/*关闭*/,
            onClick: () => {
              self.onClose();
            }
          },
          dPriceList: priceTieredRecord && priceTieredRecord.recordList || [],
          dTip: fsapi.i18n.get('ava.object_form.priceservice.tiered_price_tip')/*备注：展示的是对应【分层价目表明细】的原始层级价格*/
        })
      }, pageId);
    }
  }
});