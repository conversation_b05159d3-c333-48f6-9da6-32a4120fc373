import {i18n, isEmpty, parseHttpRst} from "../../pluginbase-ava/package/pluginutils";
import dialogset from '../../objformmain/dialogset/index';
import paas_api from "../../business-cmpt-paas-main/paasapi";
import {TAG} from './enum'

class FailReasonHandler {

    handle(failReason, pluginApi) {
        try {
            let failReasonValue = parseHttpRst(failReason);
            if (isEmpty(failReasonValue)) {
                console.log(TAG, 'async_commit_fail_reason is empty');
                return
            }
            let {versionCheckBlocked, validationRuleMessage, funcValidateMessage, isDuplicate, validationResult, paramsIdempotentMessage, objectData} = failReasonValue;
            if (isDuplicate) {
                this.checkDuplicate(objectData)
            } else {
                let reasons = [];
                let tempValidateMessage;
                if (validationRuleMessage && validationRuleMessage.match) {
                    tempValidateMessage = validationRuleMessage;
                } else if (funcValidateMessage && funcValidateMessage.match) {
                    tempValidateMessage = funcValidateMessage;
                } else if (paramsIdempotentMessage && paramsIdempotentMessage.match) {
                    tempValidateMessage = paramsIdempotentMessage;
                }
                if (versionCheckBlocked) {
                    reasons.push(this.newReason(true, i18n('ava.object_form.async_commit.conflict_tip')/*数据在您编辑时有其他人变更，您提交的数据与其存在冲突*/))
                } else if (tempValidateMessage) {
                    let {blockMessages, nonBlockMessages} = tempValidateMessage;
                    (blockMessages && blockMessages.length) && (reasons.push(this.newReason(true, blockMessages.join("; "))));
                    (nonBlockMessages && nonBlockMessages.length) && reasons.push(this.newReason(false, nonBlockMessages.join("; ")));
                } else if (validationResult && validationResult.message) {
                    reasons.push(this.newReason(validationResult.block, validationResult.message))
                }
                this.showFailReasonPop(reasons)
            }
        } catch (e) {
            pluginApi.alert(e);
            console.warn(e);
        }
    }

    showFailReasonPop(reasons) {
        if (!reasons || !reasons.length) {
            return;
        }
        dialogset.iconDialog.show({
            title: i18n("ava.object_form.validata_rule_warn"/*提示*/),
            dataList: reasons,
            showCancel: false,
            confirmText: i18n('ava.object_form.iknow')/*我知道了*/,
            success(res) {
            }
        });
    }

    checkDuplicate(objectData) {
        paas_api.openDuplicateCheckResultPage({
            apiName: 'SalesOrderObj',
            objectData: objectData,
            displayName: i18n('SalesOrderObj.attribute.self.display_name')/*销售订单*/,
            duplicate_rule_api_name: null,
            onSuccess(rst) {
            },
            onFail(e) {
            }
        })
    }

    newReason(block, label) {
        return {icon: "yichang", color: block ? "#FF704F" : "#FA9B08", label: label}
    }
}

export default new FailReasonHandler();