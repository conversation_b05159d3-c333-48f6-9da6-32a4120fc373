<fs-popup show="{{dShow}}" mask="{{true}}" bindonBackClose="noop" bindonMaskClose="noop" position="bottom">
    <view class="content" slot="content">
        <view style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; padding: 24rpx">
            <text class="title">{{dTitle}}</text>
<!--            <view class="fxui_all titlebar_close_black close" catchtap="dismiss"></view>-->
        </view>
        <view class="line"></view>
        <view style="display: flex; flex-direction: column; padding: 24rpx">
            <scroll-view scroll-y style="width: 100%; height: {{dScrollViewHeight}}px">
                <text wx:if="{{dShowWaitInfo}}" style="font-size: 28rpx; color: #181c25;">{{dWaitInfo}}</text>
                <block wx:else>
                    <view wx:for="{{dSteps||[]}}" wx:key="id">
                        <view style="display: flex; flex-direction: column;">
                            <view wx:if="{{index!==0}}"
                                  style="width: 2px; height: 24px; background: #c1c5ce; margin-left: 7px; {{item.lineStyle}}"></view>
                            <view style="display: flex; flex-direction: row; align-items: center">
                                <view class="fxui_all shenpikaishi" style="{{item.iconStyle}}"></view>
                                <text style="font-size: 24rpx; color: #181c25; margin-left: 20rpx">{{item.content}}</text>
                            </view>
                        </view>
                    </view>
                </block>
            </scroll-view>
        </view>
        <view class="line"></view>
        <view class="action-container">
            <view wx:for="{{dButtons||[]}}" wx:key="type" class="action" style="margin-right: {{index===0?0:24}}rpx">
                <text class="action-text" bindtap="onButtonClick" data-actiontype="{{item.type}}">{{item.text}}</text>
            </view>
        </view>
    </view>
</fs-popup>