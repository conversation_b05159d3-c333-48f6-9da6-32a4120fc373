import {isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import paasapi from "../../../business-cmpt-paas-main/paasapi";
import fsapi from 'fs-hera-api';
import {ActionType} from "../enum";

class ActionHandler {

    doAction(options, callback) {
        let {actionType, task, masterData} = options;
        if (actionType === ActionType.BACK_TO_LIST) {
            // this.backToList(masterData, task.getJobId());
            callback && callback({navigateBack: true});
        } else if (actionType === ActionType.CONTINUE_ADD) {
            this.continueAdd(masterData);
            callback && callback({navigateBack: true});
        } else if (actionType === ActionType.VIEW_DETAIL) {
            this.viewDetail(task);
            callback && callback({navigateBack: true});
        } else if (actionType === ActionType.REEDIT) {
            this.reedit();
            callback && callback({navigateBack: false});
        }
    }

    backToList(masterData, jobId) {
        if (isEmpty(masterData)) {
            fsapi.util.showToast('backToList: masterData is null');
            return;
        }
        let {object_describe_api_name} = masterData;
        paasapi.openObjectList({
            apiName: object_describe_api_name,
            staticData: {async_commit_job_id: jobId}
        })
    }

    continueAdd(masterData) {
        if (isEmpty(masterData)) {
            fsapi.util.showToast('continueAdd: masterData is null');
            return;
        }
        let {object_describe_api_name, record_type} = masterData;
        paasapi.openObjectForm({
            action: "Add",
            objectApiName: object_describe_api_name,
            mainObjectData: {record_type},
        })
    }

    viewDetail(task) {
        let message = task && task.getMessage();
        let commitResult = message && message.Value;
        if (isEmpty(commitResult)) {
            fsapi.util.showToast('viewDetail: async result is null');
            return;
        }
        let {_id, object_describe_api_name} = commitResult.objectData;
        paasapi.crmOpenDetail({
            useReLaunch: false,
            apiName: object_describe_api_name,
            dataId: _id
        })
    }

    reedit() {

    }
}

export default new ActionHandler();