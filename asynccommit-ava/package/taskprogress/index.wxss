.content {
    display: flex;
    flex-direction: column;
    background: white;
    width: 100vw;
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-bottom: calc(env(safe-area-inset-bottom));
}

.title{
    font-size: 36rpx;
    color: #181c25;
}

.close {
    color: #91959E;
    font-size: 16px;
    line-height: 22px;
    background: #F3F5F8;
    width: 22px;
    height: 22px;
    text-align: center;
    border-radius: 14px;
}

.line{
    height: 1px;
    width: 100%;
    background: #dee1e8;
}

.action-container{
    display: flex;
    flex-direction: row-reverse;
    padding: 16rpx;
    align-items: center;
}

.action{
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    border-radius: 8rpx;
    border: 2rpx solid #c1c5ce;
    padding: 12rpx 32rpx;
}

.action-text{
    font-size: 28rpx;
    line-height: 40rpx;
    text-align: center;
    color: #181c25;
}