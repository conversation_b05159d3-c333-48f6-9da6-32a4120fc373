import page from "fs-hera-api/api/page/index";
import AsyncCommitPopModule from "../../../pluginbase-ava/package/modules/AsyncCommitPopModule";
import {ActionType, TAG, TASK_STATUS} from "../enum";
import {i18n, isEmpty, uuid} from "../../../pluginbase-ava/package/pluginutils";
import actionHandler from "./actionHandler";

Component({

    options: {
        addGlobalClass: true
    },

    data: {
        dShow: false,
        dScrollViewHeight: 0,
        dTitle: null,
        dShowWaitInfo: true,//是否显示等待信息
        dWaitInfo: null,
        dSteps: undefined,
        dButtons: [],//按钮
    },

    methods: {
        noop() {
        },

        dismiss() {
            this.setData({dShow: false});
        },

        onButtonClick(event) {
            let actionType = event.currentTarget && event.currentTarget.dataset && event.currentTarget.dataset.actiontype;
            let options = {actionType, task: this.task, masterData: this.masterData};
            actionHandler.doAction(options, result => {
                let {navigateBack} = result;
                if (navigateBack) {
                    let jobId = this.task.getJobId();
                    page.setCallbackDataThenBack({async_commit_job_id: jobId}, this.getPageId(), {
                        delta: 1
                    });
                }
                this.dismiss();
                this.actionCallback && this.actionCallback(actionType, this.task);
            });
        },

        getUpdateDataByTask(task) {
            let taskStatus = task && task.getStatus();
            let showWaitInfo = taskStatus === TASK_STATUS.CREATED;
            let buttons = this.getButtons(task);
            let steps = this.getSteps(task);
            return {dButtons: buttons, dShowWaitInfo: showWaitInfo, dSteps: steps};
        },

        getSteps(task) {
            function newStep(content, iconStyle, lineStyle) {
                return {id: uuid(), content, iconStyle, lineStyle}
            }

            let steps = [];
            let preSteps = this.data.dSteps || [];
            let taskStatus = task && task.getStatus();
            if (taskStatus === TASK_STATUS.RUNNING) {
                let message = task.getMessage();
                if (isEmpty(message)) {
                    steps.push(...preSteps, newStep(i18n('ava.object_form.async_commit.in_progress')/*处理中*/, 'color: #30C776;', 'background: #30C776;'));
                } else {
                    steps = message.split('$|$').filter(item => {
                        return !isEmpty(item);
                    }).map(item => {
                        return newStep(item, 'color: #30C776;', 'background: #30C776;');
                    }) || [];
                }
            } else if (taskStatus === TASK_STATUS.REQUESTING) {
                steps.push(...preSteps)
            } else if (taskStatus === TASK_STATUS.SUCCESS) {
                steps.push(...preSteps, newStep(i18n('ava.object_form.async_commit.created')/*创建完成*/, 'color: #30C776;', 'background: #30C776;'));
            } else if (taskStatus === TASK_STATUS.FAIL) {
                steps.push(...preSteps, newStep(i18n('ava.object_form.async_commit.created_failed')/*业务失败，请重新编辑*/, 'color: #FF522A;', 'background: #FF522A;'));
            } else if (taskStatus === TASK_STATUS.ERROR) {
                steps.push(...preSteps, newStep(i18n('ava.object_form.async_commit.service_exception')/*服务异常*/, 'color: #FF522A;', 'background: #FF522A;'));
            }
            return steps;
        },

        getButtons(task) {
            function newButton(type, text, index) {
                return {type, text, index}
            }

            let buttons = [newButton(ActionType.CONTINUE_ADD, i18n('ava.object_form.async_commit.continue_create')/*继续新建*/, 1),
                newButton(ActionType.BACK_TO_LIST, i18n('ava.object_form.async_commit.return_list_page')/*关闭*/, 3)]
            let taskStatus = task && task.getStatus();
            if (taskStatus === TASK_STATUS.SUCCESS) {
                buttons.push(newButton(ActionType.VIEW_DETAIL, i18n('ava.object_form.async_commit.view_details')/*查看详情*/, 2));
            } else if (taskStatus === TASK_STATUS.FAIL || taskStatus === TASK_STATUS.ERROR) {
                buttons.push(newButton(ActionType.REEDIT, i18n('ava.object_form.async_commit.reedit')/*重新编辑*/, 0));
            }
            return buttons.sort((a, b) => {
                return a.index - b.index;
            });
        }
    },

    lifetimes: {
        attached() {
            this.triggerEvent("attached");
            let pageId = this.getPageId();
            AsyncCommitPopModule.onShow(params => {
                console.log(TAG, 'TaskProgressModule onShow');
                let {task, masterData, actionCallback} = params;
                let updateData = this.getUpdateDataByTask(task);
                this.task = task;
                this.masterData = masterData;
                this.actionCallback = actionCallback;
                this.setData(Object.assign({
                    dShow: true,
                    dScrollViewHeight: 300,
                    dTitle: i18n('ava.object_form.async_commit.title')/*订单创建中*/,
                    dWaitInfo: i18n('ava.object_form.async_commit.wait_info')/*订单处理中请耐心等待，暂时无法进行其他操作*/,
                }, updateData));
            }, pageId);

            AsyncCommitPopModule.onRefresh(params => {
                console.log(TAG, 'TaskProgressModule onRefresh')
                let {task, masterData} = params || {};
                let updateData = this.getUpdateDataByTask(task);
                this.task = task;
                this.masterData = masterData;
                this.setData(updateData);
                let taskStatus = task && task.getStatus();
                if (taskStatus === TASK_STATUS.SUCCESS) {//订单新建成功后，自动关闭弹窗和新建页
                    console.log(TAG, 'TaskProgressModule task success then goto detail');
                    this.onButtonClick({currentTarget: {dataset: {actiontype: ActionType.VIEW_DETAIL}}});
                }
            }, pageId);
        }
    }
})