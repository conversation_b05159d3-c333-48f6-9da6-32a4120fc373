import {request} from "../../pluginbase-ava/package/pluginutils";

export default class DynamicAmountApi {

    constructor(http) {
        this.http = http;
    }

    createJob(queryParam) {
        let param = this.getCreateJobParams(queryParam);
        return this.http(param);
    }

    createJobAndParseResult(queryParam) {
        let param = this.getCreateJobParams(queryParam);
        return request(this.http, param).then(rst => {
            return rst || {};
        });
    }

    getCreateJobParams(queryParam) {
        return {
            url: `FHH/EM1HJobCenter/inputJobCenter/createJob`,
            data: {
                apiName: 'SalesOrderObj',
                templateId: 'sfa_sales_order',
                jobType: '5',
                queryParam: queryParam
            }
        }
    }

    queryJobState(jobId) {
        return request(this.http, {
            url: `FHH/EM1HJobCenter/inputJobCenter/queryJobState`,
            data: {
                jobId
            },
        }).then(rst => {
            return rst || {};
        });
    }
}