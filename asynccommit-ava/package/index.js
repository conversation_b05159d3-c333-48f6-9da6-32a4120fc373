import log from "../../pluginbase-ava/package/log";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import Async<PERSON>ommit<PERSON><PERSON> from "./AsyncCommitApi";
import {AsyncCommiter} from './AsyncCommiter'
import {isDraft, isEmpty} from "../../pluginbase-ava/package/pluginutils";
import failReasonHandler from "./failReasonHandler";

export default class AsyncCommit {
    constructor(pluginService, pluginParam) {
        let {bizStateConfig, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        this.pluginApi = new PluginApi(pluginService);
        let context = {
            bizStateConfig: this.bizStateConfig,
            pluginApi: this.pluginApi,
            requestApi: new AsyncCommitApi(pluginService.api.request),
        }
        this.asyncCommiter = new AsyncCommiter(context);
    }

    formRenderAfter(pluginExecResult, options) {
        let {dataUpdater} = options;
        dataUpdater.setBtnHidden({btnApiName: 'Add_Save_Continue_button_default', status: true});//隐藏提交并新建按钮
    }

    formRenderEnd(pluginExecResult, options) {
        let {dataGetter} = options;
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        let masterData = dataGetter.getMasterData();
        let {async_commit_fail_reason} = masterData || {};
        if (!isDraft(sourceAction) || isEmpty(async_commit_fail_reason)) {
            return;
        }
        let asyncCommitFailReason = JSON.parse(async_commit_fail_reason);
        failReasonHandler.handle(asyncCommitFailReason, this.pluginApi)
    }

    formSubmitPostBefore(pluginExecResult, options) {
        return this.asyncCommiter.formSubmitPostBefore(pluginExecResult, options);
    }

    apply() {
        let asyncCreateOrder = this.bizStateConfig.asyncCreateOrder();
        return asyncCreateOrder ? [{
            event: "form.render.after",
            functional: this.formRenderAfter.bind(this)
        }, {
            event: "form.render.end",
            functional: this.formRenderEnd.bind(this)
        }, {
            event: "form.submit.post.before",
            functional: this.formSubmitPostBefore.bind(this)
        }] : [];
    }
}