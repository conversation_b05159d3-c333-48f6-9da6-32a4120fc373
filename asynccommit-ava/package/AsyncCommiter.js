import AsyncCommitPopModule from '../../pluginbase-ava/package/modules/AsyncCommitPopModule';
import {isEdit, isEmpty, parseHttpRst, requireAsync, uuid} from "../../pluginbase-ava/package/pluginutils";
import {ActionType, TAG, TASK_STATUS} from "./enum";

export class AsyncCommiter {

    constructor(context) {
        let {pluginApi, requestApi} = context;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async formSubmitPostBefore(pluginExecResult, options) {
        let {dataGetter, postParams} = options;
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        if (isEdit(sourceAction)) {//编辑场景不支持异步化
            return;
        }
        let self = this;
        let masterData = dataGetter.getMasterData();
        let pageId = dataGetter.getPageId();
        let {data: queryParam} = postParams || {};
        let token = 'async_commit_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        return self.requestApi.createJob(queryParam)
            .then(async postResult => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                let module = await requireAsync('../../business-cmpt-paas-sub/business/crm/asynccommit/looper');
                let looper = module.default || module;
                if (!looper) {
                    self.pluginApi.showToast('async commit looper can not be null');
                    return {consumed: true}
                }
                return new Promise(async (resolve, reject) => {
                    let jobResult = parseHttpRst(postResult);
                    looper.init({timeout: 60000});//超过60秒按超时处理
                    looper.start(jobResult, task => {
                        console.log(TAG, 'taskChanged task = ' + JSON.stringify(task));
                        self.refreshPop(task, masterData, (actionType, task) => {
                            let isReedit = actionType === ActionType.REEDIT;
                            if (isReedit) {
                                let message = task.getMessage();
                                resolve({postResult: message});
                            } else {
                                resolve({consumed: true});
                            }
                            looper.stop && looper.stop();
                        }, pageId);
                    });
                });
            })
            .catch(err => {
                this.pluginApi.showToast(err);
                this.pluginApi.hideSingletonLoading(token, pageId);
                return {consumed: true}
            });
    }

    refreshPop(task, masterData, actionCallback, pageId) {
        if (isEmpty(task)) {
            return;
        }
        let taskStatus = task.getStatus();
        if (taskStatus === TASK_STATUS.CREATED) {
            AsyncCommitPopModule.show({task, masterData, actionCallback}, pageId);
        } else {
            AsyncCommitPopModule.refresh({task, masterData}, pageId);
        }
    }
}