{"compileOnSave": false, "compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "es5", "module": "CommonJS", "rootDir": "./", "noImplicitAny": false, "lib": ["esnext", "dom"]}, "exclude": ["__test__", "helpers", "node_modules"]}