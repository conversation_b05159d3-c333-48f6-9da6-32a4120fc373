
export type StrArrayMap = {
    [props: string]: Array<string>;
}

export interface FieldMapFace {
    bom_id: string;
    product_id: string;
    attribute: string;
    attribute_json: string;
    nonstandard_attribute: string;
    nonstandard_attribute_json: string;
    quantity:string;
    product_price:string;
}
export interface ChangeInfoFace {
    masterUpdate: any;
    mdUpdate: any;
    mdAdd: Array<any>;
    mdDel: Array<any>;
}
export interface ModifyInfoFace {
    modifyFields: StrArrayMap;
    modifyIndex: Array<string>;
}

export interface DetailMapFace {
    [rowId: string]: any;
}
export interface ExecuteFace {
    masterData: any;
    detailDataMap: DetailMapFace;
    changeInfo: ChangeInfoFace;
    modifyInfo: ModifyInfoFace;
    type:string;
    triggerCal:any;
    config:any;
}

/**
 * 公式相关 
 */
export interface ModifiedIndexAndFields {
    [rowId: string]: Set<string>
}

export interface TriggerInfo {
    isTrigger: boolean;
    triggerParam: StrArrayMap
}

//接口返回数据格式

export interface ResFormulaFetcher {
    [id: string]: {
        [objApiname:string]:{
            [field: string]: {
                calculateFields:{
                    [objApiname:string]:Array<string>
                } 
            }
        } 
    }
}
//格式化本地存储格式
//id:product_id|bom_id
interface DetailTriggerMap {
    [dataId: string]: StrArrayMap
}
interface MasterTriggerMap {
    [triggerField:string]:{
        [dataId:string]: Array<string>
    }
}

export interface FormulaDicFace {
    detailFormula: DetailTriggerMap,
    masterFormula: MasterTriggerMap
};


/**
 * 高级公式计算接口相关
 */
export interface ReqQuoterCal {
    detailObjectApiName: string;
    masterData: any;
    detailDataMap: {
        [key: string]: any
    };
    detailCalculateFieldApiNames: {
        [objectApi: string]: StrArrayMap;
    }
    fieldsMap: {
        [key: string]: string
    };
}

export interface ResQuoterCal {
    [dataIndex: string]: {
        data_index: string;
        [props: string]: any;
    }
}

export interface ReqBatchCalFace {
    noLoading: boolean;
    changeFields: Array<string>;
    filterFields: StrArrayMap;
    extraFields: StrArrayMap;
    operateType: string;
    dataIndex: Array<string>;
    objApiName: string;
    masterData: object;
    details: {
        [api: string]: Array<any>;
    };
}

export interface ResBatchCalFace {
    Result: {
        StatusCode: number;
    },
    Value: ResQuoterCal
}

export interface QuoterEngineResult {
    statusCode: number;
    data: any | undefined;
    message: string | undefined;

}