
import {
    StrArrayMap,
    FieldMapFace,
    ModifyInfoFace,
    DetailMapFace,
    ExecuteFace,
    ChangeInfoFace,
    FormulaDicFace,
    ResFormulaFetcher,
    ModifiedIndexAndFields,
    TriggerInfo,
    ReqQuoterCal,
    ResQuoterCal,
    ReqBatchCalFace,
    ResBatchCalFace,
    QuoterEngineResult
} from './data_interface';

import PPM from 'plugin_public_methods';

export default class QuoterEngine {
    public formulaDic: FormulaDicFace;
    constructor(
        public requestId: string,
        public masterApiName: string,
        public detailApiName: string,
        public fieldMap: FieldMapFace,
        public request: any,
    ) {
        this.formulaDic = {
            detailFormula: {},
            masterFormula: {}
        };
    }

    /**************************** 报价器高级公式计算 ****************************/
    //高级公式批量计算
    public async executeQuoterBatch(param: ExecuteFace) {
        const { detailDataMap, changeInfo } = param,
            { mdAdd = [] } = changeInfo;
        if (mdAdd?.length) {
            await this.fetchFormulas(mdAdd);
        }
        const { isTrigger, triggerParam } = this.checkTrigger(changeInfo, detailDataMap);
        if (!isTrigger) {
            return this.generateRes(0);
        }
        const result = await this.quoterCalculate(param, triggerParam);
        return result;
    }

    /**
    * 高级公式查询接口调用
    */
    public async fetchFormulas(mdAdd: Array<any>) {
        await PPM.composeAsync(
            this.parseFormulaFetcherRes.bind(this),
            this.reqFormulaFetcher.bind(this),
            this.parseFormulaFetcherArgs.bind(this)
        )(mdAdd);
    }

    private parseFormulaFetcherArgs(mdAdd: Array<any>) {
        const { bom_id, product_id } = this.fieldMap;
        const idsMap = {
            bom_id: new Set(),
            product_id: new Set()
        };
        // 收集bom_id和product_id
        mdAdd.forEach(d => {
            if (d[bom_id]) {
                idsMap.bom_id.add(d[bom_id]);
            } else {
                idsMap.product_id.add(d[product_id]);
            }
        });
        let initialWhere: any = [];
        // 构建查询条件
        const wheres = Object.entries(idsMap).reduce((accum, [field, idsSet]) => {
            if (idsSet.size > 0) {
                accum.push({
                    connector: "OR",
                    filters: [{
                        field_name: field,
                        field_values: Array.from(idsSet),
                        operator: "IN"
                    }, {
                        field_name: "ref_object_api_name",
                        field_values: [this.detailApiName],
                        operator: "EQ"
                    }
                    ]
                });
            }
            return accum;
        }, initialWhere),
            queryInfoStr = JSON.stringify({
                "wheres": wheres
            });
        return {
            "queryInfo": queryInfoStr,
            "apiName": this.detailApiName
        }
    }

    private async reqFormulaFetcher(args: any) {
        const url = "FHH/EM1HNCRM/API/v1/object/quoter_sfa/service/queryAdvancedFormulaList",
            result = await PPM.ajax(this.request, url, args);
        return result;
    }

    private parseFormulaFetcherRes(result: ResFormulaFetcher) {
        const { masterApiName, detailApiName } = this;
        const masterMap = this.formulaDic.masterFormula;

        const processFieldInfo = (fieldInfo: any) => {
            return fieldInfo.calculateFields?.[detailApiName] || [];
        };

        Object.entries(result).forEach(([dataId, data]) => {
            // 从对象字段处理
            const detailFieldMap: StrArrayMap = {};
            Object.entries(data[detailApiName] || {}).forEach(([triggerField, fieldInfo]) => {
                detailFieldMap[triggerField] = processFieldInfo(fieldInfo);
            });
            this.formulaDic.detailFormula[dataId] = detailFieldMap;

            // 主对象字段处理
            Object.entries(data[masterApiName] || {}).forEach(([triggerField, fieldInfo]) => {
                masterMap[triggerField] = masterMap[triggerField] || {};
                masterMap[triggerField][dataId] = processFieldInfo(fieldInfo);
            });
        });

        this.formulaDic.masterFormula = masterMap;
    }

    /**
    * 判断是否触发报价计算
    * 收集触发计算的参数
    */
    private checkTrigger(changeInfo: ChangeInfoFace, detailDataMap: DetailMapFace): TriggerInfo {

        const triggerParam: StrArrayMap = PPM.compose(
            this.formatTriggerParam.bind(this),
            PPM.partial(this.getMasterTrigger.bind(this), detailDataMap, changeInfo).bind(this),
            PPM.curry(2, this.getDetailTrigger.bind(this))(detailDataMap)
        )(changeInfo)

        return {
            isTrigger: Object.keys(triggerParam).length > 0,
            triggerParam
        }
    }

    private getDetailTrigger(detailDataMap: DetailMapFace, changeInfo: ChangeInfoFace): ModifiedIndexAndFields {
        const needModifyData: ModifiedIndexAndFields = {},
            { bom_id, product_id } = this.fieldMap,
            { detailFormula = {} } = this.formulaDic,
            { mdAdd = [], mdUpdate = {} } = changeInfo,
            newDataIds = mdAdd.map(d => d.rowId);

        // 更新触发参数
        const updateTrigger = (dataItem: any, rowId: string, type: string, editItem: any = {}) => {

            const id = dataItem[bom_id] ? dataItem[bom_id] : dataItem[product_id];
            if (detailFormula?.[id]) {
                const calFields = new Set<string>();
                if (type === "add") {
                    Object.values(detailFormula[id]).forEach(triggerCalFields => {
                        triggerCalFields.forEach(field => calFields.add(field));
                    });
                } else {
                    Object.keys(editItem).forEach(mu => {
                        (detailFormula[id][mu] || []).forEach(field => calFields.add(field));
                    })
                }
                if (calFields.size > 0) {
                    needModifyData[rowId] = calFields;
                }
            }
        };

        mdAdd.forEach(d => updateTrigger(d, d.rowId, "add"));
        Object.keys(mdUpdate)
            .filter(rowId => !newDataIds.includes(rowId))
            .forEach(rowId => updateTrigger(detailDataMap[rowId], rowId, "edit", mdUpdate[rowId]));
        return needModifyData;
    }

    private getMasterTrigger(detailDataMap: DetailMapFace, changeInfo: ChangeInfoFace, needModifyData: ModifiedIndexAndFields): ModifiedIndexAndFields {
        const { masterFormula = {} } = this.formulaDic,
            { bom_id, product_id } = this.fieldMap,
            idMap = Object.values(detailDataMap).reduce((accMap, item) => {
                const dataId = item[bom_id] || item[product_id];
                accMap[dataId] = accMap[dataId] || [];
                accMap[dataId].push(item.rowId);
                return accMap;
            }, {});

        const handleMasterUpdate = (key: string) => {
            const targetObj = masterFormula[key] || {};
            Object.entries(targetObj).forEach(([dataId, triggerCalFields]) => {
                const rowIds = idMap[dataId] || [];
                rowIds.forEach((rId: string) => {
                    const calFields = needModifyData[rId] || new Set<string>();
                    triggerCalFields.forEach(field => calFields.add(field));
                    if (calFields.size > 0) {
                        needModifyData[rId] = calFields;
                    }
                })
            });
        };

        Object.keys(changeInfo.masterUpdate || {}).forEach(handleMasterUpdate);
        return needModifyData;
    }

    private formatTriggerParam(needModifyData: ModifiedIndexAndFields): StrArrayMap {
        const triggerParam: StrArrayMap = {};
        Object.keys(needModifyData).forEach(rowId => {
            triggerParam[rowId] = Array.from(needModifyData[rowId]);
        });
        return triggerParam;
    }

    /**
    * 报价器计算
    */
    private async quoterCalculate(param: ExecuteFace, triggerParam: StrArrayMap) {
        if (param.type == 'add') {
            //如果新增数据有高级公式，对新增数据取默认值
            await this.preCalAddData(param, triggerParam);
        }
        return await PPM.composeAsync(
            this.verityQuoterCalRes.bind(this),
            this.reqQuoterCal.bind(this),
            PPM.curry(2, this.parseQuoterCalArgs.bind(this))(param),
        )(triggerParam)
    }

    // 选数据场景，执行报价器计算前先调用计算接口计算一遍默认值。对于bom数据，根据配置不更新部分字段
    private async preCalAddData(param: ExecuteFace, triggerParam: StrArrayMap) {
        const { masterData, detailDataMap, changeInfo, type, config = {} } = param,
            addData = changeInfo.mdAdd || [],
            initDataMap: any = {};
        Object.keys(triggerParam).forEach(key => {
            if (addData.some(d => d.rowId == key)) {
                initDataMap[key] = detailDataMap[key]
            }
        });
        const initDataKeys = Object.keys(initDataMap);
        if (initDataKeys.length >= 1) {
            const changeInfo = {} as ChangeInfoFace;
            const args = this.parseCalArgs({
                ...param,
                detailDataMap: initDataMap,
                modifyInfo: {
                    modifyFields: {},
                    modifyIndex: initDataKeys
                }
            }, "mdAdd");
            const result = await param.triggerCal({
                ...args,
                noMerge: true,
            });
            this.updateAddData(param, result);
        }
    }

    /**
     * 高级公式计算接口调用
     */
    private parseQuoterCalArgs(param: ExecuteFace, triggerParam: StrArrayMap): ReqQuoterCal {
        const { masterData, detailDataMap, modifyInfo: { modifyFields, modifyIndex } } = param;
        const { bom_id, product_id, attribute, attribute_json, nonstandard_attribute, nonstandard_attribute_json } = this.fieldMap;
        return {
            detailObjectApiName: this.detailApiName,
            masterData: masterData,
            detailDataMap: detailDataMap,
            detailCalculateFieldApiNames: {
                [this.detailApiName]: triggerParam
            },
            fieldsMap: {    //字段映射
                bom_id,
                product_id,
                attribute,
                attribute_json: "attribute_json",
                nonstandard_attribute,
                nonstandard_attribute_json: "nonstandard_attribute_json"
            }
        }
    }

    private async reqQuoterCal(reqParam: ReqQuoterCal): Promise<ResQuoterCal> {
        const url = "FHH/EM1HNCRM/API/v1/object/quoter_sfa/service/batchCalculate";
        const result = await PPM.ajax(this.request, url, reqParam);
        return result;
    }

    private verityQuoterCalRes(result: ResQuoterCal): QuoterEngineResult {
        if (result) {
            return this.generateRes(0, result)
        } else {
            return this.generateRes(1, null, "报价器计算失败，请重试")
        }
    }

    /**************************** 底层计算接口计算 ****************************/
    /**
     * 底层计算接口调用
     */
    public async batchCalculate(param: ExecuteFace, triggerCal: any): Promise<QuoterEngineResult> {
        return await PPM.composeAsync(
            this.verityCalRes.bind(this),
            triggerCal,
            this.parseCalArgs.bind(this)
        )(param)
    }
    //格式化计算接口入参
    private parseCalArgs(param: ExecuteFace, operateType: string = "mdEdit"): ReqBatchCalFace {
        const { masterData, detailDataMap, modifyInfo: { modifyFields, modifyIndex } } = param;
        return {
            noLoading: true,
            changeFields: [],
            filterFields: {},
            extraFields: modifyFields,
            operateType: operateType,
            dataIndex: modifyIndex,
            objApiName: this.detailApiName,
            masterData: masterData,
            details: {
                [this.detailApiName]: Object.values(detailDataMap || {})
            }
        }
    }

    private verityCalRes(result: ResBatchCalFace): QuoterEngineResult {
        if (result?.Result?.StatusCode === 0) {
            return this.generateRes(0);
        } else {
            return this.generateRes(1, null, "计算失败");
        }
    }

    //组织返回结果
    public generateRes(statusCode: number, data?: any, message?: string): QuoterEngineResult {
        return {
            statusCode,
            message,
            data
        }
    }

    //
    public getFormulaDic(){
        return this.formulaDic;
    }

    private updateAddData(param: ExecuteFace, result: any) {
        const { detailDataMap = {}, config = {} } = param,
            excludedBomDefFields = config.excludedBomDefFields || [];
        const { bom_id } = this.fieldMap;

        const resMap:ResQuoterCal = result?.Value?.calculateResult?.[this.detailApiName];
        if (!resMap) {
            return;
        }

        Object.entries(resMap).forEach(([key, item]) => {

            if (detailDataMap[key]) {
                const excludeFields = detailDataMap[key][bom_id] ? excludedBomDefFields : [];
                Object.keys(item).forEach(field =>{
                    if (!excludeFields.includes(field)) {
                        detailDataMap[key][field] = item[field];
                    }
                })

            }

        });

    }
}
