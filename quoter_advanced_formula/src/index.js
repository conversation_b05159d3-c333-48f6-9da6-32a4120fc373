import Base from "plugin_base";
import QuoterEngine from './package/core';
import PPM from 'plugin_public_methods';
export default class QuoterAdvancedFormula extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.pluginUniqId = "quoter" + PPM.uniqueCode();
    }

    async init(param, bomPlugin) {
        this.initFields(param, bomPlugin);
        this.initModule(param);
        await this.initFormulas(param);
    }

    //初始化字段/小数位数信息
    initFields(param, bomPlugin) {
        this.masterApi = param.masterObjApiName;
        this.detailApi = param.objApiName;
        this.fieldMap = this.getAllFields(this.detailApi);
        if (bomPlugin) {
            const detailPlugin = (bomPlugin.params?.details || []).find(d => d.objectApiName == this.detailApi);
            this.fieldMap.bom_id = detailPlugin?.fieldMapping?.bom_id || "bom_id";
            this.fieldMap.product_price = detailPlugin?.fieldMapping?.product_price || "product_price";
        }
    }

    initModule(param) {
        const requestId = param.dataGetter.getRequestId(),
            masterData = param.dataGetter.getMasterData();
        this.quoterEngine = new QuoterEngine(
            requestId,
            this.masterApi,
            this.detailApi,
            this.fieldMap,
            this.request
        )
    }

    //初始化时，缓存当前数据的高级公式。如编辑、复制、转换等场景
    async initFormulas(param) {
        const details = param.dataGetter.getDetail(this.detailApi);
        if (details.length) {
            await this.quoterEngine.fetchFormulas(details);
        }
    }

    //初始化需要重算高级公式，计算全部数据
    async initDataByQuoter(param) {
        param.isInit = true;
        await this.executeQuoter(param, {
            type:"init"
        });
    }

    //执行报价器
    async executeQuoter(param, config) {

        const needQuoter = this.preCheckForQuoter(param);
        if (!needQuoter) {
            return;
        }
        this.showLoading();

        param.type = config.type;
        param.config = config;

        const result = await PPM.composeAsync(
            PPM.curry(2, this.executeQuoterAfter.bind(this))(param),
            this.executeQuoterCore.bind(this),
        )(param);

        if (result?.statusCode !== 0) {
            result.message && this.alert(result.message);
        }
        this.hideLoading();
        return result;
    }

    // 获取有高级公式的行：根据指定字段获取
    getHasFormulaRows(field){
        const result = {};
        const {detailFormula = {}} = this.quoterEngine.getFormulaDic();
        Object.entries(detailFormula).forEach(([key, value]) => {
            Object.values(value).forEach(fields => {
                if (fields.includes(field)) {
                    if (!result[field]) {
                        result[field] = [];
                    }
                    result[field].push(key);
                }
            });
        });
        return result;
    }

    //报机器是否需要执行检查
    preCheckForQuoter(param) {
        const details = param.dataGetter.getDetail(this.detailApi);
        return details.length > 0;
    }
    //执行报价器逻辑：获取公式&计算
    async executeQuoterCore(param) {
        const result = await PPM.composeAsync(
            PPM.curry(2, this.updateQuoterResult.bind(this))(param),
            this.quoterEngine.executeQuoterBatch.bind(this.quoterEngine),
            this.parseQuoterArgs.bind(this),
        )(param);
        return result;
    }

    parseQuoterArgs(param) {
        const args = this.getCommonArgs(param);
        return args;
    }

    //报价器计算结果更新
    updateQuoterResult(param, result = {}) {
        const { statusCode, data = {}, message } = result;

        if (result?.statusCode !== 0) {
            return result;
        }

        const modifyIndex = [],
            modifyFields = [];

        Object.values(data).forEach((item = {}) => {
            const updateKeys = Object.keys(item),
                rowId = item.data_index;
            if (updateKeys.length) {
                //更新数据
                param.dataUpdater.updateDetail(this.detailApi, rowId, item);
                param.dataUpdater.setReadOnly({
                    dataIndex: rowId,
                    fieldName: Object.keys(item || {}),
                    status: true,
                    operateId: this.pluginUniqId
                });
                //收集更新结果
                modifyIndex.push(rowId);
                modifyFields.push(...updateKeys);
            }
        })
        return {
            ...result,
            quoterModifyInfo: {
                modifyIndex,
                modifyFields: [...new Set(modifyFields)]
            }
        }
    }


    //报价器计算后事件：例如底层计算
    async executeQuoterAfter(param, quoterRes = {}) {
        if (quoterRes?.statusCode !== 0) {
            return quoterRes;
        }
        const { quoterModifyInfo: { modifyIndex = [], modifyFields = [] } } = quoterRes,
             needCalFields = this.getBatchCalFields(param,modifyFields);
        // if (Object.keys(needCalFields).length <= 0) {
        //     return this.quoterEngine.generateRes(0);
        // }
        // const args = {
        //     ...(this.getCommonArgs(param)),
        //     modifyInfo: {
        //         modifyIndex,
        //         modifyFields: needCalFields
        //     }
        // },
        //     result = await this.quoterEngine.batchCalculate(args, param.triggerCal);
        return {
            //...result,
            ...quoterRes,
            quoterRes: quoterRes.data,
            needCalFields
        }
    }

    //获取计算接口要计算的字段
    getBatchCalFields(param, modifyFields) {
        if (modifyFields.length === 0) {
            return {};
        }
        const needCalFields = param.dataGetter.getCalculateFieldsByFieldName(modifyFields, false, this.detailApi) || {};
        needCalFields[this.detailApi] = (needCalFields[this.detailApi] || []).filter(f => !modifyFields.includes(f));
        if (needCalFields[this.detailApi].length === 0) {
            delete needCalFields[this.detailApi];
        }
        return needCalFields;
    }

    getCommonArgs(param) {
        let masterData = param.dataGetter.getMasterData();
        const detailsArr = param.dataGetter.getDetail(this.detailApi),
            detailsMap = detailsArr.reduce((dataMap, d) => {
                dataMap[d.rowId] = d;
                return dataMap;
            }, {}),
            { mdInsert = [], mdAdd = [] } = param.collectChange(),
            { changeData = {} } = param;

        const changeInfo = {
            mdAdd: mdAdd
        };
        if (mdInsert.length>0) {
            const insertData = mdInsert.flatMap(item => item.datas);
            changeInfo.mdAdd = [...mdAdd, ...insertData];
        }

        switch (param.type) {
            case "init":
                changeInfo.mdAdd = detailsArr;
                break;
            case "masterEdit":
                changeInfo.masterUpdate = changeData;
                masterData = {
                    ...masterData,
                    ...changeData
                }
                break;
            case "detailEdit":
                changeInfo.mdUpdate = changeData;
                Object.entries(changeData).forEach(([key, item]) => {
                    detailsMap[key] = {
                        ...detailsMap[key],
                        ...item
                    }
                })
                break;
        }
        return {
            masterData,
            detailDataMap: detailsMap,
            changeInfo,
            modifyInfo: {},
            type: param.type,
            triggerCal: param.triggerCal,
            config:param.config
        };
    }
}