const path = require("path");

module.exports = {
  entry: "./src/index.js",
  output: {
    path: path.resolve(__dirname, "dist"),
    filename: "index.js",
    libraryTarget: "umd", // 将库导出为通用模块定义 (UMD) 格式
    library: "Rebate", // 库的名称
  },
  target: ['web', 'es5'],
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        exclude: /(__test__|node_modules|helpers)/,
        use: {
          loader: 'ts-loader',
          options:{
            transpileOnly: true,
          }
        },
      },
      {
        test: /\.(js|jsx)$/,
        exclude:  /(__test__|node_modules|helpers)/,
        use: {
          loader: "babel-loader",
          options: {
            presets: [
              '@babel/preset-env'
            ],
            cacheDirectory: false, 
          }
        }
      },
    ],
  },
  stats: {
    children: true
  },
};
