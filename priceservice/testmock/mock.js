const masterApiName = 'QuoteObj';
const mdApiName = 'QuoteLinesObj';

const masterData = {
    "quoter_json__c": "{\"version\":5,\"selectedData\":{}}",
    "lock_rule": null,
    "field_2v87e__c": "0.00",
    "base_quote_amount__r": "CNY 200.00",
    "account_id__r": "张三",
    "mc_exchange_rate": "1.000000",
    "currency_base_quote_amount": {
        "prefix": "CNY",
        "suffix": "",
        "fieldApiName": "currency_base_quote_amount",
        "objectApiName": "QuoteObj"
    },
    "extend_obj_data_id": "665ec3198ad1e00007a4cfdd",
    "created_by__r": {
        "picAddr": "",
        "mobile": "***********",
        "description": "",
        "dept": "1000",
        "supervisorId": null,
        "title": null,
        "empNum": "",
        "modifyTime": *************,
        "post": "",
        "createTime": *************,
        "phone": "",
        "name": "CRM管理员",
        "nickname": "CRM管理员",
        "tenantId": "90242",
        "id": "1000",
        "position": null,
        "enterpriseName": null,
        "email": "",
        "status": 0
    },
    "life_status_before_invalid": null,
    "owner_department_id": "1000",
    "owner_department": "研发部门",
    "field_G22r2__c": null,
    "attribute_constraint_id__c": "65a61c52527aa20007d139eb",
    "lock_status": "0",
    "package": "CRM",
    "create_time": 1717486361730,
    "new_opportunity_id": null,
    "attribute_constraint_id__c__r": "Test",
    "quoter_label__c": null,
    "version": "2",
    "created_by": [
        "1000"
    ],
    "relevant_team": [
        {
            "teamMemberEmployee": [
                "1000"
            ],
            "teamMemberRole": "1",
            "teamMemberRoleList": [
                "1"
            ],
            "teamMemberPermissionType": "2",
            "outTenantId": "",
            "sourceType": "",
            "teamMemberType": "0",
            "teamMemberDeptCascade": "0"
        }
    ],
    "quote_amount": "200.00",
    "quote_discount": "100.0000",
    "data_own_department": [
        "1000"
    ],
    "price_book_id": "",
    "name": "2024000000000204",
    "quote_product_sum": "200.00",
    "_id": "665ec3188ad1e00007a4ce2c",
    "tenant_id": "90242",
    "quote_time": null,
    "field_IC8c9__c": "2000.00",
    "origin_source": null,
    "lock_user": null,
    "quoter_json__c__o": "{\"version\":5,\"selectedData\":{}}",
    "is_deleted": false,
    "partner_id": "",
    "bom_created_status": false,
    "object_describe_api_name": "QuoteObj",
    "owner__l": [
        {
            "id": "1000",
            "tenantId": "90242",
            "name": "CRM管理员",
            "picAddr": "",
            "email": "",
            "nickname": "CRM管理员",
            "phone": "",
            "description": "",
            "status": 0,
            "createTime": *************,
            "modifyTime": *************,
            "dept": "1000",
            "post": "",
            "empNum": ""
        }
    ],
    "out_owner": null,
    "relevant_team__r": "CRM管理员",
    "out_resources": null,
    "mc_functional_currency": "CNY",
    "owner__r": {
        "picAddr": "",
        "mobile": "***********",
        "description": "",
        "dept": "1000",
        "supervisorId": null,
        "title": null,
        "empNum": "",
        "modifyTime": *************,
        "post": "",
        "createTime": *************,
        "phone": "",
        "name": "CRM管理员",
        "nickname": "CRM管理员",
        "tenantId": "90242",
        "id": "1000",
        "position": null,
        "enterpriseName": null,
        "email": "",
        "status": 0
    },
    "field_Du5ak__c": "200.00",
    "field_1q3w8__c": null,
    "owner": [
        "1000"
    ],
    "last_modified_time": 1717486363447,
    "attribute_constraint_id__c__relation_ids": "65a61c52527aa20007d139eb",
    "life_status": "normal",
    "last_modified_by__l": [
        {
            "id": "1000",
            "tenantId": "90242",
            "name": "CRM管理员",
            "picAddr": "",
            "email": "",
            "nickname": "CRM管理员",
            "phone": "",
            "description": "",
            "status": 0,
            "createTime": *************,
            "modifyTime": *************,
            "dept": "1000",
            "post": "",
            "empNum": ""
        }
    ],
    "last_modified_by": [
        "1000"
    ],
    "field_w3VKg__c": "200.00",
    "out_tenant_id": null,
    "mc_currency": "CNY",
    "base_quote_amount": "200.00",
    "record_type": "default__c",
    "account_id": "65966ccac6f30e0007f8cdde",
    "account_id__relation_ids": "65966ccac6f30e0007f8cdde",
    "order_by": null,
    "mc_exchange_rate_version": "*************",
    "base_quote_product_sum": "200.000",
    "pricebook_id": "",
    "price_book_id__r": ""
};
const addDatas = [{
    "record_type": "default__c",
    "object_describe_api_name": "QuoteLinesObj",
    "rowId": "****************",
    "object_describe_id": "6595044ac7150800070da5dd",
    "lock_rule": "default_lock_rule",
    "life_status": "normal",
    "lock_status": "0",
    "quantity": "1",
    "print_hierarchy": ".",
    "price_book_product_id": "665ebfae8ad1e00007a4a1df",
    "price_book_product_id__r": "PBProdCode20240604001700",
    "product_id": "665ebf388ad1e00007a49bb4",
    "product_id__r": "扫地机器人",
    "price_book_id": "66597bc3ebd8080007bac1f0",
    "price_book_id__r": "阶梯价目表",
    "discount": "100",
    "price_book_price": "300.00"
}]
const lookupDatas = [
    {
        "pricebook_id__relation_ids": "66597bc3ebd8080007bac1f0",
        "discount": "100",
        "product_status__v": "1",
        "is_saleable": "是",
        "product_status__r": "已上架",
        "mc_exchange_rate": "1.000000",
        "currency_pricebook_sellingprice": {
            "prefix": "CNY",
            "suffix": "",
            "fieldApiName": "currency_pricebook_sellingprice",
            "objectApiName": "PriceBookProductObj"
        },
        "product_id__ro": {
            "version": "2",
            "product_status": "已上架",
            "field_s27tu__c": "扫地机器人",
            "extend_obj_data_id": "665ebf388ad1e00007a49bd9",
            "lock_status__v": "0",
            "object_describe_api_name": "ProductObj",
            "is_saleable__v": true,
            "record_type": "预设业务类型",
            "product_status__v": "1",
            "field_AhbB1__c": "0.00",
            "tenant_id": "90242",
            "on_shelves_time": "2024-06-04 15:16",
            "product_category_id__relation_ids": "6594c7c0cfd011000162fde1",
            "category__v": "4",
            "is_giveaway": "否",
            "unit": "个",
            "_id": "665ebf388ad1e00007a49bb4",
            "life_status__v": "normal",
            "is_giveaway__v": "0",
            "is_saleable": "是",
            "owner_department_id": "1000",
            "unit__v": "1",
            "product_category_id": "6594c7c0cfd011000162fde1",
            "is_deleted__v": false,
            "owner_department": "研发部门",
            "create_time": "2024-06-04 15:16",
            "name": "扫地机器人",
            "price": "300.00",
            "searchAfterId": [
                "1717485368787",
                "665ebf388ad1e00007a49bb4"
            ],
            "product_code": "扫地机器人",
            "is_package": "否",
            "last_modified_time": "2024-06-04 15:16",
            "life_status": "正常",
            "lock_status": "未锁定",
            "package": "CRM",
            "display_name": "扫地机器人",
            "is_deleted": "否",
            "picture_path": [],
            "is_package__v": false,
            "category": "家用电器"
        },
        "pricebook_sellingprice__r": "CNY 300.00",
        "product_id": "665ebf388ad1e00007a49bb4",
        "owner_department_id": "1000",
        "total_num": 2,
        "owner_department": "研发部门",
        "searchAfterId": [
            "PBProdCode20240604001700",
            "665ebfae8ad1e00007a4a1df"
        ],
        "pricebook_sellingprice": "300.00",
        "barcode": "",
        "pricebook_id": "66597bc3ebd8080007bac1f0",
        "lock_status": "0",
        "package": "CRM",
        "create_time": 1717485486494,
        "product_life_status": "正常",
        "product_category__v": "6594c7c0cfd011000162fde1",
        "version": "1",
        "created_by": [
            "1000"
        ],
        "product_name": "扫地机器人",
        "unit": "个",
        "data_own_department": [
            "1000"
        ],
        "name": "PBProdCode20240604001700",
        "_id": "665ebfae8ad1e00007a4a1df",
        "start_count": "0",
        "stand_price": "300.00",
        "tenant_id": "90242",
        "specification_value_set": "",
        "product_life_status__r": "正常",
        "product_status": "已上架",
        "product_code": "扫地机器人",
        "product_id__r": "扫地机器人",
        "product_id__relation_ids": "665ebf388ad1e00007a49bb4",
        "is_deleted": false,
        "object_describe_api_name": "PriceBookProductObj",
        "end_count": "10",
        "product_life_status__v": "normal",
        "mc_functional_currency": "CNY",
        "is_saleable__v": true,
        "owner": [
            "1000"
        ],
        "last_modified_time": 1717485486494,
        "is_package": "否",
        "unit__r": "个",
        "life_status": "normal",
        "last_modified_by": [
            "1000"
        ],
        "pricebook_price": "300.00",
        "pricebook_id__r": "阶梯价目表",
        "mc_currency": "CNY",
        "is_package__v": false,
        "record_type": "default__c",
        "category__v": "4",
        "unit__v": "1",
        "mc_exchange_rate_version": "*************",
        "category": "家用电器",
        "category__r": "家用电器",
        "product_category": "家用电器",
        "operate": [],
        "product_id__id": "665ebf388ad1e00007a49bb4",
        "pricebook_id__id": "66597bc3ebd8080007bac1f0",
        "_idKey": "665ebfae8ad1e00007a4a1df66597bc3ebd8080007bac1f0",
        "__tbIndex": 0,
        "_fields": {
            "lock_rule": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "default_value": "default_lock_rule",
                "label": "锁定规则",
                "type": "lock_rule",
                "define_type": "package",
                "api_name": "lock_rule"
            },
            "discount": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "default_value": "100",
                "label": "价目表折扣",
                "type": "percentile",
                "define_type": "package",
                "api_name": "discount"
            },
            "is_saleable": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "true_or_false",
                "label": "是否可独立销售",
                "type": "quote",
                "quote_field": "product_id__r.is_saleable",
                "define_type": "package",
                "api_name": "is_saleable",
                "options": [
                    {
                        "value": true,
                        "label": "是"
                    },
                    {
                        "value": false,
                        "label": "否"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "mc_exchange_rate": {
                "describe_api_name": "PriceBookProductObj",
                "type": "number",
                "decimal_places": 6,
                "define_type": "package",
                "max_length": 16,
                "is_index": true,
                "is_active": true,
                "length": 10,
                "label": "汇率",
                "round_mode": 4,
                "api_name": "mc_exchange_rate"
            },
            "extend_obj_data_id": {
                "describe_api_name": "PriceBookProductObj",
                "type": "text",
                "define_type": "system",
                "max_length": 100,
                "is_index": false,
                "is_active": true,
                "default_value": "",
                "label": "extend_obj_data_id",
                "api_name": "extend_obj_data_id"
            },
            "life_status_before_invalid": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "作废前生命状态",
                "type": "text",
                "define_type": "package",
                "max_length": 256,
                "api_name": "life_status_before_invalid"
            },
            "product_id": {
                "describe_api_name": "PriceBookProductObj",
                "type": "object_reference",
                "wheres": [
                    {
                        "connector": "OR",
                        "filters": [
                            {
                                "value_type": 0,
                                "operator": "EQ",
                                "field_name": "product_status",
                                "field_values": [
                                    "1"
                                ]
                            }
                        ]
                    }
                ],
                "define_type": "package",
                "input_mode": "",
                "is_index": true,
                "is_active": true,
                "default_value": "",
                "label": "产品",
                "target_api_name": "ProductObj",
                "target_related_list_name": "target_related_list_pricebookproduct_product",
                "api_name": "product_id"
            },
            "owner_department": {
                "describe_api_name": "PriceBookProductObj",
                "type": "text",
                "define_type": "package",
                "max_length": 100,
                "is_index": false,
                "is_active": true,
                "default_value": "",
                "label": "负责人所在部门",
                "api_name": "owner_department"
            },
            "pricebook_sellingprice": {
                "describe_api_name": "PriceBookProductObj",
                "type": "currency",
                "decimal_places": 2,
                "define_type": "package",
                "max_length": 14,
                "is_index": true,
                "is_active": true,
                "length": 12,
                "default_value": "$product_id__r.price$",
                "label": "价目表售价",
                "round_mode": 4,
                "api_name": "pricebook_sellingprice"
            },
            "barcode": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "text",
                "label": "条形码",
                "type": "quote",
                "quote_field": "product_id__r.barcode",
                "define_type": "package",
                "api_name": "barcode",
                "options": null,
                "length": 0,
                "decimal_places": 0
            },
            "pricebook_id": {
                "describe_api_name": "PriceBookProductObj",
                "type": "master_detail",
                "define_type": "package",
                "is_index": true,
                "is_active": true,
                "label": "价目表",
                "target_api_name": "PriceBookObj",
                "target_related_list_name": "target_related_list_pricebookproduct",
                "api_name": "pricebook_id"
            },
            "lock_status": {
                "describe_api_name": "PriceBookProductObj",
                "type": "select_one",
                "options": [
                    {
                        "label": "未锁定",
                        "value": "0"
                    },
                    {
                        "label": "锁定",
                        "value": "1"
                    }
                ],
                "define_type": "package",
                "is_index": false,
                "is_active": true,
                "default_value": "0",
                "label": "锁定状态",
                "api_name": "lock_status"
            },
            "package": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "package",
                "type": "text",
                "define_type": "system",
                "max_length": 200,
                "api_name": "package"
            },
            "is_giveaway": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": false,
                "quote_field_type": "select_one",
                "label": "是否赠品",
                "type": "quote",
                "quote_field": "product_id__r.is_giveaway",
                "define_type": "package",
                "api_name": "is_giveaway",
                "options": [
                    {
                        "value": "0",
                        "label": "否"
                    },
                    {
                        "value": "1",
                        "label": "是"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "create_time": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "label": "创建时间",
                "type": "date_time",
                "define_type": "system",
                "date_format": "yyyy-MM-dd HH:mm:ss",
                "api_name": "create_time"
            },
            "product_life_status": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "select_one",
                "label": "产品对象生命状态",
                "type": "quote",
                "quote_field": "product_id__r.life_status",
                "define_type": "package",
                "api_name": "product_life_status",
                "options": [
                    {
                        "value": "ineffective",
                        "label": "未生效"
                    },
                    {
                        "value": "under_review",
                        "label": "审核中"
                    },
                    {
                        "value": "normal",
                        "label": "正常"
                    },
                    {
                        "value": "in_change",
                        "label": "变更中"
                    },
                    {
                        "value": "invalid",
                        "label": "作废"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "product_name": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "text",
                "label": "产品名称",
                "type": "quote",
                "quote_field": "product_id__r.name",
                "define_type": "package",
                "api_name": "product_name",
                "options": null,
                "length": 0,
                "decimal_places": 0
            },
            "version": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "length": 8,
                "label": "version",
                "type": "number",
                "decimal_places": 0,
                "define_type": "system",
                "round_mode": 4,
                "api_name": "version"
            },
            "relevant_team": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "label": "相关团队",
                "type": "embedded_object_list",
                "define_type": "package",
                "api_name": "relevant_team"
            },
            "unit": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "select_one",
                "label": "单位",
                "type": "quote",
                "quote_field": "product_id__r.unit",
                "define_type": "package",
                "api_name": "unit",
                "options": [
                    {
                        "value": "1",
                        "label": "个"
                    },
                    {
                        "value": "2",
                        "label": "块"
                    },
                    {
                        "value": "3",
                        "label": "只"
                    },
                    {
                        "value": "4",
                        "label": "把"
                    },
                    {
                        "value": "5",
                        "label": "枚"
                    },
                    {
                        "value": "6",
                        "label": "条"
                    },
                    {
                        "value": "7",
                        "label": "瓶"
                    },
                    {
                        "value": "8",
                        "label": "盒"
                    },
                    {
                        "value": "9",
                        "label": "套"
                    },
                    {
                        "value": "10",
                        "label": "箱"
                    },
                    {
                        "value": "11",
                        "label": "米"
                    },
                    {
                        "value": "12",
                        "label": "千克"
                    },
                    {
                        "value": "13",
                        "label": "吨"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "name": {
                "describe_api_name": "PriceBookProductObj",
                "type": "auto_number",
                "define_type": "system",
                "is_index": true,
                "is_active": true,
                "default_value": "",
                "label": "价目表明细编号",
                "api_name": "name"
            },
            "ceiling_price": {
                "describe_api_name": "PriceBookProductObj",
                "type": "currency",
                "decimal_places": 2,
                "define_type": "package",
                "max_length": 14,
                "is_index": true,
                "is_active": true,
                "length": 12,
                "default_value": "",
                "label": "浮动上限价格",
                "round_mode": 4,
                "api_name": "ceiling_price"
            },
            "start_count": {
                "describe_api_name": "PriceBookProductObj",
                "type": "number",
                "decimal_places": 0,
                "define_type": "package",
                "max_length": 14,
                "is_index": true,
                "is_active": true,
                "length": 14,
                "default_value": "",
                "label": "起始数量(不含)",
                "round_mode": 4,
                "api_name": "start_count"
            },
            "floor_price": {
                "describe_api_name": "PriceBookProductObj",
                "type": "currency",
                "decimal_places": 2,
                "define_type": "package",
                "max_length": 14,
                "is_index": true,
                "is_active": true,
                "length": 12,
                "default_value": "",
                "label": "浮动下限价格",
                "round_mode": 4,
                "api_name": "floor_price"
            },
            "stand_price": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "currency",
                "label": "标准价格",
                "type": "quote",
                "quote_field": "product_id__r.price",
                "define_type": "package",
                "api_name": "stand_price",
                "options": null,
                "length": 18,
                "decimal_places": 2
            },
            "end_date": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "label": "有效结束时间",
                "type": "date_time",
                "define_type": "package",
                "date_format": "yyyy-MM-dd HH:mm:ss",
                "api_name": "end_date"
            },
            "tenant_id": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "tenant_id",
                "type": "text",
                "define_type": "system",
                "max_length": 200,
                "api_name": "tenant_id"
            },
            "specification_value_set": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "text",
                "label": "规格属性",
                "type": "quote",
                "quote_field": "product_id__r.product_spec",
                "define_type": "package",
                "api_name": "specification_value_set",
                "options": null,
                "length": 0,
                "decimal_places": 0
            },
            "product_status": {
                "describe_api_name": "PriceBookProductObj",
                "quote_field_type": "select_one",
                "type": "quote",
                "quote_field": "product_id__r.product_status",
                "define_type": "package",
                "is_index": true,
                "is_active": true,
                "label": "上下架",
                "is_show_mask": false,
                "api_name": "product_status",
                "options": [
                    {
                        "value": "1",
                        "label": "已上架"
                    },
                    {
                        "value": "2",
                        "label": "已下架"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "product_code": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "text",
                "label": "产品编码",
                "type": "quote",
                "quote_field": "product_id__r.product_code",
                "define_type": "package",
                "api_name": "product_code",
                "options": null,
                "length": 0,
                "decimal_places": 0
            },
            "origin_source": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "数据来源",
                "type": "select_one",
                "options": [
                    {
                        "label": "数据同步",
                        "value": "0"
                    }
                ],
                "define_type": "system",
                "api_name": "origin_source"
            },
            "lock_user": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "加锁人",
                "type": "employee",
                "define_type": "package",
                "api_name": "lock_user"
            },
            "is_deleted": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "default_value": false,
                "label": "is_deleted",
                "type": "true_or_false",
                "options": [],
                "define_type": "system",
                "api_name": "is_deleted"
            },
            "object_describe_api_name": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "object_describe_api_name",
                "type": "text",
                "define_type": "system",
                "max_length": 200,
                "api_name": "object_describe_api_name"
            },
            "end_count": {
                "describe_api_name": "PriceBookProductObj",
                "type": "number",
                "decimal_places": 0,
                "define_type": "package",
                "max_length": 14,
                "is_index": true,
                "is_active": true,
                "length": 14,
                "default_value": "",
                "label": "结束数量(含)",
                "round_mode": 4,
                "api_name": "end_count"
            },
            "out_owner": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "label": "外部负责人",
                "type": "employee",
                "define_type": "system",
                "api_name": "out_owner"
            },
            "mc_functional_currency": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "本位币",
                "type": "select_one",
                "options": [
                    {
                        "label": "CNY - 人民币",
                        "value": "CNY"
                    }
                ],
                "define_type": "package",
                "api_name": "mc_functional_currency"
            },
            "start_date": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "label": "有效开始时间",
                "type": "date_time",
                "define_type": "package",
                "date_format": "yyyy-MM-dd HH:mm:ss",
                "api_name": "start_date"
            },
            "owner": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "label": "负责人",
                "type": "employee",
                "define_type": "package",
                "api_name": "owner"
            },
            "is_package": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "true_or_false",
                "label": "是否产品组合",
                "type": "quote",
                "quote_field": "product_id__r.is_package",
                "define_type": "package",
                "api_name": "is_package",
                "options": [
                    {
                        "value": true,
                        "label": "是"
                    },
                    {
                        "value": false,
                        "label": "否"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "last_modified_time": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "label": "最后修改时间",
                "type": "date_time",
                "define_type": "system",
                "date_format": "yyyy-MM-dd HH:mm:ss",
                "api_name": "last_modified_time"
            },
            "life_status": {
                "describe_api_name": "PriceBookProductObj",
                "type": "select_one",
                "options": [
                    {
                        "label": "未生效",
                        "value": "ineffective"
                    },
                    {
                        "label": "审核中",
                        "value": "under_review"
                    },
                    {
                        "label": "正常",
                        "value": "normal"
                    },
                    {
                        "label": "变更中",
                        "value": "in_change"
                    },
                    {
                        "label": "作废",
                        "value": "invalid"
                    }
                ],
                "define_type": "package",
                "is_index": true,
                "is_active": true,
                "default_value": "normal",
                "label": "生命状态",
                "api_name": "life_status"
            },
            "last_modified_by": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "label": "最后修改人",
                "type": "employee",
                "define_type": "system",
                "api_name": "last_modified_by"
            },
            "pricebook_price": {
                "describe_api_name": "PriceBookProductObj",
                "return_type": "number",
                "type": "formula",
                "decimal_places": 2,
                "define_type": "package",
                "max_length": 14,
                "is_index": false,
                "is_active": true,
                "length": 12,
                "label": "价目表价格",
                "round_mode": 4,
                "api_name": "pricebook_price"
            },
            "out_tenant_id": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "外部企业",
                "type": "text",
                "define_type": "system",
                "max_length": 200,
                "api_name": "out_tenant_id"
            },
            "mc_currency": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": true,
                "is_active": true,
                "default_value": "",
                "label": "币种",
                "type": "select_one",
                "options": [
                    {
                        "label": "USD - 美元",
                        "value": "USD"
                    },
                    {
                        "label": "CNY - 人民币",
                        "value": "CNY"
                    }
                ],
                "define_type": "package",
                "api_name": "mc_currency"
            },
            "record_type": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "业务类型",
                "type": "record_type",
                "options": [
                    {
                        "description": "预设业务类型",
                        "is_active": true,
                        "label": "预设业务类型",
                        "value": "default__c",
                        "api_name": "default__c"
                    }
                ],
                "define_type": "package",
                "api_name": "record_type"
            },
            "order_by": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "length": 8,
                "label": "order_by",
                "type": "number",
                "decimal_places": 0,
                "define_type": "system",
                "round_mode": 4,
                "api_name": "order_by"
            },
            "mc_exchange_rate_version": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "label": "汇率版本",
                "type": "text",
                "define_type": "package",
                "max_length": 256,
                "api_name": "mc_exchange_rate_version"
            },
            "category": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "select_one",
                "label": "产品分类",
                "type": "quote",
                "quote_field": "product_id__r.category",
                "define_type": "package",
                "api_name": "category",
                "options": [
                    {
                        "value": "1",
                        "label": "办公用品"
                    },
                    {
                        "value": "1710925428015",
                        "label": "办公用品/电脑"
                    },
                    {
                        "value": "2",
                        "label": "数码产品"
                    },
                    {
                        "value": "3",
                        "label": "服务"
                    },
                    {
                        "value": "1711090540800",
                        "label": "服务/配送"
                    },
                    {
                        "value": "4",
                        "label": "家用电器"
                    },
                    {
                        "value": "5",
                        "label": "家居用品"
                    },
                    {
                        "value": "1708504298598",
                        "label": "aaa"
                    },
                    {
                        "value": "1708504591792",
                        "label": "名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长名称很长"
                    },
                    {
                        "value": "1711088932621",
                        "label": "蔬菜"
                    }
                ],
                "length": 0,
                "decimal_places": 0
            },
            "product_category": {
                "describe_api_name": "PriceBookProductObj",
                "is_index": false,
                "is_active": true,
                "quote_field_type": "object_reference",
                "label": "分类",
                "type": "quote",
                "quote_field": "product_id__r.product_category_id",
                "define_type": "package",
                "api_name": "product_category",
                "options": null,
                "length": 0,
                "decimal_places": 0
            }
        },
        "_is_open_display_name": false,
        "pricebook_id__relation_ids__tpd": "66597bc3ebd8080007bac1f0",
        "discount__tpd": "100%",
        "product_status__v__tpd": "1",
        "is_saleable__tpd": "是",
        "product_status__r__tpd": "已上架",
        "mc_exchange_rate__tpd": "1.000000",
        "currency_pricebook_sellingprice__tpd": {
            "prefix": "CNY",
            "suffix": "",
            "fieldApiName": "currency_pricebook_sellingprice",
            "objectApiName": "PriceBookProductObj"
        },
        "product_id__ro__tpd": {
            "version": "2",
            "product_status": "已上架",
            "field_s27tu__c": "扫地机器人",
            "extend_obj_data_id": "665ebf388ad1e00007a49bd9",
            "lock_status__v": "0",
            "object_describe_api_name": "ProductObj",
            "is_saleable__v": true,
            "record_type": "预设业务类型",
            "product_status__v": "1",
            "field_AhbB1__c": "0.00",
            "tenant_id": "90242",
            "on_shelves_time": "2024-06-04 15:16",
            "product_category_id__relation_ids": "6594c7c0cfd011000162fde1",
            "category__v": "4",
            "is_giveaway": "否",
            "unit": "个",
            "_id": "665ebf388ad1e00007a49bb4",
            "life_status__v": "normal",
            "is_giveaway__v": "0",
            "is_saleable": "是",
            "owner_department_id": "1000",
            "unit__v": "1",
            "product_category_id": "6594c7c0cfd011000162fde1",
            "is_deleted__v": false,
            "owner_department": "研发部门",
            "create_time": "2024-06-04 15:16",
            "name": "扫地机器人",
            "price": "300.00",
            "searchAfterId": [
                "1717485368787",
                "665ebf388ad1e00007a49bb4"
            ],
            "product_code": "扫地机器人",
            "is_package": "否",
            "last_modified_time": "2024-06-04 15:16",
            "life_status": "正常",
            "lock_status": "未锁定",
            "package": "CRM",
            "display_name": "扫地机器人",
            "is_deleted": "否",
            "picture_path": [],
            "is_package__v": false,
            "category": "家用电器"
        },
        "pricebook_sellingprice__r__tpd": "CNY 300.00",
        "product_id__tpd": "665ebf388ad1e00007a49bb4",
        "owner_department_id__tpd": "1000",
        "total_num__tpd": 2,
        "owner_department__tpd": "",
        "searchAfterId__tpd": [
            "PBProdCode20240604001700",
            "665ebfae8ad1e00007a4a1df"
        ],
        "pricebook_sellingprice__tpd": "CNY 300.00",
        "barcode__tpd": "",
        "pricebook_id__tpd": "66597bc3ebd8080007bac1f0",
        "lock_status__tpd": "未锁定",
        "package__tpd": "CRM",
        "create_time__tpd": "2024-06-04 15:18",
        "product_life_status__tpd": "正常",
        "product_category__v__tpd": "6594c7c0cfd011000162fde1",
        "version__tpd": "1",
        "created_by__tpd": "CRM管理员",
        "product_name__tpd": "扫地机器人",
        "unit__tpd": "个",
        "data_own_department__tpd": "<span data-departmentid=\"1000\">研发部门</span>",
        "name__tpd": "PBProdCode20240604001700",
        "_id__tpd": "665ebfae8ad1e00007a4a1df",
        "start_count__tpd": "0",
        "stand_price__tpd": "300.00",
        "tenant_id__tpd": "90242",
        "specification_value_set__tpd": "",
        "product_life_status__r__tpd": "正常",
        "product_status__tpd": "已上架",
        "product_code__tpd": "扫地机器人",
        "product_id__r__tpd": "扫地机器人",
        "product_id__relation_ids__tpd": "665ebf388ad1e00007a49bb4",
        "is_deleted__tpd": false,
        "object_describe_api_name__tpd": "PriceBookProductObj",
        "end_count__tpd": "10",
        "product_life_status__v__tpd": "normal",
        "mc_functional_currency__tpd": "CNY",
        "is_saleable__v__tpd": true,
        "owner__tpd": "CRM管理员",
        "last_modified_time__tpd": "2024-06-04 15:18",
        "is_package__tpd": "否",
        "unit__r__tpd": "个",
        "life_status__tpd": "正常",
        "last_modified_by__tpd": "CRM管理员",
        "pricebook_price__tpd": "300.00",
        "pricebook_id__r__tpd": "阶梯价目表",
        "mc_currency__tpd": "CNY - 人民币",
        "is_package__v__tpd": false,
        "record_type__tpd": "预设业务类型",
        "category__v__tpd": "4",
        "unit__v__tpd": "1",
        "mc_exchange_rate_version__tpd": "*************",
        "category__tpd": "家用电器",
        "category__r__tpd": "家用电器",
        "operate__tpd": [],
        "product_id__id__tpd": "665ebf388ad1e00007a49bb4",
        "pricebook_id__id__tpd": "66597bc3ebd8080007bac1f0",
        "_idKey__tpd": "665ebfae8ad1e00007a4a1df66597bc3ebd8080007bac1f0",
        "__tbIndex__tpd": 0,
        "floor_price__tpd": "",
        "ceiling_price__tpd": "",
        "start_date__tpd": "",
        "end_date__tpd": "",
        "out_owner__tpd": "",
        "rowId": "1717486379425450",
        "newestPrice": "300.00",
        "id": "665ebfae8ad1e00007a4a1df",
        "value": "PBProdCode20240604001700"
    }
];
// 模拟beginGetRealPrice返回值
const realPriceRes = [
    {
        "from1": 'realPriceRes',
        "lock_rule": null,
        "price_book_end_date": null,
        "pricebook_id__relation_ids": "66597bc3ebd8080007bac1f0",
        "discount": "100",
        "product_status__v": "1",
        "is_saleable": "是",
        "product_status__r": "已上架",
        "mc_exchange_rate": "1.000000",
        "extend_obj_data_id": null,
        "life_status_before_invalid": null,
        "product_id": "665ebf388ad1e00007a49bb4",
        "owner_department_id": "1000",
        "owner_department": "研发部门",
        "searchAfterId": [
            "665ebfae8ad1e00007a4a1df",
            "665ebfae8ad1e00007a4a1df"
        ],
        "pricebook_sellingprice": "300.00",
        "barcode": "",
        "pricebook_id": "66597bc3ebd8080007bac1f0",
        "lock_status": "0",
        "package": "CRM",
        "create_time": 1717485486494,
        "is_giveaway": "0",
        "param_price": null,
        "price_book_priority": 99999999,
        "product_life_status": "正常",
        "product_category__v": "6594c7c0cfd011000162fde1",
        "version": "1",
        "created_by": [
            "1000"
        ],
        "product_name": "扫地机器人",
        "relevant_team": null,
        "price_book_start_date": null,
        "price_book_last_mod_time": 1717140420258,
        "unit": "个",
        "data_own_department": [
            "1000"
        ],
        "name": "PBProdCode20240604001700",
        "_id": "665ebfae8ad1e00007a4a1df",
        "start_count": "0",
        "ceiling_price": null,
        "parent_prod_pkg_key": "",
        "floor_price": null,
        "stand_price": "300.00",
        "tenant_id": "90242",
        "end_date": null,
        "selling_price": "300.00",
        "root_prod_pkg_key": "",
        "specification_value_set": "",
        "product_life_status__r": "正常",
        "param_amount": 1,
        "product_status": "已上架",
        "product_code": "扫地机器人",
        "origin_source": null,
        "lock_user": null,
        "product_id__r": "扫地机器人",
        "product_id__relation_ids": "665ebf388ad1e00007a49bb4",
        "is_deleted": false,
        "object_describe_api_name": "PriceBookProductObj",
        "end_count": "10",
        "product_life_status__v": "normal",
        "out_owner": null,
        "mc_functional_currency": "CNY",
        "is_saleable__v": true,
        "start_date": null,
        "prod_pkg_key": "****************",
        "owner": [
            "1000"
        ],
        "last_modified_time": 1717140420258,
        "is_package": "否",
        "param_priceBookId": "66597bc3ebd8080007bac1f0",
        "unit__r": "个",
        "life_status": "normal",
        "selling_data_define_type": "sys",
        "available_range_id": [
            "6657207f9a9e7b0007d3030a",
            "66050c413ce86400073f9297",
            "662b5510dbc4f2000738cf8b",
            "66029bf8678adb00078d2699"
        ],
        "last_modified_by": [
            "1000"
        ],
        "pricebook_price": "300.00",
        "out_tenant_id": null,
        "pricebook_id__r": "阶梯价目表",
        "mc_currency": "CNY",
        "is_package__v": false,
        "record_type": "default__c",
        "rowId": "****************",
        "category__v": "4",
        "param_unit": "",
        "order_by": null,
        "unit__v": "1",
        "mc_exchange_rate_version": "*************",
        "category": "家用电器",
        "category__r": "家用电器",
        "product_category": "家用电器"
    }
]
const mdData = [
    {
        "from1": 'mdData',
        "record_type": "default__c",
        "object_describe_api_name": "QuoteLinesObj",
        "rowId": "****************",
        "object_describe_id": "6595044ac7150800070da5dd",
        "lock_rule": "default_lock_rule",
        "life_status": "normal",
        "lock_status": "0",
        "quantity": "1",
        "print_hierarchy": ".",
        "price_book_product_id": "665ebfae8ad1e00007a4a1df",
        "price_book_product_id__r": "PBProdCode20240604001700",
        "product_id": "665ebf388ad1e00007a49bb4",
        "product_id__r": "扫地机器人",
        "price_book_id": "66597bc3ebd8080007bac1f0",
        "price_book_id__r": "阶梯价目表",
        "discount": "100",
        "price_book_price": "300.00"
    }
];
const detailData = [
    {
        from1: 'detailData',
        "lock_rule": null,
        "discount": "100.0000",
        "product_status__v": "1",
        "is_saleable": "是",
        "price_editable": null,
        "field_yno1W__c": "2.000",
        "product_status__r": "已上架",
        "mc_exchange_rate": "1.000000",
        "field_kqXJy__c": "2200.00",
        "field_1B48A__c": "2000.00",
        "price": "200",
        "life_status_before_invalid": null,
        "product_id": "665ec11c8ad1e00007a4c14a",
        "owner_department_id": null,
        "node_no": null,
        "searchAfterId": [
            "10",
            "665ec3188ad1e00007a4ce2d"
        ],
        "quote_lines_unit": "",
        "price_mode": null,
        "bom_type": null,
        "standard_bom_id": null,
        "base_sales_amount": "200.00",
        "sales_amount": "200.00",
        "product_life_status": "正常",
        "version": "2",
        "quote_id__r": "2024000000000204",
        "price_book_id": "6594c79bc715080007e4b05b",
        "bom_core_id": null,
        "related_core_id": null,
        "tenant_id": "90242",
        "data_own_organization": [],
        "quote_lines_specs": "",
        "product_life_status__r": "正常",
        "product_status": "已上架",
        "increment": null,
        "origin_source": null,
        "node_price": null,
        "product_id__r": "智能摄像头",
        "nonstandard_attribute_json__c": null,
        "product_id__relation_ids": "665ec11c8ad1e00007a4c14a",
        "nonstandard_attribute_json": null,
        "extra_discount": "100.0000",
        "bom_version": null,
        "base_selling_price": "200.00",
        "product_life_status__v": "normal",
        "attribute_json__c": null,
        "price_book_product_id__relation_ids": "665ec11c8ad1e00007a4c14a90242",
        "attribute": null,
        "mc_functional_currency": "CNY",
        "field_zJjtC__c": "440000.00",
        "prod_pkg_key": null,
        "base_total_amount": "200.00",
        "last_modified_time": null,
        "is_package": "否",
        "field_zMxUg__c": "200.00",
        "life_status": "normal",
        "out_tenant_id": null,
        "amount_editable": null,
        "bom_id": null,
        "product_group_id": null,
        "sales_price": "200.00",
        "order_by": "10",
        "max_amount": null,
        "base_sales_price": "200.00",
        "price_book_product_id__r": "PBProdCode20240604001703",
        "price_book_product_id": "665ec11c8ad1e00007a4c14a90242",
        "new_bom_path": null,
        "node_discount": null,
        "standard_bom_line_id": null,
        "amount_any": null,
        "node_type": null,
        "price_book_id__relation_ids": "6594c79bc715080007e4b05b",
        "owner_department": null,
        "price_book_id__r": "标准价目表",
        "lock_status": "0",
        "package": "CRM",
        "create_time": null,
        "min_amount": null,
        "quote_id__relation_ids": "665ec3188ad1e00007a4ce2c",
        "field_1UE2o__c": "1.00",
        "display_name": "智能摄像头",
        "created_by": [
            "1000"
        ],
        "relevant_team": [],
        "field_IeCwj__c": "190.00",
        "field_Wiks6__c": null,
        "data_own_department": [
            "1000"
        ],
        "total_amount": "200.00",
        "name": null,
        "attribute_json": null,
        "parent_prod_pkg_key": null,
        "selling_price": "200.00",
        "root_prod_pkg_key": null,
        "total_discount": "100.0000",
        "nonstandard_attribute": null,
        "lock_user": null,
        "quote_lines_unit__r": "",
        "is_deleted": false,
        "quote_lines_unit__v": null,
        "object_describe_api_name": "QuoteLinesObj",
        "base_price": "200.00",
        "owner__l": [
            {
                "id": "1000",
                "tenantId": "90242",
                "name": "CRM管理员",
                "picAddr": "",
                "email": "",
                "nickname": "CRM管理员",
                "phone": "",
                "description": "",
                "status": 0,
                "createTime": *************,
                "modifyTime": *************,
                "dept": "1000",
                "post": "",
                "empNum": ""
            }
        ],
        "field_y3aug__c": null,
        "out_owner": null,
        "owner__r": {
            "picAddr": "",
            "mobile": "***********",
            "description": "",
            "dept": "1000",
            "supervisorId": null,
            "title": null,
            "empNum": "",
            "modifyTime": *************,
            "post": "",
            "createTime": *************,
            "phone": "",
            "name": "CRM管理员",
            "nickname": "CRM管理员",
            "tenantId": "90242",
            "id": "1000",
            "position": null,
            "enterpriseName": null,
            "email": "",
            "status": 0
        },
        "is_saleable__v": true,
        "owner": [
            "1000"
        ],
        "quantity": "1",
        "last_modified_by__l": [
            {
                "id": "1000",
                "tenantId": "90242",
                "name": "CRM管理员",
                "picAddr": "",
                "email": "",
                "nickname": "CRM管理员",
                "phone": "",
                "description": "",
                "status": 0,
                "createTime": *************,
                "modifyTime": *************,
                "dept": "1000",
                "post": "",
                "empNum": ""
            }
        ],
        "print_hierarchy": ".",
        "last_modified_by": [
            "1000"
        ],
        "temp_node_bom_id": null,
        "mc_currency": "CNY",
        "is_package__v": false,
        "record_type": "default__c",
        "last_modified_by__r": {
            "picAddr": "",
            "mobile": "***********",
            "description": "",
            "dept": "1000",
            "supervisorId": null,
            "title": null,
            "empNum": "",
            "modifyTime": *************,
            "post": "",
            "createTime": *************,
            "phone": "",
            "name": "CRM管理员",
            "nickname": "CRM管理员",
            "tenantId": "90242",
            "id": "1000",
            "position": null,
            "enterpriseName": null,
            "email": "",
            "status": 0
        },
        "temp_node_group_id": null,
        "mc_exchange_rate_version": "*************",
        "attribute_price_book_id": null,
        "rowId": "****************"
    }
]
const describeData = {
    from1: 'describeData',
    "tenant_id": "90242",
    "store_table_name": "quote_lines",
    "description": "",
    "index_version": 200,
    "is_deleted": false,
    "define_type": "package",
    "display_name_r": "报价单明细",
    "release_version": "6.4",
    "package": "CRM",
    "is_active": true,
    "last_modified_time": *************,
    "create_time": *************,
    "last_modified_by": "1000",
    "display_name": "报价单明细",
    "created_by": "-1000",
    "version": 66,
    "is_open_display_name": true,
    "icon_index": 12,
    "api_name": "QuoteLinesObj",
    "icon_path": "",
    "is_udef": true,
    "short_name": "ql",
    "_id": "6595044ac7150800070da5dd",
    "fields": {
        account_id: {
            "describe_api_name": "QuoteObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": true,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "客户名称",
            "index_name": "s_4",
            "is_index": true,
            "is_active": true,
            "create_time": *************,
            "is_encrypted": false,
            "default_value": "",
            "label": "客户名称",
            "target_api_name": "AccountObj",
            "target_related_list_name": "account_quote_list",
            "target_related_list_label": "报价单",
            "action_on_target_delete": "set_null",
            "related_wheres": [],
            "api_name": "account_id",
            "_id": "65950449c7150800070da4a8",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "cascade_detail_api_name": {
                "QuoteObj": [
                    "new_opportunity_id"
                ]
            },
            "is_readonly": false,
            "full_line": false,
            "render_type": "object_reference",
            "is_tile_help_text": false,
            "options": [],
            "cus_children": [
                "new_opportunity_id",
                "price_book_id"
            ],
            "cascade_all_fields": [
                "new_opportunity_id",
                "price_book_id",
                "account_id"
            ]
        },
        "lock_rule": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "锁定规则",
            "is_unique": false,
            "rules": [],
            "default_value": "default_lock_rule",
            "label": "锁定规则",
            "type": "lock_rule",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "lock_rule",
            "define_type": "package",
            "_id": "6595044ac7150800070da586",
            "is_single": false,
            "label_r": "锁定规则",
            "is_index_field": false,
            "index_name": "s_2",
            "status": "released"
        },
        "discount": {
            "expression_type": "percentile",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "type": "percentile",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "系统折扣",
            "index_name": "d_9",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792524,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$price_book_product_id__r.discount$",
            "label": "折扣",
            "api_name": "discount",
            "_id": "6595044ac7150800070da58a",
            "is_index_field": false,
            "help_text": "记录系统能够给与客户的折扣，不能修改。未开启价目表时，默认100%",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "base_sales_price",
                        "base_total_amount",
                        "total_discount",
                        "total_amount",
                        "base_sales_amount",
                        "sales_price",
                        "base_selling_price",
                        "sales_amount",
                        "field_IeCwj__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        },
                        {
                            "fieldName": "sales_price",
                            "order": 8,
                            "type": "D"
                        }
                    ]
                }
            },
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "is_saleable": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "true_or_false",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "是否可独立售卖",
            "type": "quote",
            "quote_field": "product_id__r.is_saleable",
            "is_required": false,
            "api_name": "is_saleable",
            "define_type": "package",
            "_id": "65951336ea4ea100077a22f2",
            "is_single": false,
            "label_r": "是否可独立售卖",
            "is_index_field": false,
            "index_name": "b_0",
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "options": [
                {
                    "value": true,
                    "label": "是"
                },
                {
                    "value": false,
                    "label": "否"
                }
            ],
            "is_tile_help_text": false
        },
        "price_editable": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "true_or_false",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "价格可编辑",
            "type": "quote",
            "quote_field": "bom_id__r.price_editable",
            "is_required": false,
            "api_name": "price_editable",
            "define_type": "package",
            "_id": "65951336ea4ea100077a2303",
            "is_single": false,
            "label_r": "价格可编辑",
            "is_index_field": false,
            "index_name": "b_1",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "options": [
                {
                    "value": true,
                    "label": "是"
                },
                {
                    "value": false,
                    "label": "否"
                }
            ],
            "is_tile_help_text": false
        },
        "field_yno1W__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 3,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_22",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704883239804,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 11,
            "default_value": "2",
            "label": "人天",
            "field_num": 10,
            "api_name": "field_yno1W__c",
            "_id": "659e742716fc580001bad66a",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "mc_exchange_rate": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 6,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "汇率",
            "index_name": "d_25",
            "max_length": 16,
            "is_index": true,
            "is_active": true,
            "create_time": 1710332612162,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 10,
            "default_value": "",
            "label": "汇率",
            "api_name": "mc_exchange_rate",
            "_id": "65f19ac45b87b10001b41dc9",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "base_quote_product_sum"
                    ],
                    "QuoteLinesObj": [
                        "base_sales_price",
                        "base_price",
                        "base_selling_price",
                        "base_total_amount",
                        "base_sales_amount"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_price",
                            "order": 8,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "field_kqXJy__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_19",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704715506825,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$field_1B48A__c$+$field_y3aug__c$+$field_zMxUg__c$",
            "label": "金额4",
            "currency_unit": "￥",
            "field_num": 7,
            "api_name": "field_kqXJy__c",
            "_id": "659be4f2c30cd900013ff9c7",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteLinesObj": [
                        "field_zJjtC__c"
                    ]
                },
                "relate_fields": {
                    "QuoteLinesObj": [
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "field_1B48A__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_16",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704353561876,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$price$*10",
            "label": "金额1",
            "currency_unit": "￥",
            "field_num": 4,
            "api_name": "field_1B48A__c",
            "_id": "65965f1ae807f80001d034fc",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "field_IC8c9__c"
                    ],
                    "QuoteLinesObj": [
                        "field_zJjtC__c",
                        "field_kqXJy__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_IC8c9__c",
                            "order": 8,
                            "type": "C"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "life_status_before_invalid": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "作废前生命状态",
            "is_unique": false,
            "label": "作废前生命状态",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "life_status_before_invalid",
            "define_type": "package",
            "_id": "6595044ac7150800070da592",
            "is_single": false,
            "label_r": "作废前生命状态",
            "is_index_field": false,
            "index_name": "t_2",
            "max_length": 256,
            "status": "released"
        },
        "price": {
            "expression_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "extend_info": {
                "show_positive_sign": true
            },
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "价格",
            "index_name": "d_7",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "length": 14,
            "default_value": "$price_book_product_id__r.pricebook_sellingprice$*EXCHANGERATE($price_book_product_id__r.mc_currency$,$mc_currency$,1.0)",
            "label": "价格",
            "currency_unit": "￥",
            "api_name": "price",
            "_id": "6595044ac7150800070da591",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "记录系统能够给与客户的原始价格。",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "field_IC8c9__c",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "base_sales_price",
                        "base_total_amount",
                        "total_discount",
                        "base_sales_amount",
                        "sales_amount",
                        "field_IeCwj__c",
                        "field_kqXJy__c",
                        "field_1B48A__c",
                        "total_amount",
                        "base_price",
                        "sales_price",
                        "base_selling_price",
                        "field_zJjtC__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_IC8c9__c",
                            "order": 8,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_price",
                            "order": 8,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_price",
                            "order": 8,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_1B48A__c",
                            "order": 7,
                            "type": "D"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "product_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": true,
            "wheres": [
                {
                    "connector": "OR",
                    "filters": [
                        {
                            "operator": "EQ",
                            "field_name": "is_saleable",
                            "field_values": [
                                true
                            ]
                        }
                    ]
                }
            ],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品名称",
            "index_name": "s_6",
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品名称",
            "target_api_name": "ProductObj",
            "target_related_list_name": "product_quote_lines_list",
            "target_related_list_label": "报价单明细",
            "action_on_target_delete": "set_null",
            "related_wheres": [],
            "api_name": "product_id",
            "_id": "6595044ac7150800070da593",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "field_2v87e__c",
                        "field_w3VKg__c"
                    ],
                    "QuoteLinesObj": [
                        "field_kqXJy__c",
                        "is_package",
                        "field_zMxUg__c",
                        "quote_lines_specs",
                        "product_status",
                        "is_saleable",
                        "product_life_status",
                        "quote_lines_unit",
                        "field_zJjtC__c",
                        "display_name"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_w3VKg__c",
                            "order": 2,
                            "type": "C"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "quote_lines_specs",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "product_status",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "display_name",
                            "order": 1,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_lines_unit",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_zMxUg__c",
                            "order": 1,
                            "type": "D"
                        },
                        {
                            "fieldName": "is_saleable",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "is_package",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "product_life_status",
                            "order": 1000,
                            "type": "Q"
                        }
                    ]
                }
            },
            "help_text_type": "hover",
            "is_open_display_name": true,
            "is_tile_help_text": false
        },
        "node_no": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "子件序号",
            "index_name": "d_24",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1706169547020,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "",
            "label": "序号",
            "api_name": "node_no",
            "_id": "65b214ca1dd106000172dcce",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "quote_lines_unit": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "select_one",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "单位",
            "type": "quote",
            "quote_field": "product_id__r.unit",
            "is_required": false,
            "api_name": "quote_lines_unit",
            "define_type": "package",
            "_id": "6595044ac7150800070da598",
            "is_single": false,
            "label_r": "单位",
            "is_index_field": false,
            "index_name": "s_5",
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "options": [
                {
                    "value": "1",
                    "label": "个"
                },
                {
                    "value": "2",
                    "label": "块"
                },
                {
                    "value": "3",
                    "label": "只"
                },
                {
                    "value": "4",
                    "label": "把"
                },
                {
                    "value": "5",
                    "label": "枚"
                },
                {
                    "value": "6",
                    "label": "条"
                },
                {
                    "value": "7",
                    "label": "瓶"
                },
                {
                    "value": "8",
                    "label": "盒"
                },
                {
                    "value": "9",
                    "label": "套"
                },
                {
                    "value": "10",
                    "label": "箱"
                },
                {
                    "value": "11",
                    "label": "米"
                },
                {
                    "value": "12",
                    "label": "千克"
                },
                {
                    "value": "13",
                    "label": "吨"
                }
            ],
            "is_tile_help_text": false,
            "length": 0,
            "decimal_places": 0
        },
        "price_mode": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "select_one",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "定价模式",
            "type": "quote",
            "quote_field": "bom_id__r.price_mode",
            "is_required": false,
            "api_name": "price_mode",
            "define_type": "package",
            "_id": "65951336ea4ea100077a2305",
            "is_single": false,
            "label_r": "定价模式",
            "is_index_field": false,
            "index_name": "s_9",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "options": [
                {
                    "value": "1",
                    "label": "配置价格"
                },
                {
                    "value": "2",
                    "label": "价目表价格"
                }
            ],
            "is_tile_help_text": false
        },
        "bom_type": {
            "describe_api_name": "QuoteLinesObj",
            "auto_adapt_places": false,
            "quote_field_type": "select_one",
            "remove_mask_roles": {},
            "description": "bom类型",
            "is_unique": false,
            "type": "quote",
            "quote_field": "bom_core_id__r.category",
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "bom类型",
            "index_name": "s_10",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "label": "bom类型",
            "is_need_convert": false,
            "api_name": "bom_type",
            "_id": "65951336ea4ea100077a22fc",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "options": [
                {
                    "value": "configure",
                    "label": "配置BOM"
                },
                {
                    "value": "standard",
                    "label": "标准BOM"
                }
            ],
            "is_tile_help_text": false
        },
        "standard_bom_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "标准BOM",
            "index_name": "t_17",
            "max_length": 200,
            "is_index": true,
            "is_active": true,
            "create_time": 1711452628382,
            "is_encrypted": false,
            "default_value": "",
            "label": "标准BOMID",
            "is_need_convert": false,
            "api_name": "standard_bom_id",
            "_id": "6602b1d2491f7e000761d663",
            "is_index_field": true,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "quote_id": {
            "describe_api_name": "QuoteLinesObj",
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "type": "master_detail",
            "is_required": true,
            "define_type": "package",
            "is_single": false,
            "label_r": "报价单",
            "index_name": "s_1",
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792524,
            "is_encrypted": false,
            "label": "报价单",
            "target_api_name": "QuoteObj",
            "show_detail_button": false,
            "target_related_list_name": "quote_lines_list",
            "target_related_list_label": "报价单明细",
            "api_name": "quote_id",
            "is_create_when_master_create": true,
            "_id": "6595044ac7150800070da5a0",
            "is_index_field": true,
            "is_required_when_master_create": false,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "field_IC8c9__c",
                        "field_w3VKg__c",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "base_sales_price",
                        "base_total_amount",
                        "total_discount",
                        "base_sales_amount",
                        "sales_amount",
                        "mc_currency",
                        "mc_exchange_rate",
                        "field_IeCwj__c",
                        "field_kqXJy__c",
                        "field_1B48A__c",
                        "total_amount",
                        "price",
                        "base_price",
                        "sales_price",
                        "base_selling_price",
                        "mc_exchange_rate_version",
                        "field_zJjtC__c",
                        "mc_functional_currency"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_w3VKg__c",
                            "order": 2,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_IC8c9__c",
                            "order": 8,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "mc_currency",
                            "order": 2,
                            "type": "D"
                        },
                        {
                            "fieldName": "mc_functional_currency",
                            "order": 3,
                            "type": "D"
                        },
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        },
                        {
                            "fieldName": "mc_exchange_rate_version",
                            "order": 3,
                            "type": "D"
                        },
                        {
                            "fieldName": "mc_exchange_rate",
                            "order": 3,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_price",
                            "order": 8,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "price",
                            "order": 6,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_price",
                            "order": 8,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_1B48A__c",
                            "order": 7,
                            "type": "D"
                        }
                    ]
                }
            },
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "base_sales_amount": {
            "expression_type": "js",
            "return_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "销售金额小计(本位币)",
            "index_name": "d_27",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "expression": "$sales_amount$*$mc_exchange_rate$",
            "create_time": 1710332634722,
            "is_encrypted": false,
            "length": 12,
            "label": "销售金额小计(本位币)",
            "api_name": "base_sales_amount",
            "_id": "65f19adac662dc00010840c6",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "base_quote_product_sum"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        }
                    ]
                }
            },
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "sales_amount": {
            "expression_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "销售金额小计",
            "index_name": "d_2",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1567482601993,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$selling_price$*$quantity$",
            "label": "销售金额小计",
            "currency_unit": "￥",
            "api_name": "sales_amount",
            "_id": "6595044ac7150800070da5a1",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "base_sales_amount",
                        "field_IeCwj__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "product_life_status": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "select_one",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "产品生命状态",
            "type": "quote",
            "quote_field": "product_id__r.life_status",
            "is_required": false,
            "api_name": "product_life_status",
            "define_type": "package",
            "_id": "65951336ea4ea100077a22f4",
            "is_single": false,
            "label_r": "产品生命状态",
            "is_index_field": false,
            "index_name": "s_11",
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "options": [
                {
                    "value": "ineffective",
                    "label": "未生效"
                },
                {
                    "value": "under_review",
                    "label": "审核中"
                },
                {
                    "value": "normal",
                    "label": "正常"
                },
                {
                    "value": "in_change",
                    "label": "变更中"
                },
                {
                    "value": "invalid",
                    "label": "作废"
                }
            ],
            "is_tile_help_text": false
        },
        "version": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "create_time": *************,
            "length": 8,
            "is_unique": false,
            "description": "version",
            "label": "version",
            "type": "number",
            "decimal_places": 0,
            "is_need_convert": false,
            "is_required": false,
            "api_name": "version",
            "define_type": "system",
            "index_name": "version",
            "round_mode": 4,
            "status": "released"
        },
        "price_book_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "价目表",
            "index_name": "s_7",
            "is_index": true,
            "is_active": true,
            "create_time": 1704264778271,
            "is_encrypted": false,
            "default_value": "",
            "label": "价目表",
            "target_api_name": "PriceBookObj",
            "target_related_list_name": "price_book_quote_lines_list",
            "target_related_list_label": "报价单明细",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "related_wheres": [],
            "api_name": "price_book_id",
            "_id": "6595044ac7150800070da5b0",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "bom_core_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品组合",
            "index_name": "s_12",
            "max_length": 256,
            "is_index": true,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品组合",
            "target_api_name": "BomCoreObj",
            "target_related_list_name": "bome_core_list",
            "target_related_list_label": "报价单明细",
            "action_on_target_delete": "set_null",
            "related_wheres": [],
            "api_name": "bom_core_id",
            "_id": "65951336ea4ea100077a22fb",
            "is_index_field": true,
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteLinesObj": [
                        "bom_type",
                        "bom_version"
                    ]
                },
                "relate_fields": {
                    "QuoteLinesObj": [
                        {
                            "fieldName": "bom_version",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "bom_type",
                            "order": 1000,
                            "type": "Q"
                        }
                    ]
                }
            },
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "related_core_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "复用产品组合Id",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "复用产品组合Id",
            "index_name": "t_7",
            "max_length": 100,
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "复用产品组合Id",
            "is_need_convert": false,
            "api_name": "related_core_id",
            "_id": "65951336ea4ea100077a22fe",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "tenant_id": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "pattern": "",
            "is_unique": false,
            "description": "tenant_id",
            "label": "tenant_id",
            "type": "text",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "tenant_id",
            "define_type": "system",
            "index_name": "ei",
            "max_length": 200,
            "status": "released"
        },
        "quote_lines_specs": {
            "describe_api_name": "QuoteLinesObj",
            "auto_adapt_places": false,
            "quote_field_type": "text",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "quote",
            "quote_field": "product_id__r.product_spec",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "规格",
            "index_name": "t_3",
            "is_index": false,
            "is_active": true,
            "create_time": 1536053792512,
            "is_encrypted": false,
            "label": "规格",
            "api_name": "quote_lines_specs",
            "_id": "6595044ac7150800070da588",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false,
            "options": null,
            "length": 0,
            "decimal_places": 0
        },
        "product_status": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "select_one",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "产品上下架",
            "type": "quote",
            "quote_field": "product_id__r.product_status",
            "is_required": false,
            "api_name": "product_status",
            "define_type": "package",
            "_id": "65951336ea4ea100077a22f3",
            "is_single": false,
            "label_r": "产品上下架",
            "is_index_field": false,
            "index_name": "s_13",
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "options": [
                {
                    "value": "1",
                    "label": "已上架"
                },
                {
                    "value": "2",
                    "label": "已下架"
                }
            ],
            "is_tile_help_text": false
        },
        "increment": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "number",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "增加数量幅度",
            "type": "quote",
            "quote_field": "bom_id__r.increment",
            "is_required": false,
            "api_name": "increment",
            "define_type": "package",
            "_id": "65951336ea4ea100077a2302",
            "is_single": false,
            "label_r": "增加数量幅度",
            "is_index_field": false,
            "index_name": "d_13",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "decimal_places": 0,
            "length": 14,
            "is_tile_help_text": false
        },
        "origin_source": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "is_unique": false,
            "label": "数据来源",
            "type": "select_one",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "origin_source",
            "options": [
                {
                    "label": "数据同步",
                    "value": "0"
                }
            ],
            "define_type": "system",
            "is_extend": false,
            "index_name": "s_os",
            "config": {
                "display": 0
            },
            "status": "released"
        },
        "node_price": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "标准选配价格",
            "index_name": "d_31",
            "max_length": 20,
            "is_index": true,
            "is_active": true,
            "create_time": 1715913565250,
            "is_encrypted": false,
            "length": 18,
            "default_value": "",
            "label": "标准选配价格",
            "currency_unit": "￥",
            "is_need_convert": false,
            "api_name": "node_price",
            "_id": "6646c35d036e6a0006692223",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "nonstandard_attribute_json__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "type": "long_text",
            "default_to_zero": false,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "t_13",
            "max_length": 256,
            "is_index": true,
            "is_active": true,
            "create_time": 1704351616535,
            "is_encrypted": false,
            "min_length": 0,
            "default_value": "",
            "label": "非标属性json",
            "field_num": 3,
            "api_name": "nonstandard_attribute_json__c",
            "_id": "65965780e807f80001ccc5c2",
            "is_index_field": false,
            "help_text": "",
            "status": "new",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "nonstandard_attribute_json": {
            "expression_type": "json",
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704283177510,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "label": "非标属性值",
            "type": "long_text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "nonstandard_attribute_json",
            "define_type": "package",
            "_id": "66044b13a090690001b384ec",
            "is_single": false,
            "is_extend": false,
            "label_r": "非标属性值",
            "is_index_field": false,
            "index_name": "t_12",
            "max_length": 256,
            "status": "released"
        },
        "extra_discount": {
            "expression_type": "percentile",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "type": "percentile",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "额外折扣",
            "index_name": "d_10",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1567482639977,
            "is_encrypted": false,
            "length": 12,
            "default_value": "1",
            "label": "额外折扣",
            "api_name": "extra_discount",
            "_id": "6595044ac7150800070da58f",
            "is_index_field": false,
            "help_text": "记录除了给予的系统折扣外，销售人员给予客户的额外折扣，销售可以修改。如未修改默认公式时，修改该字段时，会自动计算出基于报价的销售单价（销售单价=报价*额外折扣）",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "total_discount",
                        "base_sales_amount",
                        "base_selling_price",
                        "sales_amount",
                        "field_IeCwj__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        }
                    ]
                }
            },
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "bom_version": {
            "describe_api_name": "QuoteLinesObj",
            "auto_adapt_places": false,
            "quote_field_type": "auto_number",
            "remove_mask_roles": {},
            "description": "bom版本",
            "is_unique": false,
            "type": "quote",
            "quote_field": "bom_core_id__r.core_version",
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "bom版本",
            "index_name": "s_14",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "label": "bom版本",
            "is_need_convert": false,
            "api_name": "bom_version",
            "_id": "65951336ea4ea100077a22fd",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "base_selling_price": {
            "expression_type": "js",
            "return_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "销售单价(本位币)",
            "index_name": "d_29",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "expression": "$selling_price$*$mc_exchange_rate$",
            "create_time": 1710332634723,
            "is_encrypted": false,
            "length": 12,
            "label": "销售单价(本位币)",
            "api_name": "base_selling_price",
            "_id": "65f19adac662dc00010840c5",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "attribute_json__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "type": "long_text",
            "default_to_zero": false,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "t_14",
            "max_length": 256,
            "is_index": true,
            "is_active": true,
            "create_time": 1704351616534,
            "is_encrypted": false,
            "min_length": 0,
            "default_value": "",
            "label": "属性json",
            "field_num": 2,
            "api_name": "attribute_json__c",
            "_id": "65965780e807f80001ccc5c1",
            "is_index_field": false,
            "help_text": "",
            "status": "new",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "attribute": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "属性",
            "index_name": "t_0",
            "max_length": 256,
            "is_index": true,
            "is_active": true,
            "create_time": 1704265172491,
            "is_encrypted": false,
            "default_value": "",
            "label": "属性",
            "is_need_convert": false,
            "api_name": "attribute",
            "_id": "659505d4d9f649000770c236",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "field_zJjtC__c": {
            "expression_type": "js",
            "return_type": "number",
            "describe_api_name": "QuoteLinesObj",
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_23",
            "is_index": true,
            "is_active": true,
            "expression": "$field_kqXJy__c$*$price$*$quantity$",
            "create_time": 1705923931479,
            "is_encrypted": false,
            "label": "计算C = 金额4 *数量*价格",
            "field_num": 12,
            "api_name": "field_zJjtC__c",
            "_id": "65ae555e704a860001f1880b",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "mc_functional_currency": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1710332612169,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "label": "本位币",
            "type": "select_one",
            "is_required": false,
            "api_name": "mc_functional_currency",
            "options": [
                {
                    "label": "CNY - 人民币",
                    "value": "CNY"
                }
            ],
            "define_type": "package",
            "_id": "65f19ac45b87b10001b41dca",
            "is_single": false,
            "label_r": "本位币",
            "is_index_field": false,
            "index_name": "s_19",
            "config": {},
            "help_text": "",
            "status": "new"
        },
        "prod_pkg_key": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品组合虚拟key",
            "index_name": "t_9",
            "max_length": 32,
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品组合虚拟key",
            "api_name": "prod_pkg_key",
            "_id": "65951336ea4ea100077a22f6",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "base_total_amount": {
            "expression_type": "js",
            "return_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "小计(本位币)",
            "index_name": "d_30",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "expression": "$total_amount$*$mc_exchange_rate$",
            "create_time": 1710332634729,
            "is_encrypted": false,
            "length": 12,
            "label": "报价小计(本位币)",
            "currency_unit": "￥",
            "api_name": "base_total_amount",
            "_id": "65f19adac662dc00010840c4",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "is_package": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "true_or_false",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "是否产品组合",
            "type": "quote",
            "quote_field": "product_id__r.is_package",
            "is_required": false,
            "api_name": "is_package",
            "define_type": "package",
            "_id": "65951336ea4ea100077a22f1",
            "is_single": false,
            "label_r": "是否产品组合",
            "is_index_field": false,
            "index_name": "b_2",
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "options": [
                {
                    "value": true,
                    "label": "是"
                },
                {
                    "value": false,
                    "label": "否"
                }
            ],
            "is_tile_help_text": false,
            "length": 0,
            "decimal_places": 0
        },
        "last_modified_time": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": true,
            "create_time": *************,
            "is_unique": false,
            "description": "last_modified_time",
            "label": "最后修改时间",
            "type": "date_time",
            "time_zone": "",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "last_modified_time",
            "define_type": "system",
            "date_format": "yyyy-MM-dd HH:mm:ss",
            "index_name": "md_time",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "field_zMxUg__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_18",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704355613643,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$product_id__r.price$",
            "label": "金额3",
            "currency_unit": "￥",
            "field_num": 6,
            "api_name": "field_zMxUg__c",
            "_id": "6596671de807f80001d3f99e",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "field_2v87e__c",
                        "field_w3VKg__c"
                    ],
                    "QuoteLinesObj": [
                        "field_zJjtC__c",
                        "field_kqXJy__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_w3VKg__c",
                            "order": 2,
                            "type": "C"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "life_status": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "生命状态",
            "is_unique": false,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "value": "ineffective",
                    "label": "未生效"
                },
                {
                    "value": "under_review",
                    "label": "审核中"
                },
                {
                    "value": "normal",
                    "label": "正常"
                },
                {
                    "value": "in_change",
                    "label": "变更中"
                },
                {
                    "value": "invalid",
                    "label": "作废"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "生命状态",
            "index_name": "s_4",
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792524,
            "is_encrypted": false,
            "default_value": "normal",
            "label": "生命状态",
            "is_need_convert": false,
            "api_name": "life_status",
            "_id": "6595044ac7150800070da59f",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released",
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "out_tenant_id": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "pattern": "",
            "is_unique": false,
            "description": "out_tenant_id",
            "label": "外部企业",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "out_tenant_id",
            "define_type": "system",
            "index_name": "o_ei",
            "config": {
                "display": 0
            },
            "max_length": 200,
            "status": "released"
        },
        "amount_editable": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "true_or_false",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "数量可编辑",
            "type": "quote",
            "quote_field": "bom_id__r.amount_editable",
            "is_required": false,
            "api_name": "amount_editable",
            "define_type": "package",
            "_id": "65951336ea4ea100077a2304",
            "is_single": false,
            "label_r": "数量可编辑",
            "is_index_field": false,
            "index_name": "b_3",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "options": [
                {
                    "value": true,
                    "label": "是"
                },
                {
                    "value": false,
                    "label": "否"
                }
            ],
            "is_tile_help_text": false
        },
        "bom_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品选配明细",
            "index_name": "s_15",
            "is_index": true,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品选配明细",
            "target_api_name": "BOMObj",
            "target_related_list_name": "target_related_list_bom",
            "target_related_list_label": "报价单的明细",
            "action_on_target_delete": "set_null",
            "related_wheres": [],
            "api_name": "bom_id",
            "_id": "65951336ea4ea100077a22f5",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteLinesObj": [
                        "product_group_id",
                        "amount_any",
                        "min_amount",
                        "max_amount",
                        "increment",
                        "price_editable",
                        "amount_editable",
                        "price_mode"
                    ]
                },
                "relate_fields": {
                    "QuoteLinesObj": [
                        {
                            "fieldName": "amount_editable",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "price_mode",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "min_amount",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "max_amount",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "price_editable",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "amount_any",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "increment",
                            "order": 1000,
                            "type": "Q"
                        },
                        {
                            "fieldName": "product_group_id",
                            "order": 1000,
                            "type": "Q"
                        }
                    ]
                }
            },
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "product_group_id": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "object_reference",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "产品分组",
            "type": "quote",
            "quote_field": "bom_id__r.product_group_id",
            "is_required": false,
            "api_name": "product_group_id",
            "define_type": "package",
            "_id": "65951336ea4ea100077a22f9",
            "is_single": false,
            "label_r": "产品分组",
            "is_index_field": false,
            "index_name": "s_16",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "sales_price": {
            "expression_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": true,
            "define_type": "package",
            "is_single": false,
            "label_r": "报价",
            "index_name": "d_8",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$price$*$discount$",
            "label": "报价",
            "currency_unit": "￥",
            "api_name": "sales_price",
            "_id": "6595044ac7150800070da5ab",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "记录系统给与客户的折扣后价格，建议不能修改。",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "base_sales_price",
                        "base_total_amount",
                        "total_discount",
                        "total_amount",
                        "base_sales_amount",
                        "base_selling_price",
                        "sales_amount",
                        "field_IeCwj__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "max_amount": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "number",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "最大数量",
            "type": "quote",
            "quote_field": "bom_id__r.max_amount",
            "is_required": false,
            "api_name": "max_amount",
            "define_type": "package",
            "_id": "65951336ea4ea100077a2301",
            "is_single": false,
            "label_r": "最大数量",
            "is_index_field": false,
            "index_name": "d_14",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "decimal_places": 0,
            "length": 14,
            "is_tile_help_text": false
        },
        "order_by": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "create_time": *************,
            "length": 8,
            "is_unique": false,
            "description": "order_by",
            "label": "order_by",
            "type": "number",
            "decimal_places": 0,
            "is_need_convert": false,
            "is_required": false,
            "api_name": "order_by",
            "define_type": "system",
            "index_name": "l_by",
            "round_mode": 4,
            "status": "released"
        },
        "base_sales_price": {
            "expression_type": "js",
            "return_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": true,
            "define_type": "package",
            "is_single": false,
            "label_r": "报价(本位币)",
            "index_name": "d_26",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "expression": "$sales_price$*$mc_exchange_rate$",
            "create_time": 1710332634690,
            "is_encrypted": false,
            "length": 12,
            "label": "报价(本位币)",
            "api_name": "base_sales_price",
            "_id": "65f19adac662dc00010840c3",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "price_book_product_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [
                {
                    "connector": "OR",
                    "filters": [
                        {
                            "operator": "EQ",
                            "field_name": "is_saleable",
                            "field_values": [
                                true
                            ]
                        },
                        {
                            "value_type": 0,
                            "operator": "EQ",
                            "field_name": "product_status",
                            "field_values": [
                                "1"
                            ]
                        }
                    ]
                }
            ],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "价目表产品",
            "index_name": "s_0",
            "is_index": true,
            "is_active": true,
            "create_time": 1704264778271,
            "is_encrypted": false,
            "default_value": "",
            "label": "价目表产品",
            "target_api_name": "PriceBookProductObj",
            "target_related_list_name": "price_book_product_quote_lines_list",
            "target_related_list_label": "报价单明细",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "related_wheres": [],
            "api_name": "price_book_product_id",
            "_id": "6595044ac7150800070da5af",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "field_IC8c9__c",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "base_sales_price",
                        "base_total_amount",
                        "total_discount",
                        "base_sales_amount",
                        "discount",
                        "sales_amount",
                        "field_IeCwj__c",
                        "field_kqXJy__c",
                        "field_1B48A__c",
                        "total_amount",
                        "price",
                        "base_price",
                        "sales_price",
                        "base_selling_price",
                        "field_zJjtC__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_IC8c9__c",
                            "order": 8,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "discount",
                            "order": 1,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_price",
                            "order": 8,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "price",
                            "order": 6,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_price",
                            "order": 8,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_1B48A__c",
                            "order": 7,
                            "type": "D"
                        }
                    ]
                }
            },
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "new_bom_path": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "Bom完整路径",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "Bom完整路径",
            "index_name": "t_5",
            "max_length": 500,
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "Bom完整路径",
            "is_need_convert": false,
            "api_name": "new_bom_path",
            "_id": "65951336ea4ea100077a22ff",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "node_discount": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "type": "percentile",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "选配折扣",
            "index_name": "d_32",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "create_time": 1715913565250,
            "is_encrypted": false,
            "length": 12,
            "default_value": "",
            "label": "选配折扣",
            "is_need_convert": false,
            "api_name": "node_discount",
            "_id": "6646c35d036e6a0006692224",
            "is_index_field": false,
            "help_text": "",
            "status": "released",
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "standard_bom_line_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "标准BOM明细ID",
            "index_name": "t_18",
            "max_length": 200,
            "is_index": true,
            "is_active": true,
            "create_time": 1711452628378,
            "is_encrypted": false,
            "default_value": "",
            "label": "标准BOM明细ID",
            "is_need_convert": false,
            "api_name": "standard_bom_line_id",
            "_id": "6602b1d4491f7e000761da10",
            "is_index_field": true,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "extend_obj_data_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "extend_obj_data_id",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "system",
            "is_single": false,
            "is_extend": false,
            "label_r": "extend_obj_data_id",
            "index_name": "t_1",
            "max_length": 100,
            "is_index": false,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "default_value": "",
            "label": "extend_obj_data_id",
            "api_name": "extend_obj_data_id",
            "_id": "6595044ac7150800070da58e",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "amount_any": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1709867474777,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "true_or_false",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "数量是否任意值",
            "type": "quote",
            "quote_field": "bom_id__r.amount_any",
            "is_required": false,
            "api_name": "amount_any",
            "define_type": "package",
            "_id": "65ea81d3c10fde0001a1775a",
            "is_single": false,
            "is_index_field": false,
            "index_name": "b_4",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "options": [
                {
                    "value": true,
                    "label": "是"
                },
                {
                    "value": false,
                    "label": "否"
                }
            ],
            "is_tile_help_text": false
        },
        "node_type": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "value": "standard",
                    "label": "标准子件"
                },
                {
                    "value": "temp",
                    "label": "临时子件"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "子件类型",
            "index_name": "s_17",
            "is_index": true,
            "is_active": true,
            "create_time": 1706169547020,
            "is_encrypted": false,
            "default_value": "",
            "label": "子件类型",
            "is_need_convert": false,
            "api_name": "node_type",
            "_id": "65b214ca1dd106000172dccd",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released",
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "owner_department": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": true,
            "label_r": "负责人主属部门",
            "index_name": "owner_dept",
            "max_length": 100,
            "is_index": false,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "default_value": "",
            "label": "负责人所在部门",
            "is_need_convert": false,
            "api_name": "owner_department",
            "_id": "6595044ac7150800070da595",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "lock_status": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "锁定状态",
            "is_unique": false,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "value": "0",
                    "label": "未锁定"
                },
                {
                    "value": "1",
                    "label": "锁定"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "锁定状态",
            "index_name": "s_3",
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792511,
            "is_encrypted": false,
            "default_value": "0",
            "label": "锁定状态",
            "is_need_convert": false,
            "api_name": "lock_status",
            "_id": "6595044ac7150800070da59a",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released",
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "package": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "pattern": "",
            "is_unique": false,
            "description": "package",
            "label": "package",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "package",
            "define_type": "system",
            "index_name": "pkg",
            "max_length": 200,
            "status": "released"
        },
        "create_time": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": true,
            "create_time": *************,
            "is_unique": false,
            "description": "create_time",
            "label": "创建时间",
            "type": "date_time",
            "time_zone": "",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "create_time",
            "define_type": "system",
            "date_format": "yyyy-MM-dd HH:mm:ss",
            "index_name": "crt_time",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "min_amount": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "quote_field_type": "number",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "label": "最小数量",
            "type": "quote",
            "quote_field": "bom_id__r.min_amount",
            "is_required": false,
            "api_name": "min_amount",
            "define_type": "package",
            "_id": "65951336ea4ea100077a2300",
            "is_single": false,
            "label_r": "最小数量",
            "is_index_field": false,
            "index_name": "d_0",
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "help_text_type": "hover",
            "decimal_places": 0,
            "length": 14,
            "is_tile_help_text": false
        },
        "field_1UE2o__c": {
            "expression_type": "js",
            "return_type": "number",
            "describe_api_name": "QuoteLinesObj",
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_15",
            "is_index": true,
            "is_active": true,
            "expression": "1",
            "create_time": 1705644939435,
            "is_encrypted": false,
            "label": "计算字段B=数量*金额1",
            "field_num": 11,
            "api_name": "field_1UE2o__c",
            "_id": "65aa138e028d1e0001ca3e39",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "display_name": {
            "expression_type": "js",
            "return_type": "text",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "index_name": "t_19",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "expression": "$product_id__r.name$",
            "create_time": 1713504473320,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品名称展示",
            "api_name": "display_name",
            "_id": "662200dd44e600000156c303",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "new",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "created_by": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": true,
            "is_active": true,
            "create_time": *************,
            "is_unique": false,
            "label": "创建人",
            "type": "employee",
            "is_need_convert": true,
            "is_required": false,
            "api_name": "created_by",
            "define_type": "system",
            "is_single": true,
            "index_name": "crt_by",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "relevant_team": {
            "describe_api_name": "QuoteLinesObj",
            "embedded_fields": {
                "teamMemberEmployee": {
                    "is_index": true,
                    "is_need_convert": true,
                    "is_required": false,
                    "api_name": "teamMemberEmployee",
                    "is_unique": false,
                    "define_type": "package",
                    "description": "成员员工",
                    "label": "成员员工",
                    "type": "employee",
                    "is_single": true,
                    "help_text": "成员员工"
                },
                "teamMemberRole": {
                    "is_index": true,
                    "is_need_convert": false,
                    "is_required": false,
                    "api_name": "teamMemberRole",
                    "options": [
                        {
                            "label": "负责人",
                            "value": "1"
                        },
                        {
                            "label": "普通成员",
                            "value": "4"
                        }
                    ],
                    "is_unique": false,
                    "define_type": "package",
                    "description": "成员角色",
                    "label": "成员角色",
                    "type": "select_one",
                    "help_text": "成员角色"
                },
                "teamMemberPermissionType": {
                    "is_index": true,
                    "is_need_convert": false,
                    "is_required": false,
                    "api_name": "teamMemberPermissionType",
                    "options": [
                        {
                            "label": "只读",
                            "value": "1"
                        },
                        {
                            "label": "读写",
                            "value": "2"
                        }
                    ],
                    "is_unique": false,
                    "define_type": "package",
                    "description": "成员权限类型",
                    "label": "成员权限类型",
                    "type": "select_one",
                    "help_text": "成员权限类型"
                }
            },
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "label": "相关团队",
            "type": "embedded_object_list",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "relevant_team",
            "define_type": "package",
            "_id": "6595044ac7150800070da5a7",
            "is_single": false,
            "label_r": "相关团队",
            "is_index_field": false,
            "index_name": "a_team",
            "help_text": "相关团队",
            "status": "released"
        },
        "field_IeCwj__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_21",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704715528456,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$quote_id__r.quote_product_sum$-10",
            "label": "金额6",
            "currency_unit": "￥",
            "field_num": 9,
            "api_name": "field_IeCwj__c",
            "_id": "659be508c30cd900013ffb73",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "field_Wiks6__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_20",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704715519897,
            "is_encrypted": false,
            "length": 12,
            "default_value": "",
            "label": "金额5",
            "currency_unit": "￥",
            "field_num": 8,
            "api_name": "field_Wiks6__c",
            "_id": "659be500c30cd900013ffada",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "data_own_department": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "department",
            "is_required": false,
            "wheres": [],
            "optional_type": "department",
            "define_type": "package",
            "is_single": true,
            "label_r": "归属部门",
            "index_name": "data_owner_dept_id",
            "is_index": true,
            "is_active": true,
            "create_time": 1527155151798,
            "is_encrypted": false,
            "default_value": "",
            "label": "归属部门",
            "is_need_convert": false,
            "api_name": "data_own_department",
            "_id": "6595044ac7150800070da5a8",
            "is_index_field": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "total_amount": {
            "expression_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "报价小计",
            "index_name": "d_12",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$sales_price$*$quantity$",
            "label": "报价小计",
            "currency_unit": "￥",
            "api_name": "total_amount",
            "_id": "6595044ac7150800070da5a9",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteLinesObj": [
                        "base_total_amount"
                    ]
                },
                "relate_fields": {
                    "QuoteLinesObj": [
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "attribute_json": {
            "expression_type": "json",
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1704265173788,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "label": "属性值",
            "type": "long_text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "attribute_json",
            "define_type": "package",
            "_id": "66044b13a090690001b384ed",
            "is_single": false,
            "is_extend": false,
            "label_r": "属性值",
            "is_index_field": false,
            "index_name": "t_4",
            "max_length": 256,
            "status": "released"
        },
        "name": {
            "describe_api_name": "QuoteLinesObj",
            "prefix": "{yyyy}",
            "auto_adapt_places": false,
            "description": "",
            "is_unique": true,
            "start_number": 1,
            "type": "auto_number",
            "is_required": true,
            "define_type": "system",
            "postfix": "{dd}",
            "is_single": false,
            "label_r": "报价单明细编码",
            "index_name": "name",
            "is_index": true,
            "is_active": true,
            "auto_number_type": "normal",
            "create_time": 1527155151798,
            "is_encrypted": false,
            "serial_number": 6,
            "default_value": "{yyyy}000001{dd}",
            "label": "报价单明细编码",
            "condition": "NONE",
            "api_name": "name",
            "func_api_name": "",
            "_id": "6595044ac7150800070da5ac",
            "is_index_field": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "_id": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "pattern": "",
            "is_unique": false,
            "description": "_id",
            "label": "_id",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "_id",
            "define_type": "system",
            "index_name": "_id",
            "max_length": 200,
            "status": "released"
        },
        "parent_prod_pkg_key": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品组合虚拟parent key",
            "index_name": "t_6",
            "max_length": 32,
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品组合虚拟parent key",
            "api_name": "parent_prod_pkg_key",
            "_id": "65951336ea4ea100077a22f7",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "field_IC8c9__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_IC8c9__c",
                            "order": 8,
                            "type": "C"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "selling_price": {
            "expression_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "销售单价",
            "index_name": "d_5",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1567482653305,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$sales_price$*$extra_discount$",
            "label": "销售单价",
            "currency_unit": "￥",
            "api_name": "selling_price",
            "_id": "6595044ac7150800070da584",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "记录允许销售人员填写给与客户的最终产品的单价。如未修改默认公式，修改该字段时，会自动计算出基于报价的额外折扣（额外折扣=(报价-销售单价)/报价）",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "base_selling_price",
                        "sales_amount",
                        "total_discount",
                        "base_sales_amount",
                        "field_IeCwj__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "root_prod_pkg_key": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品组合虚拟root key",
            "index_name": "t_8",
            "max_length": 32,
            "is_index": false,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品组合虚拟root key",
            "api_name": "root_prod_pkg_key",
            "_id": "65951336ea4ea100077a22f8",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "total_discount": {
            "expression_type": "percentile",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "type": "percentile",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "总折扣",
            "index_name": "d_6",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1571369437544,
            "is_encrypted": false,
            "length": 12,
            "default_value": "$selling_price$/$price$",
            "label": "总折扣",
            "api_name": "total_discount",
            "_id": "6595044ac7150800070da587",
            "is_index_field": false,
            "help_text": "记录系统和销售给予客户单品的总折扣，销售可以修改。如未修改默认公式时，修改该字段时，会自动计算出基于报价的销售单价（销售单价=价格*总折扣）和额外折扣（额外折扣=额外折扣=(报价-销售单价)/报价）",
            "status": "released",
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "nonstandard_attribute": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "index_name": "t_11",
            "max_length": 256,
            "is_index": true,
            "is_active": true,
            "create_time": 1704283176838,
            "is_encrypted": false,
            "default_value": "",
            "label": "非标属性",
            "is_need_convert": false,
            "api_name": "nonstandard_attribute",
            "_id": "65954c2912e1da0006444427",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "lock_user": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1536053792524,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "加锁人",
            "is_unique": false,
            "label": "加锁人",
            "type": "employee",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "lock_user",
            "define_type": "package",
            "_id": "6595044ac7150800070da58d",
            "is_single": true,
            "label_r": "加锁人",
            "is_index_field": false,
            "index_name": "a_1",
            "status": "released"
        },
        "is_deleted": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "create_time": *************,
            "is_unique": false,
            "description": "is_deleted",
            "default_value": false,
            "label": "is_deleted",
            "type": "true_or_false",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "is_deleted",
            "options": [],
            "define_type": "system",
            "index_name": "is_del",
            "status": "released"
        },
        "object_describe_api_name": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": *************,
            "pattern": "",
            "is_unique": false,
            "description": "object_describe_api_name",
            "label": "object_describe_api_name",
            "type": "text",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "object_describe_api_name",
            "define_type": "system",
            "index_name": "api_name",
            "max_length": 200,
            "status": "released"
        },
        "base_price": {
            "expression_type": "js",
            "return_type": "currency",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "extend_info": {
                "show_positive_sign": true
            },
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "价格(本位币)",
            "index_name": "d_28",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "expression": "$price$*$mc_exchange_rate$",
            "create_time": 1710332634723,
            "is_encrypted": false,
            "length": 12,
            "label": "价格(本位币)",
            "api_name": "base_price",
            "_id": "65f19adac662dc00010840c2",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "field_y3aug__c": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 4,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_17",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1704355606601,
            "is_encrypted": false,
            "length": 10,
            "default_value": "",
            "label": "金额2",
            "currency_unit": "￥",
            "field_num": 5,
            "api_name": "field_y3aug__c",
            "_id": "65966716e807f80001d3f884",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteLinesObj": [
                        "field_zJjtC__c",
                        "field_kqXJy__c"
                    ]
                },
                "relate_fields": {
                    "QuoteLinesObj": [
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "out_owner": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": true,
            "is_active": true,
            "create_time": *************,
            "is_unique": false,
            "label": "外部负责人",
            "type": "employee",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "out_owner",
            "define_type": "system",
            "is_single": true,
            "index_name": "o_owner",
            "config": {
                "display": 1
            },
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "owner": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "employee",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "is_single": true,
            "label_r": "负责人",
            "index_name": "owner",
            "is_index": true,
            "is_active": true,
            "create_time": *************,
            "is_encrypted": false,
            "default_value": "",
            "label": "负责人",
            "is_need_convert": false,
            "api_name": "owner",
            "_id": "6595044ac7150800070da599",
            "is_index_field": false,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "quantity": {
            "expression_type": "number",
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": true,
            "define_type": "package",
            "is_single": false,
            "label_r": "数量",
            "index_name": "d_11",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": *************,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "1",
            "label": "数量",
            "api_name": "quantity",
            "_id": "6595044ac7150800070da59b",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "base_total_amount",
                        "total_amount",
                        "base_sales_amount",
                        "sales_amount",
                        "field_zJjtC__c",
                        "field_IeCwj__c"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        }
                    ]
                }
            },
            "empty_prompt": "",
            "show_tag": false,
            "font_color": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "print_hierarchy": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "层级",
            "index_name": "t_10",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "create_time": 1704268598726,
            "is_encrypted": false,
            "default_value": ".",
            "label": "层级",
            "api_name": "print_hierarchy",
            "_id": "65951336ea4ea100077a22fa",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "此字段仅在 [ 报价单 ] 、 [ 报价单明细 ] ，打印模板中显示，但不可在布局中配置显示，默认值只支持 . _ - ，填写其它或者公式，默认按照 . 处理",
            "status": "new",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "last_modified_by": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": true,
            "is_active": true,
            "create_time": *************,
            "is_unique": false,
            "label": "最后修改人",
            "type": "employee",
            "is_need_convert": true,
            "is_required": false,
            "api_name": "last_modified_by",
            "define_type": "system",
            "is_single": true,
            "index_name": "md_by",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "temp_node_bom_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "临时子件ID",
            "index_name": "t_15",
            "max_length": 32,
            "is_index": true,
            "is_active": true,
            "create_time": 1706169547020,
            "is_encrypted": false,
            "default_value": "",
            "label": "临时子件ID",
            "api_name": "temp_node_bom_id",
            "_id": "65b214ca1dd106000172dcd0",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released",
            "empty_prompt": "",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "mc_currency": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "value": "USD",
                    "label": "USD - 美元"
                },
                {
                    "value": "CNY",
                    "label": "CNY - 人民币"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "币种",
            "index_name": "s_20",
            "is_index": true,
            "is_active": true,
            "create_time": 1710332612162,
            "is_encrypted": false,
            "default_value": "",
            "label": "币种",
            "api_name": "mc_currency",
            "_id": "65f19ac45b87b10001b41dc8",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "new",
            "calculate_relation": {
                "calculate_fields": {
                    "QuoteObj": [
                        "quote_amount",
                        "field_2v87e__c",
                        "quote_product_sum",
                        "field_IC8c9__c",
                        "base_quote_product_sum",
                        "base_quote_amount",
                        "field_Du5ak__c"
                    ],
                    "QuoteLinesObj": [
                        "selling_price",
                        "base_sales_price",
                        "base_total_amount",
                        "total_discount",
                        "base_sales_amount",
                        "sales_amount",
                        "mc_exchange_rate",
                        "field_IeCwj__c",
                        "field_kqXJy__c",
                        "field_1B48A__c",
                        "total_amount",
                        "price",
                        "base_price",
                        "sales_price",
                        "base_selling_price",
                        "mc_exchange_rate_version",
                        "field_zJjtC__c",
                        "mc_functional_currency"
                    ]
                },
                "relate_fields": {
                    "QuoteObj": [
                        {
                            "fieldName": "field_Du5ak__c",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_product_sum",
                            "order": 12,
                            "type": "C"
                        },
                        {
                            "fieldName": "base_quote_product_sum",
                            "order": 14,
                            "type": "C"
                        },
                        {
                            "fieldName": "field_2v87e__c",
                            "order": 15,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_quote_amount",
                            "order": 16,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_IC8c9__c",
                            "order": 8,
                            "type": "C"
                        },
                        {
                            "fieldName": "quote_amount",
                            "order": 14,
                            "type": "F"
                        }
                    ],
                    "QuoteLinesObj": [
                        {
                            "fieldName": "mc_functional_currency",
                            "order": 3,
                            "type": "D"
                        },
                        {
                            "fieldName": "sales_amount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_discount",
                            "order": 11,
                            "type": "D"
                        },
                        {
                            "fieldName": "total_amount",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_IeCwj__c",
                            "order": 13,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_sales_amount",
                            "order": 13,
                            "type": "F"
                        },
                        {
                            "fieldName": "base_sales_price",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "selling_price",
                            "order": 10,
                            "type": "D"
                        },
                        {
                            "fieldName": "mc_exchange_rate_version",
                            "order": 3,
                            "type": "D"
                        },
                        {
                            "fieldName": "mc_exchange_rate",
                            "order": 3,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_selling_price",
                            "order": 12,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_kqXJy__c",
                            "order": 9,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_price",
                            "order": 8,
                            "type": "F"
                        },
                        {
                            "fieldName": "field_zJjtC__c",
                            "order": 10,
                            "type": "F"
                        },
                        {
                            "fieldName": "price",
                            "order": 6,
                            "type": "D"
                        },
                        {
                            "fieldName": "base_total_amount",
                            "order": 11,
                            "type": "F"
                        },
                        {
                            "fieldName": "sales_price",
                            "order": 8,
                            "type": "D"
                        },
                        {
                            "fieldName": "field_1B48A__c",
                            "order": 7,
                            "type": "D"
                        }
                    ]
                }
            },
            "show_tag": false,
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "record_type": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1536053792532,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "record_type",
            "is_unique": false,
            "label": "业务类型",
            "type": "record_type",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "record_type",
            "options": [
                {
                    "description": "预设业务类型",
                    "is_active": true,
                    "label": "预设业务类型",
                    "value": "default__c",
                    "api_name": "default__c"
                },
                {
                    "is_active": true,
                    "label": "自定义业务类型",
                    "value": "record_7J7NG__c",
                    "api_name": "record_7J7NG__c"
                }
            ],
            "define_type": "package",
            "_id": "6595044ac7150800070da5a6",
            "is_single": false,
            "label_r": "业务类型",
            "is_index_field": false,
            "index_name": "r_type",
            "config": {},
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "temp_node_group_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "临时子件分组",
            "index_name": "s_18",
            "is_index": true,
            "is_active": true,
            "create_time": 1706169547021,
            "is_encrypted": false,
            "default_value": "",
            "label": "临时子件分组",
            "target_api_name": "ProductGroupObj",
            "target_related_list_name": "target_related_list_product_group_id",
            "target_related_list_label": "产品选配明细",
            "action_on_target_delete": "cascade_delete",
            "related_wheres": [],
            "api_name": "temp_node_group_id",
            "_id": "65b214ca1dd106000172dccf",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        },
        "mc_exchange_rate_version": {
            "describe_api_name": "QuoteLinesObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1710332612169,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "label": "汇率版本",
            "type": "text",
            "is_required": false,
            "api_name": "mc_exchange_rate_version",
            "define_type": "package",
            "_id": "65f19ac45b87b10001b41dcb",
            "is_single": false,
            "label_r": "汇率版本",
            "is_index_field": false,
            "index_name": "t_16",
            "max_length": 256,
            "help_text": "",
            "status": "new"
        },
        "attribute_price_book_id": {
            "describe_api_name": "QuoteLinesObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "产品属性价目表",
            "index_name": "s_8",
            "is_index": true,
            "is_active": true,
            "create_time": 1704265173296,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品属性价目表",
            "target_api_name": "AttributePriceBookObj",
            "target_related_list_name": "attribute_price_book_quote_lines_list",
            "target_related_list_label": "报价单明细",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "related_wheres": [],
            "api_name": "attribute_price_book_id",
            "_id": "659505d5d9f649000770c2ad",
            "is_index_field": true,
            "help_text": "",
            "status": "released",
            "help_text_type": "hover",
            "is_tile_help_text": false
        }
    },
    "actions": {},
    "calculate_relation": {
        "relate_fields": {
            "QuoteLinesObj": [
                {
                    "fieldName": "field_1UE2o__c",
                    "order": 1,
                    "type": "F"
                },
                {
                    "fieldName": "field_yno1W__c",
                    "order": 1,
                    "type": "D"
                },
                {
                    "fieldName": "mc_functional_currency",
                    "order": 3,
                    "type": "D"
                },
                {
                    "fieldName": "sales_amount",
                    "order": 11,
                    "type": "D"
                },
                {
                    "fieldName": "extra_discount",
                    "order": 1,
                    "type": "D"
                },
                {
                    "fieldName": "field_IeCwj__c",
                    "order": 13,
                    "type": "D"
                },
                {
                    "fieldName": "total_discount",
                    "order": 11,
                    "type": "D"
                },
                {
                    "fieldName": "base_sales_amount",
                    "order": 13,
                    "type": "F"
                },
                {
                    "fieldName": "base_sales_price",
                    "order": 10,
                    "type": "F"
                },
                {
                    "fieldName": "selling_price",
                    "order": 10,
                    "type": "D"
                },
                {
                    "fieldName": "mc_exchange_rate_version",
                    "order": 3,
                    "type": "D"
                },
                {
                    "fieldName": "mc_exchange_rate",
                    "order": 3,
                    "type": "D"
                },
                {
                    "fieldName": "base_selling_price",
                    "order": 12,
                    "type": "F"
                },
                {
                    "fieldName": "base_price",
                    "order": 8,
                    "type": "F"
                },
                {
                    "fieldName": "base_total_amount",
                    "order": 11,
                    "type": "F"
                }
            ],
            "QuoteObj": [
                {
                    "fieldName": "field_Du5ak__c",
                    "order": 12,
                    "type": "C"
                },
                {
                    "fieldName": "quote_product_sum",
                    "order": 12,
                    "type": "C"
                },
                {
                    "fieldName": "base_quote_product_sum",
                    "order": 14,
                    "type": "C"
                },
                {
                    "fieldName": "base_quote_amount",
                    "order": 16,
                    "type": "F"
                },
                {
                    "fieldName": "field_2v87e__c",
                    "order": 15,
                    "type": "F"
                },
                {
                    "fieldName": "field_w3VKg__c",
                    "order": 2,
                    "type": "C"
                },
                {
                    "fieldName": "field_IC8c9__c",
                    "order": 8,
                    "type": "C"
                },
                {
                    "fieldName": "quote_amount",
                    "order": 14,
                    "type": "F"
                }
            ]
        },
        "calculate_fields": {
            "QuoteLinesObj": [
                "base_sales_price",
                "selling_price",
                "base_total_amount",
                "total_discount",
                "base_sales_amount",
                "sales_amount",
                "field_1UE2o__c",
                "field_yno1W__c",
                "mc_exchange_rate",
                "field_IeCwj__c",
                "extra_discount",
                "base_price",
                "base_selling_price",
                "mc_exchange_rate_version",
                "mc_functional_currency"
            ],
            "QuoteObj": [
                "quote_amount",
                "field_2v87e__c",
                "quote_product_sum",
                "field_IC8c9__c",
                "field_w3VKg__c",
                "base_quote_product_sum",
                "base_quote_amount",
                "field_Du5ak__c"
            ]
        }
    },
    "is_related_team_enabled": false,
    "is_global_search_enabled": false,
    "is_follow_up_dynamic_enabled": true,
    "is_modify_record_enabled": true,
    "functional_currency": "CNY"
};
const dataGetter = {
    getDetails: () => {
        return {
            [mdApiName]: detailData
        };
    },
    getDescribe: () => {
        return {
            ...describeData
        };
    },
    getDetail: (apiName) => {
        return [...detailData];
    },
    getMasterData: () => {
        return {
            ...masterData
        };
    },
}
const dataUpdater = {
    delDetailAndTrigger: ()=>{},
    updateDetail: ()=>{},
    del: ()=>{},
    updateData: (mdApiName, rowId, data) => {
    },
    setReadOnly: () => {},
    updateMaster: () => {}
}
// 模拟底层api对象；
const pluginService = {
    api: {
        getLocal: () => {},
        setLocal: () => {},
        confirm: ({title, msg, success, cancel}) => {
        },
        request: {},
        showLoading: () => {
        },
        hideLoading: () => {
        },
        alert: () => {
        },
        i18n: (str) => {
            return str
        },
        getPlugins: () => {
            return [
                // {
                //     pluginApiName: 'price_policy'
                // }
            ]
        }
    },
    skipPlugin: () => {},
    run: () => {
    },
};

const pluginParam = {
    params: {
        fieldMapping: {},
        details: [
            {
                objectApiName: mdApiName
            }
        ]
    },
    getMasterFields() {
        return {
            "form_account_id": "account_id",
            "form_partner_id": "partner_id",
            "form_price_book_id": "price_book_id",
            "form_mc_currency": "mc_currency"
        }
    },
    bizStateConfig: {
        'contract_constraint_mode': 0,
        openPriceList: 1,
        priceBookPriority: 1,
        price_book_product_tiered_price: 1,
    }
};

const CRM = {
    _cache: {
        priceBookProductTieredPrice: true // 开阶梯价
    }
}
let defaultConfig = {
    openPriceList: true, // 开价目表
    priceBookPriority: true, // 开优先级
    price_book_product_tiered_price: '1', // 开阶梯价
    multiunitStatus: true, // 多单位
    available_range: true, // 可售范围
    contract_constraint_mode: true, // 合同约束开关
    match_price_book_valid_field: true, // 适配价目表有效期
}
const mock_getConfig = (key) => {
    // console.log('--key--', key, defaultConfig[key])
    return defaultConfig[key];
}
const mock_getAllFields = () => {
    return {
        "form_account_id": "account_id",
        "form_partner_id": "partner_id",
        "form_price_book_id": "price_book_id",
        "form_mc_currency": "mc_currency",
        "product_price": "price",
        "price_book_id": "price_book_id",
        "discount": "discount",
        "price_book_product_id": "price_book_product_id",
        "product_id": "product_id",
        "quantity": "quantity",
        "price_book_price": "price_book_price",
        "price_book_discount": "price_book_discount",
        "sales_price": "selling_price",
        "selling_price": "sales_price",
        "subtotal": "total_amount",
        "unit": "unit",
        "actual_unit": "actual_unit"
    }
}
export default {
    defaultConfig,
    mock_getConfig,
    mock_getAllFields,
    CRM,
    dataGetter,
    dataUpdater,
    pluginService,
    pluginParam,
    mdData,
    masterData,
    masterApiName,
    mdApiName,
    realPriceRes,
    addDatas,
    lookupDatas,
    detailData
}