/**
 * @desc:取价服务公共基础逻辑，web 小程序共用；
 * @author: wangshaoh
 * @date: 12/29/21
 */

import PPM from 'plugin_public_methods'
import Base from 'plugin_base'
import Add from './package/add'
import NoClearMd from './package/noclearmd'

const CovertType = {
    mapping: 'convert',     // 转换
    clone: 'copy',          // 复制
    copy_history: 'copy',   // 复用历史报价单
    add_history: 'copy',    // 从历史订单添加
    draft: 'copy',          // 来着草稿箱
};

export default class PriceService_Base extends Base {

    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.Add = new Add(pluginService, pluginParam, this);
        this.NoClearMd = new NoClearMd(pluginService, pluginParam, this);
        this.cacheChildren([this.Add, this.NoClearMd]);

        this.isOpenTieredPrice = this.getConfig('price_book_product_tiered_price') === '1';
        this.isOpenStratifiedPricing = this.getConfig('stratified_pricing') === '1';
    }

    options() {
        return {
            defMasterFields: {
                form_account_id: 'account_id',          // 客户id
                form_partner_id: 'partner_id',          // 合作伙伴id
                form_price_book_id: 'price_book_id',    // 价目表id
                form_mc_currency:'mc_currency'
            },
            defMdFields: {
                product_price: 'price',                         // 价格
                price_book_id: 'price_book_id',                 // 价目表id
                discount: 'discount',	                        // 折扣
                price_book_product_id: 'price_book_product_id', // 开启价目表，替换价目表产品id
                product_id: 'product_id',                       // 产品名称
                quantity: 'quantity',                           // 数量
                price_book_price: 'price_book_price',           // 价目表价格
                price_book_discount: 'price_book_discount',     // 价目表折扣
                sales_price: 'selling_price',                   // 销售单价
                selling_price: 'sales_price',                   // 报价
                subtotal: 'total_amount',                       // 小计
                price_book_subtotal: 'price_book_subtotal',     // 价目表小计
                price_tiered_record: 'price_tiered_record',     // 价格分层记录
                // 多单位相关
                unit:'unit',
                actual_unit: 'actual_unit',
            },
        }
    }

    // 组件渲染之前，来自复制 映射 草稿箱需要走取价服务
    async _mdRenderBefore(plugin, param) {
        let {product_id, price_book_id, price_book_product_id, } = this.getAllFields();
        let {actual_unit} = this.getPluginFields('multi-unit') || {};

        this.mdApiName = param.objApiName;
        if (CovertType[param.formType]) {
            this.pluginService.api.showLoading();
            let mdApiName = param.objApiName;
            let masterData = param.dataGetter.getMasterData();
            let details = param.dataGetter.getDetail(mdApiName);
            let r = await this.runPlugin('price-service.mdRenderBefore.before', {
                data: details, // 从对象数据
                param
            });
            if (r && r.data) details = r.data;
            await this.getRealPriceAndCalculate({
                data: details,
                mdApiName,
                actionFrom: param.formType,
                masterApiName: param.masterObjApiName,
                masterData,
                noChildrenPrice: true,
            }, param);
            this.pluginService.api.hideLoading();
        }
        if (this.getConfig('openPriceList')) {
            // // 批量编辑，过滤掉一些字段
            let filterFields = [product_id, price_book_id, price_book_product_id, actual_unit];
            return {
                __execResult:{
                    filterBatchEditFields: filterFields
                },
                __mergeDataType:{
                    array: 'concat'
                }
            }
        }
    }

    getCalIndex(data) {
        PPM.addRowId(data);
        return data.map(item => item.rowId);
    }

    // 处理草稿箱数据；删除order_id
    parseDataForDraft(data, from) {
        if (from === 'draft' && data) {
            data.forEach(item => {
                delete item.order_id;
                delete item.order_id__r;
            })
        }
    }

    // 取价and计算
    async getRealPriceAndCalculate({data = [], mdApiName = '', actionFrom = '', masterApiName = '', masterData = {}, showAlert = true, noChildrenPrice = false, triggerUIEvent = true, uncalculatePrice = false, triggerCalBefore, changeField = '', noCalculate = false} = {}, param) {
        this.parseDataForDraft(data, actionFrom);
        let res = await this.beginGetRealPrice({data, masterData, actionFrom, masterApiName, noChildrenPrice}, param);
        if (res) {
            let r1 = await this.runPlugin('price-service.getRealPriceAndCalculate.parseData', {
                data: data, // 从对象数据
                param
            });
            if (r1 && r1.data) data = r1.data;
            // 取价结果替换行数据
            let _data = await this._formatRowDataByRealPrice({data, list: res.newRst, mdApiName, res, actionFrom, masterData, changeField}, param);
            // 复制or映射or草稿箱，取价之后，可能有数据不在可售范围内，需要提示信息；
            if(showAlert){
                this._getRealPriceAndCalculate_alertMsg({
                    data: _data,
                    realRes: res,
                });
            }
            if (triggerCalBefore) data = triggerCalBefore(_data.data, res.newRst);
            // 从历史订单添加，调用取价服务后把主子数据整合成tree
            // if (actionFrom === 'add_history') _data.data = PPM.parseDataToTree(_data.data);
            if (!data.length || !res.applicablePriceSystem || noCalculate) return;
            let {
                product_price,
                sales_price,
                selling_price,
                subtotal,
                discount,
                price_book_price,
                price_book_discount,
                price_book_product_id,
                price_book_subtotal
            } = this.getAllFields(mdApiName);
            // 计算价格、折扣、销售单价、小计 及其相关字段；
            let needCalFields = [product_price, discount, sales_price, selling_price, subtotal, price_book_price, price_book_discount, price_book_product_id];
            let extraFields = [price_book_price, price_book_discount];
            
            if (res.calculatePrice && !uncalculatePrice) extraFields = extraFields.concat([product_price, discount]);
            // 计算行
            let modifyIndex = this.getCalIndex(data);
            // 复制or映射or草稿箱，取价完，计算之前，更改要计算的行index
            let r2 = await this.runPlugin('price-service.getRealPriceAndCalculate.before', {
                modifyIndex: modifyIndex,     // 要计算的行标记
                masterApiName: masterApiName, // 主对象apiname
                data: data,                   // 从对象数据
                param
            });
            if (r2 && r2.modifyIndex) modifyIndex = r2.modifyIndex;
            // 开多单位，不能计算价目表价格
            // 如果取价返回不取最新价格，就不计算价格和折扣了；
            let ignoreCalFields = res.calculatePrice && !uncalculatePrice ? [] : [product_price, discount];
            this.getConfig('multiunitStatus') ? ignoreCalFields.push(price_book_price) : extraFields.push(price_book_price);
            // 开分层,不再计算价目表小计
            this.getConfig('stratified_pricing') === '1' && ignoreCalFields.push(price_book_subtotal); // ? , price_book_price  price_book_discount
            // 计算
            let calMethod = triggerUIEvent ? 'triggerCalAndUIEvent' : 'triggerCal';
            let r = await param[calMethod]({
                // noMerge: true,
                changeFields: needCalFields,
                operateType: 'mdEdit',
                dataIndex: modifyIndex,
                objApiName: mdApiName,
                masterData: masterData,
                // details: {},
                extraFields: {[mdApiName]: extraFields},
                filterFields: {
                    [mdApiName]: ignoreCalFields
                },
            });
            if(triggerUIEvent){
                let r3 = await this.runPlugin('price-service.getRealPriceAndCalculate.end', {
                    data, // 从对象数据
                    mdApiName,
                    masterData,
                    needCalFields,
                    res,
                    param
                });
            }
        }
        return {
            data
        }
    }

    // 取价结果提示
    _getRealPriceAndCalculate_alertMsg({data = {}, realRes = {}} = {}) {
        let priceChange = data.priceChange || [];
        let excludeData = data.excludeData || [];
        if (priceChange.length || excludeData.length) {
            let names = [];
            excludeData.forEach(function (item) {
                names.push(item.product_id__r);
            });
            let calculatePrice = realRes.calculatePrice;
            let price_tip = '';
            if (priceChange.length && !calculatePrice && !CRM.util.isGrayScale('CRM_NOT_SHOW_PRICE_ALERT')) {
                price_tip = this.getConfig('openPriceList') ? this.i18n('当前产品价格为价目表的最新价格') : this.i18n('当前产品价格为产品的最新价格')
            }
            let product_tip = excludeData.length && this.getConfig('available_range') === '1' ? `${this.i18n('产品')}【${names.join('')}】，${this.i18n('未在该客户可售范围中已移除')}` : ``;
            let msg = price_tip + '<br/>' + product_tip;
            if (!price_tip && !product_tip) {
                return;
            }
            this.pluginService.api.alert(msg);
        }
    }

    /**
     * @desc 合并计算结果到主从数据；
     * @param calRes
     * @param masterData
     * @param mdData
     * @param masterApiname
     * @param mdApiname
     */
    mergeCalResToCurrent(calRes = [], masterData = {}, mdData = [], masterApiname = '', mdApiname = '') {
        masterData = Object.assign(masterData, calRes[masterApiname] || {});
        PPM.each(calRes[mdApiname] || {}, (val, key) => {
            let m = mdData.find(d => d.rowId === key);
            if (m) m = Object.assign(m, val);
        })
    }

    /**
     * @desc 走取价服务；处理各个入口
     * @param data          从对象数据
     * @param masterData    主对象数据
     * @param actionFrom    来源
     * @param masterApiName 主对象apiname
     * @param extParams     取价接口参数扩展
     * @returns {Promise<any>}
     * @private
     */
    async beginGetRealPrice({
                                data = [],
                                masterData = {},
                                actionFrom = '',
                                masterApiName = '',
                                extParams = {},
                                from = '',
                                noChildrenPrice = false,
                            } = {}, param) {
        let me = this;
        let {form_account_id, form_partner_id, product_id, form_mc_currency} = this.getAllFields();
        let pIds = PPM.compact(PPM.pluck(data || [], product_id));
        let accountId = masterData[form_account_id] || '';
        let partnerId = masterData[form_partner_id] || '';
        if (!accountId || !data || !data.length || !pIds || !pIds.length) return;
        // 从历史订单添加，调用取价服务前把主子数据平铺，server支持计算主产品价格
        // if (actionFrom === 'add_history') data = PPM.parseTreeToNormal(data, true);
        let proList = await me.parseFullProductList(data, masterApiName, from, param, actionFrom, noChildrenPrice);
        const priceBookDesc = param.dataGetter.getDescribe(me.mdApiName)?.fields?.price_book_id;
        const allDetails = param.dataGetter.getDetails();
        const details = PPM.parsePriceBookDataRangeDetails(allDetails, priceBookDesc, me.mdApiName);
        let res = await me.getRealPrice(Object.assign({}, {
            accountId: accountId,
            partnerId: partnerId,
            requestSource: CovertType[actionFrom] || '',
            object_api_name: masterApiName,
            object_data: masterData,
            fullProductList: proList,
            mcCurrency: masterData[form_mc_currency],
            details
        }, extParams));
        if(res.applicablePriceSystem && res.newRst && !res.newRst.length){
            console.error('取价结果无数据');
            // this.pluginService.api.alert($t('priceservice.result.alert1')); // 取价结果无数据
        }
        return res;
    }

    /**
     * 用取价服务返回的字段替换行字段
     * @param data  行数据
     * @param list  取价数据（价目表明细）
     * @param res   取价服务返回结果
     * @param res.applicablePriceSystem 是否使用取价接口返回的价格和折扣，根据requestSource判断租户配置，默认逻辑：转换不重新取价，返回false，复制重新取价，返回true
     * @param res.calculatePrice 是否通过计算接口重新计算价格和折扣，兼容价格默认公式被修改的企业，默认逻辑返回false，公式被修改返回true
     */
    async _formatRowDataByRealPrice({data = [], list = [], res = {}, mdApiName = '', actionFrom, masterData, changeField} = {}, param) {
        let me = this;
        let excludeData = [];
        let priceChange = [];
        let obj = {
            data: [],
            priceChange,
            excludeData,
        };
        if (!res.applicablePriceSystem) return {data};
        let _value_name = 'selling_price';
        let {
            price_book_id, discount, price_book_product_id,
            price_book_price,
            price_book_discount,
            product_price,
            actual_unit,
            price_book_subtotal,
        } = this.getAllFields();
        let mapping = {
            [price_book_id]: 'pricebook_id',                // 价目表id
            [price_book_id + '__r']: 'pricebook_id__r',     // 价目表
            [discount]: 'discount',	                        // 折扣
            [price_book_product_id]: this.getConfig('openPriceList') ? '_id' : null,  // 开启价目表，替换价目表产品id
            [price_book_product_id + '__r']: this.getConfig('openPriceList') ? 'name' : null,  // 开启价目表，替换价目表产品id
            [price_book_price]: 'pricebook_price',                             // 价目表价格
            [price_book_discount]: 'discount',                                 // 价目表折扣
            [product_price]: _value_name,                                      // 价格
            [price_book_subtotal]: 'price_book_subtotal', // 价目表小计
        };
        let des = param.dataGetter.getDescribe(mdApiName).fields;

        let len = data.length;
        for (let i = 0; i < len; i++) {
            let item = data[i];
            let r1 = await me.runPlugin('price-service.matchRealPriceData.before', {
                data: item,  // 当前行数据
                actionFrom,
                newRst: list,
                masterData,
                changeField,
                param
            });
            if(r1 && r1.noMatch){
                obj.data.push(item);
                continue;
            }
            let value = await me.matchRealPriceData(item, list, param);
            if (value) {
                let up = {};
                // 价格改变的数据提示
                if (Number(item[product_price]) !== Number(value[_value_name])) obj.priceChange.push(item);
                item._productPrice = item[product_price];

                // 用取价返回字段值替换原字段值
                PPM.each(mapping, function (cItem, key) {
                    up[key] = PPM.hasValue(value[cItem]) ? value[cItem] : item[key];
                    if ([product_price, price_book_price].includes(key) && PPM.hasValue(up[key])) {
                        if(!des[key]) return;
                        let d = des[key].decimal_places;
                        if( PPM.hasValue(d)) up[key] = PPM.formatDecimalPlace(up[key], d);
                    }
                });
                me.addUpdateDataByRealPrice(mdApiName, item, value, up);
                param.dataUpdater.updateDetail(mdApiName, item.rowId, up);
                if(!item.rowId) item = Object.assign(item, up)    
                // 取价完，回填数据时，自己补充特殊业务字段
                let r = await me.runPlugin('price-service.matchRealPrice.after', {
                    data: item,  // 当前行数据
                    value: value,// 匹配的取价数据
                    param
                });
                if (r && r.data) item = r.data;
                obj.data.push(item);
            } else {
                if(CovertType[actionFrom]){
                    param.dataUpdater.del(mdApiName, item.rowId);
                    obj.excludeData.push(item)
                }
            }
        }

        let r2 = await me.runPlugin('price-service.formatRowDataByRealPrice.after', {
            delData: obj.excludeData,  // 当前行数据
            param
        });
        return obj;
    }
    // 添加取价其他需要更新字段
    addUpdateDataByRealPrice(mdApiName, item, value, up) {
        let {price_tiered_record, discount, price_book_product_id} = this.getAllFields(mdApiName);
        // 阶梯价/分层，数量变化，回填阶梯价记录和折扣
        if (this.isOpenTieredPrice || this.isOpenStratifiedPricing) {
            up[price_tiered_record] = value['price_tiered_record'];
            if (item[price_book_product_id] === value._id && value.hasOwnProperty('detail_discount')) {   
                up[discount] = value['detail_discount'];
            }
        }
        return up;
    }
    deleteMdData(data, rowId) {

    }

    /**
     * @desc 从取价服务返回的数据中，匹配对应的明细数据；兼容明细有重复产品
     * @param rowData
     * @param realPriceData
     * @returns {*}
     */
    async matchRealPriceData(rowData = {}, realPriceData = [], param) {
        let  res = PPM.findWhere(realPriceData, {
            rowId: rowData.rowId
        });

        // 取价完，从取价结果中查找匹配数据，特殊业务更改匹配结果
        let r = await this.runPlugin('price-service.matchRealPriceData.after', {
            curData: rowData, // 行数据
            realPriceList: realPriceData, // 取价返回结果
            value: res, // 底层匹配到的取价结果
            param
        });
        if (r && r.data) res = r.data;
        return res
    }

    /**
     * @desc    格式化取价服务需要的列表参数
     * @add     取价服务支持计算bom根节点价格
     * @param data
     * @param masterApiName
     * @param from ‘add’
     * @returns {*}
     */
    async parseFullProductList(data = [], masterApiName = '', from = '', param, actionFrom = '', noChildrenPrice = false) {
        let {
            price_book_id,
            price_book_product_id,
            product_id,
            product_price,
            quantity,
            discount,
            sale_contract_line_id,
        } = this.getAllFields(param.objApiName);
        let {actual_unit, unit} = this.getPluginFields('multi-unit') || {};

        // 调用取价之前，特殊业务更改哪些数据需要调用取价服务；
        let r1 = await this.runPlugin('price-service.parseFullProductList.before', {data: data, param});
        if (r1 && r1.data) data = r1.data;
        let res = data.map(d => {
            let q = PPM.hasValue(d[quantity]) ? d[quantity] : d.amount;
            let u = from === 'add' ?  d[actual_unit] || d[unit + '__v'] || d[unit] || '' : d[actual_unit] || '' ;
            const result = {
                "rowId": d["rowId"],
                "productId": d[product_id] || d._id || '',
                "priceBookId": d.pricebook_id || d[price_book_id] || '',
                'amount': PPM.hasValue(q) ? q : '1',
                'baseUnit': d[unit + '__v'] || '', // 基础单位key值
                'unit': u || '', // 当前单位key值
            };
            if(!noChildrenPrice) result.price = d[product_price];

            // 适配销售合同约束，复制、转换需要补全销售合同明细id
            const openSaleContractConstraint = this.getConfig('contract_constraint_mode') === '1';
            if (openSaleContractConstraint && CovertType[actionFrom]) {
                result.saleContractLineId = d[sale_contract_line_id];
            }
           
            // 阶梯价/分层，数量变化，传价目表明细id和折扣字段
            if (param.fieldName === quantity && (this.isOpenTieredPrice || this.isOpenStratifiedPricing)) {
                result['priceBookProductId'] = d[price_book_product_id];
                result['discount'] = d[discount];
            }
            return result;

        });
        // 特殊业务更改取价接口的入参数据
        let r2 = await this.runPlugin('price-service.parseFullProductList.after', {
            data: res, // 取价接口参数
            metaData: data, // 从对象元数据
            noChildrenPrice,
            param
        });
        if (r2 && r2.data) res = r2.data;
        return res;
    }

    /**
     * @desc 取价服务接口，替换已选产品价格；
     * @param param
     * @returns {Promise<any>}
     */
    getRealPrice(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/available_range/service/get_real_price`;
        let p = Object.assign({}, {
            accountId: '',
            partnerId: '',
            object_api_name: '',
            // productIdList: [],
            fullProductList: []
        }, param);
        return PPM.ajax(this.request, url, p);
    }

    /**
     * @desc 开强制优先级，价目表字段不可编辑；
     * @param plugin
     * @param param
     * @private
     */
    async _mdRenderAfter(plugin, param) {
        this.setFieldsReadonly(param);
    }

    // 开强制优先级，价目表字段不可编辑；
    // 开价目表，不允许编辑产品
    setFieldsReadonly(param) {
        let {price_book_id, product_id, form_price_book_id} = this.getAllFields();
        if (this.getConfig('priceBookPriority')) {
            param.dataUpdater.setReadOnly({
                fieldName: [price_book_id],
                dataIndex: 'all',
                objApiName: param.objApiName,
                recordType: param.recordType,
                status: true
            });
        }
        if (this.getConfig('openPriceList')) {
            param.dataUpdater.setReadOnly({
                fieldName: [product_id],
                dataIndex: 'all',
                objApiName: param.objApiName,
                recordType: param.recordType,
                status: true
            });
            if(param.masterObjApiName.includes('__c') && param.formType === "add"){
                param.dataUpdater.setReadOnly({
                    fieldName: [form_price_book_id],
                    objApiName: param.masterObjApiName,
                    status: true
                });
            }
        }
    }

    // 给其他插件用，取价and提示价格有变动
    async _getRealPriceAndShowMsg(plugin, {data = [], mdApiName = '', actionFrom = '', masterApiName = '', masterData = {}, showMsg = true, param} = {}) {
        let res = await this.beginGetRealPrice({data, masterData, actionFrom, masterApiName}, param);
        let obj = {};
        if (res) {
            let _data = await this._formatRowDataByRealPrice({data, list: res.newRst, mdApiName, res, actionFrom, masterData}, param);
            if (showMsg) {
                this._getRealPriceAndCalculate_alertMsg({
                    data: _data,
                    realRes: res,
                });
            }
            obj.data = _data.data;
        }
        return{ data: obj.data || data, res};
    }

    // 给其他插件调用取价的勾子
    async _getRealPrice(plugin, {data = [], masterData = {}, actionFrom = '', masterApiName = '', param = {}} = {}){
        this.showLoading();
        let res = await this.beginGetRealPrice({data, masterData, actionFrom, masterApiName}, param);
        this.hideLoading();
        return res
    }

    // 给其他插件用，取价and提示价格有变动
    async _getRealPriceAndCalculate(plugin, {data = [], mdApiName = '', actionFrom = '', masterApiName = '', masterData = {}, showAlert = true, param} = {}) {
        this.showLoading();
        await this.getRealPriceAndCalculate({data, mdApiName, actionFrom, masterApiName, masterData, showAlert}, param);
        this.hideLoading();
    }

    // 取价并校验取件结果
    async _getRealPriceAndCheckPriceBook(plugin, {data = [], masterData = {}, actionFrom = '', masterApiName = '', param = {}} = {}){
        this.showLoading();
        let {price_book_id} = this.getAllFields();
        let res = await this.beginGetRealPrice({data, masterData, actionFrom, masterApiName}, param);
        this.hideLoading();
        let errorData = [];  // 取价未返回的产品
        let changeData = []; // 取价后价目表有变化的产品
        if(res && res.newRst){
            data.forEach(item => {
                let f = res.newRst.find(c => c.rowId === item.rowId);
                if(!f){
                    errorData.push(item)
                }else{
                    if(item[price_book_id] && item[price_book_id] !== f.pricebook_id){
                        changeData.push(item)
                    }
                }
            });
            return {errorData, changeData};
        }
        return {};
    }

    getHook(pluginService, pluginParam) {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            }, {
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            }, {
                event: 'price-service.getRealPriceAndShowMsg',
                functional: this._getRealPriceAndShowMsg.bind(this)
            }, {
                event: 'price-service.getRealPrice',
                functional: this._getRealPrice.bind(this)
            }, {
                event: 'price-service.getRealPriceAndCalculate',
                functional: this._getRealPriceAndCalculate.bind(this)
            }, {
                event: 'price-service.getRealPriceAndCheckPriceBook',
                functional: this._getRealPriceAndCheckPriceBook.bind(this)
            } ,
        ];
    }

}


