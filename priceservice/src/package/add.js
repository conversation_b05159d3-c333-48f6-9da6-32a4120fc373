/**
 * @Description: 取价服务 (添加数据)
 * <AUTHOR>
 * @date 2022/1/08
 */

import PPM from 'plugin_public_methods'

export default class Add {

    constructor(pluginService, pluginParam, parent) {
        this.parent = parent;
    }

    getAllFields(mdApiname) {
        return this.parent.getAllFields(mdApiname);
    }

    getConfig(key) {
        return this.parent.getConfig(key);
    }

    // 选完数据之后，添加数据前
    async _batchAddAfterHook(plugin, param) {
        if (!this.getConfig('openPriceList')) return;
        this.parent.pluginService.api.showLoading();
        let mdApiName = param.objApiName;
        let {
            product_id,
            price_book_product_id
        } = this.getAllFields(mdApiName);
        let lookUpApiName = param.lookupField.api_name;
        // 开强制优先级，从产品添加，需要走取价服务；or 开启明细阶梯价， or 开分层定价，需要走取价服务
        let isDoPriceService = (lookUpApiName === product_id && this.getConfig('priceBookPriority')) 
        || this.parent.isOpenTieredPrice || this.parent.isOpenStratifiedPricing;
        let isFromProductOrPriceBook = [product_id, price_book_product_id].includes(lookUpApiName);
        this._isModule2() && this.setQuantityForModule2(param.addDatas, param.lookupDatas, mdApiName, param);
        // 从xx添加数据时，是否需要走取价
        let r = await this.parent.runPlugin('price-service.batchAddAfter.before', {
            lookUpApiName, // 从xx添加
            isFromProductOrPriceBook,
            param,
        });
        if (r && r.isDoPriceService) isDoPriceService = r.isDoPriceService;
        let addData = param.addDatas;
        if (isDoPriceService) this.addPriceBookId(addData, mdApiName, param.lookupDatas, param);
        if (lookUpApiName === price_book_product_id) this.addProductInfo(addData, mdApiName, param.lookupDatas, param);
        if (isDoPriceService) {
            // 批量添加完数据，走取价服务之前，处理数据勾子
            let r1 = await this.parent.runPlugin('price-service.batchAddAfter.parseData', {
                data: addData, // 要添加从对象数据
                lookupDatas: param.lookupDatas, // 对应的元数据
                param
            });
            if (r1 && r1.data) addData = r1.data;
            let res = await this.parent.beginGetRealPrice({
                data: addData,
                masterData: param.master_data,
                masterApiName: param.masterObjApiName,
                from:'add'
            }, param);
            if (res && res.applicablePriceSystem && res.newRst) {
                this.filterFieldsBeforeCalculate(res, param);
                this.addProductInfo(addData, mdApiName, res.newRst, param, 'priceService');
                this.addResToLookUpDatas(param.lookupDatas, res.newRst);
                // 批量添加完数据，走完取价服务，回填数据勾子
                let r2 = await this.parent.runPlugin('price-service.batchAddAfter.after', {
                    data: addData, // 要添加的从对象数据
                    realPriceData: res.newRst, // 取价接口返回结果
                    lookupDatas: param.lookupDatas, // 添加数据对应的元数据
                    mdApiName: mdApiName,
                    param
                });
                if (r2 && r2.data) addData = r2.data;
            }
        }
        this.parent.pluginService.api.hideLoading();
    }
    setQuantityForModule2(addDatas, lookupDatas, mdApiName, param) {
        let {quantity} = this.getAllFields(param.objApiName);
        lookupDatas.forEach((item, index) => {
            if (item?._selfQuantity) {
                param.dataUpdater.updateDetail(mdApiName, addDatas[index].rowId, {
                    [quantity]: item._selfQuantity,
                });
            }
        })
    }
    // 模式 2
    _isModule2(){
        return CRM.util.getConfigStatusByKey('cpq_ui_mode') === '1';
    }
    // 如果不需要计算价格，去掉价格和折扣字段；
    filterFieldsBeforeCalculate(res = {}, param = {}) {
        if (!res.calculatePrice) {
            let {product_price, discount} = this.getAllFields(param.objApiName);
            let apiname = param.objApiName;
            param.filterFields = param.filterFields || {};
            param.filterFields[apiname] = param.filterFields[apiname] || [];
            param.filterFields[apiname] = param.filterFields[apiname].concat([product_price, discount])
        }
    }

    // 获取新增数据；
    getAddData(param) {
        let res = [];
        let allData = param.dataGetter.getDetail();
        param.dataIndex.forEach(rowId => {
            let f = allData.find(item => item.rowId === rowId);
            if (f) res.push(f);
        });
        return res;
    }

    addPriceBookId(data = [], mdApiName, lookupDatas = [], param) {
        let {price_book_product_id, price_book_id, product_price } = this.getAllFields(mdApiName);
        let {actual_unit} = this.parent.getPluginFields('multi-unit', mdApiName) || {};

        let m = {
            [price_book_product_id]: 'price_book_product_id',
            [price_book_id]: 'price_book_id',
            [actual_unit]: 'actual_unit',
        };
        if(!lookupDatas?.length) return;
        data.forEach((item, index) => {
            PPM.each(m, (val, key) => {
                if (lookupDatas[index].hasOwnProperty(val)) item[key] = lookupDatas[index][val];
            })
        });
    }

    /**
     * @desc 补产品id
     * @param data
     * @param lookupDatas
     */
    addProductInfo(data = [], mdApiName, lookupDatas = [], param, from = '') {
        let {product_id, price_book_product_id, price_book_id, product_price, discount,price_book_price, price_book_discount,
            price_book_subtotal, price_tiered_record

        } = this.getAllFields(mdApiName);
        let m = {
            [product_id]: 'product_id',
            [product_id + '__r']: 'product_id__r',
            [price_book_product_id]: '_id',
            [price_book_product_id + '__r']: 'name',
            [price_book_id]: 'pricebook_id',
            [price_book_id + '__r']: 'pricebook_id__r',
            [product_price]: 'selling_price',
            [discount]: 'discount',
            [price_book_discount]: 'discount',
            [price_book_price]: 'pricebook_price',                             // 价目表价格
            [price_book_subtotal]: 'price_book_subtotal', // 价目表小计
            [price_tiered_record]: 'price_tiered_record'

        };
        let des = param.dataGetter.getDescribe(mdApiName).fields;
        data.forEach((item, index) => {
            // 取价完成后有rowId，适配合同约束有可能有数据不完整需要根据rowId来匹配行
            const lookUpDataItem = from === 'priceService' && lookupDatas.length ?lookupDatas.find(({rowId}) => rowId === item.rowId) : lookupDatas[index];
            let obj = {};
            PPM.each(m, (val, key) => {
                if (lookUpDataItem && lookUpDataItem.hasOwnProperty(val)) item[key] = obj[key] = lookUpDataItem[val];
                if (key === product_price && PPM.hasValue(item[key])) {
                    let d = des[key]?.decimal_places;
                    item[key] = obj[key] = PPM.formatDecimalPlace(item[key], d);
                }
            });
            if(item.rowId) param.dataUpdater.updateDetail(mdApiName, item.rowId, obj);
        });
    }

    // 把取价结果，放到lookupdata中，多单位插件需要用
    addResToLookUpDatas(lookUpData, prsRes){
        lookUpData.forEach((item, index) => {
            item.get_real_price_result = prsRes[index]
        })
    }

    // 主对象价目表是否有值且必填
    priceBookIsRequired(param) {
        let masterData = param.dataGetter.getMasterData();
        let {form_price_book_id} = this.parent.getAllFields(param.objApiName);
        let des = param.dataGetter.getDescribe(param.masterObjApiName);
        let isOpen = this.getConfig('openPriceList');
        return isOpen && !masterData[form_price_book_id] && des.fields[form_price_book_id].is_required;
    }

    // 添加数据前
    async _batchAddBeforeHook(pluginService, param) {
        let mdApiName = param.objApiName;
        let masterData = param.dataGetter.getMasterData();
        let lookUpApiName = param.lookupField.api_name;
        let {
            price_book_product_id,
            product_id,
            form_account_id,
            form_price_book_id
        } = this.parent.getAllFields(mdApiName);
        let isSpecial = [price_book_product_id, product_id].includes(lookUpApiName);
        if (isSpecial) {
            // 提示选客户和价目表
            if (this.getConfig('openPriceList') && (!masterData[form_account_id] || this.priceBookIsRequired(param))) {
                let msg = '';
                if (!masterData[form_account_id]) {
                    msg = this.parent.i18n('请先选客户');
                } else if (!masterData[form_price_book_id]) msg = this.parent.i18n('请先选择价目表');
                pluginService.api.alert(msg);
                pluginService.skipPlugin(); // 不会执行第三个钩子
            }
        }
    }

    // 获取所有产品id，不含子产品
    getAllProductIds(details = [], field = '', recordType) {
        let res = [];
        details.forEach(item => {
            if (!item.parent_rowId && item.record_type === recordType) res.push(item[field]);
        });
        return res;
    }

    async _mdEditAfter(plugin, param) {
        let {
            price_book_product_id,
            price_book_id,
            quantity,
            product_id,
        } = this.parent.getAllFields(param.objApiName);

        let r = await this.parent.runPlugin('price-service.mdEditAfter.before', {
            param
        });

        // 切换价目表明细字段，需要补充产品信息
        let changeField = param.fieldName;
        if (changeField === price_book_product_id) this.addProductInfo_lookup(param.changeData, param.lookupData, param);
        if (changeField === price_book_id) this.setPriceBookFields(param.changeData, param.lookupData, param);
        if (changeField === product_id) await this.getRealPriceForEdit(param.changeData, param.lookupData, param);
        // 开启阶梯价/分层，需要重新取价
        if (    
            (this.parent.isOpenTieredPrice || this.parent.isOpenStratifiedPricing) 
            && [quantity, price_book_product_id,  price_book_id].includes(changeField)
        ) {
            await this.getTieredPrice(plugin, param);
        }
    }
    // 切换价目表，回填价目表明细信息；
    setPriceBookFields(changeData = {}, lookupData = {}, param){
        if (!lookupData) return;
        let {price_book_id, price_book_product_id } = this.getAllFields(param.objApiName);
        for (let key in changeData) {
            changeData[key] = Object.assign(changeData[key], {
                [price_book_id]: lookupData.pricebook_id,
                [price_book_id + '__r']: lookupData.pricebook_id__r,
                [price_book_product_id]: lookupData._id,
                [price_book_product_id + '__r']: lookupData.name,
            })
        }
    }

    // 切换价目表明细，补充产品信息
    addProductInfo_lookup(changeData = {}, lookupData = {}, param) {
        if (!lookupData) return;
        let {product_id} = this.getAllFields(param.objApiName);
        for (let key in changeData) {
            changeData[key] = Object.assign(changeData[key], {
                [product_id]: lookupData.product_id,
                [product_id + '__r']: lookupData.product_id__r,
            })
        }
    }

    // 把修改的数据更新到数据上
    updateChangeData(changeData = {}, changeDataList = []){
        changeDataList.forEach(item => {
            item = Object.assign(item, changeData[item.rowId]);
        })
    }

    // 切换产品，补充产品信息
    async getRealPriceForEdit(changeData = {}, lookupData, param) {
        if (!lookupData) return;
        let mdApiName = param.objApiName;
        let {product_price, discount, sales_price, selling_price, subtotal, price_book_price, price_book_discount, price_book_product_id} = this.getAllFields(mdApiName);
        let needCalFields = [product_price, discount, sales_price, selling_price, subtotal, price_book_price, price_book_discount, price_book_product_id];
        
        let changeRowIds = param.dataIndex;
        let detailDataList = param.dataGetter.getDetail(mdApiName) || [];
        // 根据changeRowIds取明细数据
        let changeDataList = detailDataList.filter(item => {
            return changeRowIds.includes(item.rowId || item.dataIndex)
        });

        this.updateChangeData(changeData, changeDataList);

        let r = await this.parent.runPluginSync('price-service.getRealPriceForEdit.before', {
            param,
            changeDataList,
            lookupData:[lookupData]
        });
        if (r && r.changeDataList) changeDataList = r.changeDataList;
         await this.parent.getRealPriceAndCalculate({
            data: changeDataList,
            noCalculate: true,
            mdApiName: mdApiName,
            masterApiName: param.masterObjApiName,
            masterData: param.dataGetter.getMasterData(),
            showAlert: false,
            triggerUIEvent: false,
            uncalculatePrice: true // 不重新计算价格和折扣
        }, param);

        param.beChangeFields =  param.beChangeFields || [];
        param.beChangeFields = param.beChangeFields.concat(needCalFields);

        // this.addChangeFieldsToChangeData(needCalFields, detailDataList, param);
    }

    // 把需要计算的字段，添加到changeData中
    addChangeFieldsToChangeData(needCalFields = [], detailDataList = [], param) {
        let {dataIndex} = param;
        param.changeData = param.changeData || {};
        dataIndex.forEach(rowId => {
            needCalFields.forEach(field => {
                param.changeData[rowId][field] = detailDataList.find(item => item.rowId === rowId)[field];
            })
        })
        console.log(param.changeData, 'param.changeData');
    }
    
    /**
     * 开阶梯 或 分层，需要重新取价
     * @param {*} param 
     * @param {*} changeRowIds 修改了的rowId, 需要重新取价
     * @returns 
     */
    async getTieredPrice(plugin, param) {
        let {
            price_book_subtotal,
            price_tiered_record,
            price_book_product_id,
            price_book_id,
            product_price
        } = this.parent.getAllFields(param.objApiName);

        
        let {objApiName, dataGetter, formType, masterObjApiName, dataUpdater} = param;
        let changeField = param.fieldName;
        let changeRowIds = param.dataIndex;
        let r = this.parent.runPluginSync('price-service.getTieredPrice.before', {
            param,
            changeRowIds
        });
        if(r) {
            if (!r.changeRowIds.length) return;
            changeRowIds = r.changeRowIds;
        }
        
        let detailDataList = dataGetter.getDetail(objApiName) || [];
        // 根据changeRowIds取明细数据
        let _addData = detailDataList.filter(item => {
            return changeRowIds.includes(item.rowId || item.dataIndex)
        });
        if (param.changeData) {
            _addData = _addData.map(item => {
                let _changeData = param.changeData[item.rowId || item.dataIndex];
                // 更新数量等字段值
                let _data = Object.assign({}, item, _changeData);
                return _data;
            });
        }
        // 开分层，底层不计算 价目表小计, 价格
        if (this.parent.isOpenStratifiedPricing) {
            let filterFields = param.filterFields && param.filterFields[param.objApiName] 
            ? param.filterFields : {
                [param.objApiName]: []
            };
            filterFields[param.objApiName].push(price_book_subtotal, product_price)
            
            param.filterFields = filterFields;
        }
        let res = null;
        this.parent.pluginService.api.showLoading();
        if (param.operateType === 'mdAdd') {
            res = await this.parent._getRealPriceAndShowMsg(plugin, {
                data: _addData,
                masterData: dataGetter.getMasterData(),
                masterApiName: masterObjApiName,
                mdApiName: objApiName,
                showMsg: false,
                param
            })
        } else {
            res = await this.parent.getRealPriceAndCalculate({
                data: _addData,
                triggerCalBefore: (data, newRst) => {
                    if (this.parent.isOpenTieredPrice && data?.length && newRst?.length) {
                        let {price_book_product_id} = this.getAllFields(objApiName);
                        data = data.filter(item => {
                            let r = PPM.findWhere(newRst, {
                                rowId: item.rowId
                            });
                            let isStratifiedData = this.parent.isOpenStratifiedPricing && r && !!r[price_tiered_record];
                            // 阶梯产品，如果price_book_product_id不变 ，则不计算
                            if (r && r._id === item[price_book_product_id] && !isStratifiedData) {
                                return false;
                            }
                            return true;
                        })
                    }
                    return data;
                },
                mdApiName: objApiName,
                masterApiName: masterObjApiName,
                masterData: dataGetter.getMasterData(),
                showAlert: false,
                triggerUIEvent: false,
                uncalculatePrice: true // 不重新计算价格和折扣
            }, param);
        }
        this.parent.pluginService.api.hideLoading();
        return res;
    }
    _mdCopyBefore(plugin, param){
        // let _this = this;
        // return {
        //     parseParam:function(obj){
        //         return _this.filterCalFields(obj, param.objApiName);
        //     }
        // }
    }

    // 复制计算时，去掉从对象；
    filterCalFields(obj, mdApiName) {
        if (obj.calculateFieldApiNames) delete obj.calculateFieldApiNames[mdApiName];
        if (obj.calculateFields) delete obj.calculateFields[mdApiName];
        return obj
    }

    // 从对象复制之后勾子，删除extend_obj_data_id
    _mdCopyAfter(plugin, param){
        let mdApiName = param.objApiName;
        if(param.newDataIndexs && param.newDataIndexs.length){
            param.newDataIndexs.forEach(rowId => {
                param.dataUpdater.updateData(mdApiName, rowId, {
                    extend_obj_data_id: '',
                })
            })
        }
    }

    getHook() {
        return [
            {
                event: 'md.batchAdd.after',
                functional: this._batchAddAfterHook.bind(this)
            },{
                event: 'md.batchAdd.before',
                functional: this._batchAddBeforeHook.bind(this)
            },{
                event: 'md.edit.after',
                functional: this._mdEditAfter.bind(this)
            }
            ,{
                event: 'md.copy.before',
                functional: this._mdCopyBefore.bind(this)
            },{
                event: 'md.copy.after',
                functional: this._mdCopyAfter.bind(this)
            }
            // , {
            //     event: 'quoter.execute.after',
            //     functional: this._quoterAfter.bind(this)
            // }
        ];
    }

}

