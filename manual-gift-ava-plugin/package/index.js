import _ from 'fs-hera-api/api/utils/util'
import i18n from 'fs-hera-api/api/18n/index'
import requireUtils from 'fs-hera-api/api/utils/requireUtils'


import calculator from 'paas-base-ability/utils/calculator'
import { query_detail_layout } from 'paas-base-ability/form/utils'
import { getEventAsyncFuncWithPerformance } from "paas-base-ability/common/performance_monitor"
import { objectMergeWithMaxDeep, buildDiffMap } from 'paas-base-ability/common/common'


export default class ManualGiftService {


    constructor(pluginService, pluginParam) {

        this.pluginService = pluginService
        this.pluginParam = pluginParam

        let { params } = pluginParam.describe
        let { details, fieldMapping } = params || {}
        let first = details && details[0]

        this.context = {
            requestId: _.uuid(),
            mainObjectApiName: pluginParam.describe.objectApiName,
            detailObjectApiName: first && first.objectApiName,
        }
    }




    getProxy() { return this.otc_proxy && this.otc_proxy.getProxy() }



    _updateContext(options) {
        let { dataGetter, dataUpdater } = options
        let mainObjectData = dataGetter.getMasterData()
        this.context.requestId = mainObjectData.requestId || this.context.requestId
        this.context.page_code = dataGetter.getPageOptions('_dataCode')
        this.context.pageId = dataGetter.getPageId()
        this.context.sourceAction = dataGetter.getSourceAction()
        this.context.entrySource = dataGetter.getEntrySource()
        let mainObjectApiName = mainObjectData.object_describe_api_name
        let proxy = this.getProxy()
        if (!proxy) return
        let objectDataMap = { [mainObjectApiName]: { [proxy.common.define.MAIN_OBJECT_INDEX]: mainObjectData } }
        let map = dataGetter.getDetails()
        _.each(map, (list, objectApiName) => {
            let dataMap = {}
            list && list.forEach(element => {
                // TODO: 索引考虑直接使用prod_pkg_key
                let index = element.dataIndex || element.data_index
                if (index != null) dataMap[index] = element
            })
            objectDataMap[objectApiName] = dataMap
        })
        this.context.objectDataMap = objectDataMap
    }






    _updateDataWithContext(context, dataUpdater, dataGetter) {
        for (let objectApiName in context.objectDataMap) {
            let dataMap = context.objectDataMap[objectApiName]
            if (objectApiName === context.mainObjectApiName) {
                let mainObjectData = dataMap && dataMap[Object.keys(dataMap)[0]]
                let diff = buildDiffMap(_.cloneDeep(mainObjectData), dataGetter && dataGetter.getMasterData())
                if (diff) dataUpdater.updateMaster(diff)
            } else {
                dataUpdater.updateDetailByApiName(objectApiName, Object.values(dataMap), true)
            }
        }
    }








    _triggerAddDetail(options, params, recordType) {
        console.log("manual gift plugin execute 'add.manual.gift'")
        let { formApis } = options || {}
        if (!formApis || !formApis.triggerAddDetail) return
        formApis.triggerAddDetail({ apiName: this.context.detailObjectApiName, recordType, addOpt: { isAddManualGift: true } })
    }






    async updateManualGiftsAfterSelect(options) {
        let { newDatas, objApiName, formApis } = options

        let proxy = this.getProxy()
        if (!proxy) return

        let detail_mapper = proxy.common.mapper.createContextMapper(this.context, objApiName)

        let { objectDataMap } = this.context || {}

        let calculateIndexes = []
        // formApis.createNewDataIndex()
        newDatas && newDatas.forEach((element, index) => {
            if (element.dataIndex == null || element.dataIndex == '') element.dataIndex = formApis.createNewDataIndex()
            calculateIndexes.push(element.dataIndex)
            let map = objectDataMap && objectDataMap[objApiName]
            if (map) map[element.dataIndex] = element
        })
        // TODO: resetTemporaryGiveawayDatas
        let dataDiffMap = await proxy.manual_gift.core.resetManualGifts(this.context, calculateIndexes)

        objectMergeWithMaxDeep(objectDataMap || {}, dataDiffMap || {}, 2)

        let detailDiffMap = dataDiffMap && dataDiffMap[objApiName] || {}

        let noCalcFieldMap = {}
        dataDiffMap && newDatas && newDatas.forEach(data => {
            if (!data) return
            let diff = detailDiffMap[data.dataIndex]
            if (!diff) return
            for (let key in diff) { noCalcFieldMap[key] = 1 }
        })

        return (detail_mapper.mappedFields(['is_giveaway', 'gift_type']) || []).concat(Object.keys(noCalcFieldMap))
    }


    customAddButtons(opt) {
        if (!opt) return opt
        let { buttons } = opt
        // 移除添加临时赠品按钮
        opt.buttons = (buttons || []).filter(button => button.lookup_field_name !== 'field_manual_gifts')
        return opt
    }





    checkPureManualGifts(array, objApiName) {
        if (!array || !array.length) return false
        let proxy = this.getProxy()
        if (!proxy) return
        return array.reduce((res, data) => {
            return res && proxy.manual_gift.utility.isManualGift(data, proxy.common.mapper.objectFieldMap(objApiName, this.context.mapper))
        }, true)
    }










    async afterRegisterPlugins(pluginExecResult, options) {
        let res = await requireUtils.requireAsync('../../objformpkgo2c/package/onsale/plugins/otc-ability-proxy')
        this.otc_proxy = res && res.default
        await this.otc_proxy.initProxy()
        let proxy = this.getProxy()
        if (!proxy) return
        this.context.mapper = proxy.common.mapper.objectFieldsMapperFromPlugin(this.pluginParam),
            this.context.businessConfiguration = proxy.common.configuration.parseMap(this.pluginParam.bizStateConfig)
    }




    async handleLogicAfterRenderEnd(pluginExecResult, options) {
        console.log("manual gift plugin execute 'form.render.end'")

        this._updateContext(options)
        let proxy = this.getProxy()
        if (!proxy) return
        this.context = proxy.common.plugin_public.richContextOnRenderEnd(options, this.context)

        let { dataGetter, dataUpdater } = options || {}
        let recoverData = dataGetter && dataGetter.getRecoverData && dataGetter.getRecoverData()
        if (recoverData) return

        let sourceAction = dataGetter && dataGetter.getSourceAction()
        if (proxy.common.isClone(sourceAction) || proxy.common.isConvert(sourceAction)) {
            let { mapper, objectDataMap, detailObjectApiName } = this.context || {}
            // 复制时去掉临时赠品
            let dataMap = objectDataMap && objectDataMap[detailObjectApiName]
            _.each(dataMap, (data, index) => {
                if (proxy.manual_gift.utility.isManualGift(data, proxy.common.mapper.objectFieldMap(detailObjectApiName, mapper))) delete dataMap[index]
            })
        }
        // TODO: refreshAmortizeOfTemporaryGiveaways
        await proxy.manual_gift.core.refreshAmortizeForManualGifts(this.context)
        this._updateDataWithContext(this.context, dataUpdater, dataGetter)
    }





    async changeFieldEnd(pluginExecResult, options) {
        console.log("manual gift plugin execute 'field.edit.end'")
        this._updateContext(options)
        let { preData } = pluginExecResult || {}
        if (options.objApiName !== this.context.detailObjectApiName) return preData
        let context = this.context
        let proxy = this.getProxy()
        if (!proxy) return
        let { objectDataDiffMap } = proxy.common.plugin_public.pluginEventOperationContext({ options, context }) || {}
        if (!objectDataDiffMap || Object.keys(objectDataDiffMap).length === 0) return preData

        context.topChangeInfo = objectDataDiffMap
        context.objectDataDiffMap = objectDataDiffMap
        let calculateIndexes = []
        let map = objectDataDiffMap[this.context.detailObjectApiName]
        let origin_map = context.objectDataMap && context.objectDataMap[this.context.detailObjectApiName]
        for (let index in map) {
            let detail = origin_map && origin_map[index]
            if (proxy.manual_gift.utility.isManualGift(detail)) calculateIndexes.push(index)
        }
        // TODO: refreshAmortizeThenCalculate
        await proxy.manual_gift.core.refreshAmortizeAndCalculate(context, calculateIndexes)
        this._updateDataWithContext(context, options.dataUpdater, options.dataGetter)

        return preData
    }




    beforeMdRender(pluginExecResult, options) {

        console.log("manual gift plugin execute 'md.render.before'")

        let { preData } = pluginExecResult || {}

        let proxy = this.getProxy()
        if (!proxy) return
        if (!proxy.manual_gift.utility.isOpenManualGift(this.context.businessConfiguration)) return preData

        let { mdBottomComs, objApiName, dataGetter, dataUpdater } = options
        if (objApiName !== this.context.detailObjectApiName) return preData

        mdBottomComs = preData && preData.mdBottomComs || mdBottomComs

        let handleNormalButtons = (preData && preData.handleNormalButtons) || options.handleNormalButtons || []
        let handleBarButtons = (preData && preData.handleBarButtons) || options.handleBarButtons || []


        let describeLayout = dataGetter.getDescribeLayout()

        handleBarButtons.push((function (opt) {
            if (!opt) return opt
            let { buttons, recordType } = opt
            let layout = query_detail_layout(describeLayout, recordType, this.context.detailObjectApiName)
            let btn = proxy.manual_gift.utility.isExistManualAddBtn(layout)
            if (!btn) return opt
            // ava.manual.gift.add.btn.text 添加临时赠品
            opt.buttons = [{
                action: "pre-add-manual-gift",
                label: i18n.get("ava.manual.gift.add.btn.text"),
                hideIfNoData: false,
                api_name: "Batch_Lookup_Add_button_manual_gift",
                lookup_field_name: "field_manual_gifts",
                onClick: (function (params) {
                    this._triggerAddDetail(options, params, recordType)
                }).bind(this)
            }].concat(buttons || [])
        }).bind(this))

        handleNormalButtons.push(this.customAddButtons.bind(this))

        return Object.assign({}, preData || {}, { mdBottomComs, refreshUI: {}, handleNormalButtons, handleBarButtons })
    }



    syncDecorateItemBeforeRender(pluginExecResult, options) {
        console.log("manual gift plugin execute 'md.item.render.before.sync'")
        let { preData } = pluginExecResult || {}
        let { titleLeftBtns } = preData || {}
        titleLeftBtns = titleLeftBtns || []
        let { objectData, objApiName } = options
        let proxy = this.getProxy()
        if (!proxy) return
        let is_manual_gift = proxy.manual_gift.utility.isManualGift(objectData, proxy.common.mapper.objectFieldMap(objApiName, this.context.mapper))
        let identifier = "manual.gift.tag"
        titleLeftBtns = titleLeftBtns.filter(i => i.identifier != identifier)
        if (is_manual_gift) titleLeftBtns.push({
            identifier,
            // "ava.manual.gift.tag": "临赠"
            label: i18n.get("ava.manual.gift.tag"),
            style: "background: #FFFFFF; color: #30C776; font-size: 12px; line-height: 18px; border: 1px solid #30C776; box-sizing: border-box; border-radius: 2px; padding: 0px 3px; margin-right: 6px;",
            onClick: () => { }
        })
        titleLeftBtns = titleLeftBtns.length > 0 ? titleLeftBtns : null
        return Object.assign({}, preData || {}, { titleLeftBtns })
    }




    async beforeBatchAddDetailAction(pluginExecResult, options) {
        console.log("manual gift plugin execute 'md.batchAdd.before'")
        let { preData } = pluginExecResult || {}
        let { selectObjectParams, lookupField, addOpt, dataGetter } = options
        let { isAddManualGift } = addOpt || {}
        if (!isAddManualGift) return preData

        let proxy = this.getProxy()
        if (!proxy) return preData

        selectObjectParams = selectObjectParams || {}
        selectObjectParams.filters = selectObjectParams.filters || []

        let target_api_name = selectObjectParams.targetApiName || (lookupField && lookupField.target_api_name)
        if (!target_api_name) return preData

        let main_mapper = proxy.common.mapper.createContextMapper(this.context, this.context.mainObjectApiName)
        let mapper = proxy.common.mapper.createContextMapper(this.context, target_api_name)
        selectObjectParams.filters.push({ "operator": proxy.operators.EQ, "field_name": mapper.field_name("is_package"), "field_values": [false] })
        selectObjectParams.filters.push({ "operator": proxy.operators.NHASANYOF, "field_name": mapper.field_name("is_giveaway"), "field_values": ["1", "2"] })
        // 判断该对象是否开启价格政策
        let isOpenPricePolicy = proxy.price_policy.utility.isObjectEnablePolicy(this.context.businessConfiguration, this.context.mainObjectApiName)
        if (isOpenPricePolicy) {
            let filter_gift_range = this.context.businessConfiguration && this.context.businessConfiguration.enable_gift_range_shelves
            let mainObjectData = dataGetter && dataGetter.getMasterData()
            let r_f = filter_gift_range ? function (res, f) {
                res = res || {}
                if (mainObjectData) mapper.setValue(res, f, main_mapper.value(mainObjectData, f))
                return res
            } : function (res, f) {
                if (res) delete res[mapper.field_name(f)]
                return res
            }
            selectObjectParams.formObjectData = (filter_gift_range ? ['account_id', 'partner_id', 'price_book_id'] : ['account_id']).reduce(r_f, _.cloneDeep(selectObjectParams.formObjectData || {}))
        }

        // 从合同添加	本品（不能选赠品、返利品）	本品（不能选赠品、返利品）
        // 从自定义来源添加	本品（不能选赠品、返利品）	本品（不能选赠品、返利品

        return Object.assign({}, preData || {}, { selectObjectParams })
    }



    ///////////////////////////// 批量添加 ///////////////////////////////////



    async _afterBatchAddDetail(pluginExecResult, options) {
        let { preData } = pluginExecResult || {}
        let { objApiName, newDatas, addOpt } = options || {}
        let { isAddManualGift } = addOpt || {}

        if (!isAddManualGift) return preData

        if (objApiName != this.context.detailObjectApiName || !newDatas || newDatas.length == 0) return preData

        let { businessConfiguration } = this.context

        let proxy = this.getProxy()
        if (!proxy) return

        let detail_mapper = proxy.common.mapper.createContextMapper(this.context, objApiName)

        newDatas.forEach(data => {
            detail_mapper.setValue(data, 'gift_type', 'manual')
            detail_mapper.setValue(data, 'is_giveaway', '1')
            detail_mapper.setValue(data, 'prod_pkg_key', _.uuid())
        })


        if ((businessConfiguration && businessConfiguration.gift_amortize_basis) == 'product_price') {

            let search_query_info = { filters: [{ operator: proxy.operators.EQ, value_type: 0, field_values: newDatas.map(data => detail_mapper.value(data, 'product_id')), field_name: "_id", }], offset: 0, limit: 1000 }
            // 获取产品档案价
            let productList = await proxy.stand_price.queryProductList({ search_query_info })

            newDatas.forEach(data => {
                let product = productList && productList.find(e => e._id == detail_mapper.value(data, 'product_id'))
                if (product) {
                    data.basic_price_backup = product.price
                    if (!proxy.multi_unit.utility.isMultiUnit(data, detail_mapper.field_name('is_multiple_unit__v')) && !proxy.multi_unit.utility.isMultiUnit(data, detail_mapper.field_name('is_multiple_unit'))) return
                    data.basic_price_backup = calculator.mul(data.basic_price_backup, detail_mapper.value(data, 'conversion_ratio'))
                } else data.basic_price_backup = '0'
            })
        }


        let noCalcFields = await this.updateManualGiftsAfterSelect(options)

        if (noCalcFields.length <= 0) return preData

        let { extraCalUiParams } = preData || {}
        let { filterFields } = extraCalUiParams || {}

        noCalcFields = noCalcFields.concat((filterFields || {})[objApiName] || [])
        return Object.assign(preData || {}, {
            extraCalUiParams: Object.assign(extraCalUiParams || {}, { filterFields: Object.assign(filterFields || {}, { [objApiName]: noCalcFields }) })
        })
    }





    async afterBatchAddDetailAction(pluginExecResult, options) {
        console.log("manual gift plugin execute 'field.edit.end'")
        return await this._afterBatchAddDetail(pluginExecResult, options)
    }




    async batchAddDetailActionEnd(pluginExecResult, options) {
        let { preData } = pluginExecResult || {}
        let { objApiName, newDatas, dataUpdater } = options || {}
        this._updateContext(options)
        return preData
    }



    ///////////////////////////// 添加一行 ///////////////////////////////////


    async afterAddDetailAction(pluginExecResult, options) {
        console.log("manual gift plugin execute 'md.add.after'")
        if (!options.newData) return
        options.newDatas = [options.newData]
        return await this._afterBatchAddDetail(pluginExecResult, options)
    }




    async addDetailActionEnd(pluginExecResult, options) {
        console.log("manual gift plugin execute 'md.add.end'")
        if (!options.newData) return
        options.newDatas = [options.newData]
        return await this.batchAddDetailActionEnd(pluginExecResult, options)
    }

    ///////////////////////////// 复制 ///////////////////////////////////


    async afterCloneDetailAction(pluginExecResult, options) {
        console.log("manual gift plugin execute 'md.clone.after'")
        return await this._afterBatchAddDetail(pluginExecResult, options)
    }



    async cloneDetailActionEnd(pluginExecResult, options) {
        console.log("manual gift plugin execute 'md.clone.end'")
        return await this.batchAddDetailActionEnd(pluginExecResult, options)
    }



    customizeDatasAfterMappingNewlyAddedDetails(pluginExecResult, options) {
        let { dataMap, newDatas, lookupField, objApiName } = options || {}
        let field_name = lookupField && lookupField.api_name
        let proxy = this.getProxy()
        if (!proxy || !dataMap || Object.keys(dataMap).length === 0) return dataMap
        let detail_mapper = proxy.common.mapper.createContextMapper(this.context, objApiName)
        let special_fields = detail_mapper.mappedFields(['sales_price', 'subtotal', 'discount', 'prod_pkg_key', 'dynamic_amount', 'is_giveaway', 'gift_type'])
        for (let key in dataMap) {
            let data = dataMap[key]
            if (!data) continue
            let new_data = newDatas && newDatas.find(i => i[field_name] === data[field_name])
            if (detail_mapper.value(new_data, 'gift_type') === 'manual') {
                special_fields.forEach(f => {
                    if (Object.hasOwnProperty.call(new_data, f)) data[f] = new_data[f]
                })
            }
        }
        return dataMap
    }




    apply() {
        if (Boolean(Number(this.pluginParam.bizStateConfig && this.pluginParam.bizStateConfig.manual_gift)) === false) return []
        return [{
            event: 'pluginService.use.after',
            functional: getEventAsyncFuncWithPerformance(this.afterRegisterPlugins.bind(this), 'pluginService.use.after', { subModule: this.pluginParam && this.pluginParam.pluginApiName, biz: 'ava_object_form', module: 'sfa' }),
        }, {
            event: "form.render.end",
            functional: this.handleLogicAfterRenderEnd.bind(this),
        }, {
            event: "field.edit.end",
            functional: this.changeFieldEnd.bind(this)
        }, {
            event: "md.render.before",
            functional: this.beforeMdRender.bind(this),
        }, {
            event: "md.batchAdd.before",
            functional: this.beforeBatchAddDetailAction.bind(this),
        }, {
            event: "md.batchAdd.after",
            functional: this.afterBatchAddDetailAction.bind(this),
        }, {
            event: "md.batchAdd.end",
            functional: this.batchAddDetailActionEnd.bind(this)
        }, {
            event: "md.add.after",
            functional: this.afterAddDetailAction.bind(this),
        }, {
            event: "md.add.end",
            functional: this.addDetailActionEnd.bind(this),
        }, {
            event: "md.clone.after",
            functional: this.afterCloneDetailAction.bind(this),
        }, {
            event: "md.clone.end",
            functional: this.cloneDetailActionEnd.bind(this),
        }, {
            event: "md.item.render.before.sync",
            functional: this.syncDecorateItemBeforeRender.bind(this)
        }, {
            event: "customize.data.after.mapping.newly.added.details",
            functional: this.customizeDatasAfterMappingNewlyAddedDetails.bind(this)
        }]
    }


}