import _ from 'fs-hera-api/api/utils/util'
import i18n from 'fs-hera-api/api/18n/index'
import fsapi from 'fs-hera-api'
import requireUtils from 'fs-hera-api/api/utils/requireUtils'


import { validString } from 'paas-base-ability/utils/util'
import { buildDiffMap } from "paas-base-ability/common/common"
import { stringifyError, IgnoreError } from 'paas-base-ability/utils/error-utility'
import { getEventAsyncFuncWithPerformance } from "paas-base-ability/common/performance_monitor"


import { dhtWeLogService } from 'dht-services/DhtWeLogService'


export default class RebateCouponService {

    constructor(pluginService, pluginParam) {

        this.pluginService = pluginService
        this.pluginParam = pluginParam

        let { params } = pluginParam.describe
        let { details, fieldMapping } = params || {}
        let first = details && details[0]

        this.context = {
            requestId: _.uuid(),
            mainObjectApiName: pluginParam.describe.objectApiName,
            detailObjectApiName: first && first.objectApiName,
        }
    }



    getProxy() {
        return this.otc_proxy && this.otc_proxy.getProxy()
    }



    _updateContext(options) {
        let { dataGetter, formApis, dataUpdater } = options
        let mainObjectData = dataGetter.getMasterData()
        this.context.requestId = mainObjectData.requestId || this.context.requestId
        this.context.page_code = dataGetter.getPageOptions("_dataCode")
        this.context.pageId = dataGetter.getPageId()
        this.context.sourceAction = dataGetter.getSourceAction()
        this.context.entrySource = dataGetter.getEntrySource()
        let mainObjectApiName = mainObjectData.object_describe_api_name
        let proxy = this.getProxy()
        if (!proxy) return
        let objectDataMap = { [mainObjectApiName]: { [proxy.common.define.MAIN_OBJECT_INDEX]: mainObjectData } }
        let map = dataGetter.getDetails()
        let mapper = proxy.common.mapper.createContextMapper(this.context, this.context.detailObjectApiName)
        _.each(map, (list, objectApiName) => {
            let dataMap = {}
            list && list.forEach(element => {
                // TODO: 索引考虑直接使用prod_pkg_key
                let index = element.dataIndex || element.data_index
                if (index != null) {
                    dataMap[index] = element
                    if (objectApiName === this.context.detailObjectApiName) {
                        let prod_pkg_key = mapper.value(element, 'prod_pkg_key')
                        if (!validString(prod_pkg_key)) {
                            prod_pkg_key = _.uuid()
                            mapper.setValue(element, 'prod_pkg_key', prod_pkg_key)
                            dataUpdater && dataUpdater.updateDetail(objectApiName, index, { [mapper.field_name('prod_pkg_key')]: prod_pkg_key }, false)
                        }
                    }
                }
            })
            objectDataMap[objectApiName] = dataMap
        })
        this.context.objectDataMap = objectDataMap
        this.context.index_keygen = formApis && formApis.createNewDataIndex
        this.context.ui_event_handler = !formApis ? null : {
            doUIEvent: (async function (parameter, hookObj) {
                let {
                    bizInfo,
                    event_id,
                    seriesId,
                    detailObjectData,
                    masterObjectData,
                    maskFieldApiNames,
                    masterLayoutApiName,
                } = parameter || {}
                try {
                    if (hookObj && hookObj.beforeUIEvent) hookObj.beforeUIEvent()
                    let result = await formApis.triggerCalAndUIEvent({
                        beforeUiPost: (function (params) {
                            if (!params || !params.data) return
                            params.data.detail_object_data = detailObjectData
                            params.data.object_data = masterObjectData
                            params.bizInfo = bizInfo || params.bizInfo
                        }).bind(this),
                        objApiName: masterObjectData && masterObjectData.object_describe_api_name,
                        uiEventId: event_id,
                    })
                    if (hookObj && hookObj.afterUIEvent) hookObj.afterUIEvent()
                    return result && result.uiRst
                } catch (error) {
                    throw new IgnoreError(stringifyError(error))
                }
            }).bind(this)
        }
    }


    _updateDataWithContext(context, dataUpdater, dataGetter) {
        let proxy = this.getProxy()
        if (!proxy) return
        for (let objectApiName in context.objectDataMap) {
            let dataMap = context.objectDataMap[objectApiName]
            if (objectApiName === context.mainObjectApiName) {
                let mainObjectData = dataMap && dataMap[Object.keys(dataMap)[0]]
                let diff = buildDiffMap(_.cloneDeep(mainObjectData), dataGetter && dataGetter.getMasterData())
                if (diff) dataUpdater.updateMaster(diff)
            } else {
                let list = Object.values(dataMap)
                if (list) list = list.filter(i => i != null)
                if (proxy.rebate_coupon.sortDetailsForCommit) list = proxy.rebate_coupon.sortDetailsForCommit(context, list)
                dataUpdater.updateDetailByApiName(objectApiName, list, true)
            }
        }
    }


    async fillBusinessConfiguration() {
        if (!_.isEmpty(this.context.businessConfiguration)) return
        let proxy = this.getProxy()
        if (!proxy) return
        let config = await proxy.rebate_coupon.queryBusinessConfiguration(this.context)
        this.context.businessConfiguration = Object.assign(this.context.businessConfiguration || {}, config || {})
    }



    async prepare() {
        if (this.formatFinished) return
        let proxy = this.getProxy()
        if (!proxy) return
        proxy.rebate_coupon.formatDataWithMiscContent(this.context)
        this.formatFinished = true
    }



    _createContextIdentifier(dataGetter) {
        if (!dataGetter) return 'rebate_coupon'
        let pageCode = dataGetter.getPageOptions('_dataCode')
        return `${pageCode}_rebate_coupon`
    }

    _merge_mapper(mapper, other) {
        if (!other) return mapper
        if (!mapper) return other
        for (let objectApiName in other) {
            let other_map = other[objectApiName]
            mapper[objectApiName] = Object.assign(mapper[objectApiName] || {}, other_map)
        }
        return mapper
    }


    getPeriodProductPluginMapper(pluginExecResult) {
        let plugins = pluginExecResult && pluginExecResult.api.getPlugins()
        if (!plugins || plugins.length === 0) return

        let period_product_plugin = plugins.find(i => i.pluginApiName === 'period_product')
        if (!period_product_plugin) return
        let { objectApiName, params } = period_product_plugin
        let { details, fieldMapping } = params || {}

        let map = { [objectApiName]: fieldMapping }
        details && details.forEach(e => { map[e.objectApiName] = e.fieldMapping })

        this.period_product_mapper = map
    }


    async afterRegisterPlugins(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'pluginService.use.after'")
        try {
            let obj = await requireUtils.requireAsync('../objformpkgo2c/package/onsale/plugins/plugin_share')
            let share = obj.getPluginShare()
            share.destroyContext(this._createContextIdentifier(options && options.dataGetter))
        } catch (error) {
            console.log(`rebate coupon plugin 'pluginService.use.after' require plugin share error: ${stringifyError(error)}`)
            console.error(error)
        }
        this.getPeriodProductPluginMapper(pluginExecResult)
    }

    async buildContext(pluginExecResult, options) {
        if (options && options.dataGetter) this._updateContext(options)
        return this.context
    }


    syncQueryContext(pluginExecResult, options) {
        if (options && options.dataGetter) this._updateContext(options)
        return this.context
    }





    checkCouponPrecision(context) {
        let proxy = this.getProxy()
        if (proxy.coupon.utility.verifyKeyFieldsPrecision(context)) return true

        let { objectDescribeMap, detailObjectApiName } = context || {}
        let objectDescribe = objectDescribeMap && objectDescribeMap[detailObjectApiName]
        let fields = objectDescribe && objectDescribe.fields
        let mapper = proxy.common.mapper.createContextMapper(context, detailObjectApiName)
        let field_name_1 = mapper.field_name(proxy.coupon.standard_fields.COUPON_DYNAMIC_AMOUNT)
        let field_name_2 = mapper.field_name(proxy.coupon.standard_fields.COUPON_AMORTIZE_AMOUNT)
        let field_1 = fields && fields[field_name_1]
        let field_2 = fields && fields[field_name_2]
        // "ava.coupon.precision.issue.prompt.before.query": "{coupon_dynamic_amount}精度小于{coupon_amortize_amount}精度，可能导致优惠额尾差，请把两者精度配置为一致。"
        this.pluginService.api.alert(i18n.get("ava.coupon.precision.issue.prompt.before.query", [{
            coupon_dynamic_amount: field_1 && field_1.label,
            coupon_amortize_amount: field_2 && field_2.label
        }]))
        return false
    }






    async tryUsingCouponsAutomatically(options) {
        let { dataUpdater, dataGetter } = options || {}
        let proxy = this.getProxy()
        let context = this.context
        let masterData = dataGetter.getMasterData()
        let isFromShoppingCart = masterData && masterData.form_source === 'dht_cart_obj'
        if (!isFromShoppingCart || !this.checkCouponPrecision(context)) return

        await proxy.rebate_coupon.tryUsingCouponsAutomatically(context)
        this._updateDataWithContext(context, dataUpdater, dataGetter)
        return masterData && masterData.couponDatas && masterData.couponDatas[0]
    }






    async coordinateCouponLogicAfterRenderEnd(options) {
        let { businessConfiguration, objectDataMap, mainObjectApiName } = this.context
        let proxy = this.getProxy()
        let mainObjectData = proxy.common.utility.getMainObjectData(objectDataMap, mainObjectApiName)
        let openPricePolicy = proxy.price_policy.utility.isObjectEnablePolicy(businessConfiguration, mainObjectApiName)
        let { couponInstanceInfo, couponInstanceConsumed } = mainObjectData || {}
        let { repel_price_policy, id } = couponInstanceInfo || {}
        let isFromCouponDetail = !_.isEmpty(couponInstanceInfo) || couponInstanceConsumed

        let tryShowNotConsistentPrompt = (function (coupon) {
            // 2. 自动使用优惠券，若结果与预期不一致，需弹出提示
            let is_consistent = id === (coupon && coupon.id)
            console.log(`coupon plugin auto using automatically ${is_consistent ? 'consistent' : 'not consistent'}`)
            if (!is_consistent) {
                // "ava.coupon.auto.use.fit.other.not.pre.prompt": "因未满足该优惠券的使用条件，不能使用该券，已为您选用其他可用的优惠券"
                // "ava.coupon.auto.use.none.fit.with.pre.prompt": ""
                let text = i18n.get(coupon ? 'ava.coupon.auto.use.fit.other.not.pre.prompt' : 'ava.coupon.auto.use.none.fit.with.pre.prompt')
                this.pluginService.api.alert(text)
            }
        }).bind(this)

        let { dataUpdater, dataGetter, formApis } = options || {}
        console.log("coupon plugin call using automatically")
        this.context.coupon_hook = {
            customQuery: (async function (parameter, opt) {
                let res = await formApis.npcRun("custom.query.coupon.parameter", { parameter })
                return Object.assign(parameter || {}, res || {})
            }).bind(this),
            customAutoUse: (async function (parameter, opt) {
                let res = await formApis.npcRun("custom.auto.use.coupon.parameter", { parameter })
                return Object.assign(parameter || {}, res || {})
            }).bind(this)
        }

        // 非从优惠券详情进入或者与价格政策不互斥，执行自动用券逻辑(会判定是否从购物车进入以及精度一致性的校验)
        if (!isFromCouponDetail) return await this.tryUsingCouponsAutomatically(options)
        if (!repel_price_policy) {
            await this.tryUsingCouponsAutomatically(options)
            tryShowNotConsistentPrompt(mainObjectData && mainObjectData.couponDatas && mainObjectData.couponDatas[0])
            return
        }

        // 以下是从优惠券详情进入，并且期望使用的优惠券与价格政策互斥时，执行的逻辑
        // 1. 若相关字段的精度校验不通过，则无法使用优惠券，抛出事件，执行价格政策逻辑
        // 2. 自动使用优惠券，若结果与预期不一致，需弹出提示
        // 3. 自动使用优惠券的结果，如果与价格政策不互斥，抛出事件，执行价格政策逻辑

        // 抛出事件("coupon.auto.use.end.after.page.ready")，执行价格政策逻辑
        let emitEvent = (function () {
            console.log("coupon plugin run event 'coupon.auto.use.end.after.page.ready'")
            this.pluginService.run("coupon.auto.use.end.after.page.ready", options)
        }.bind(this))

        // 1. 若相关字段的精度校验不通过，则无法使用优惠券，抛出事件，执行价格政策逻辑
        if (!this.checkCouponPrecision(this.context) && openPricePolicy) return emitEvent()
        await proxy.rebate_coupon.tryUsingCouponsAutomatically(this.context)
        this._updateDataWithContext(this.context, dataUpdater, dataGetter)
        let coupon = mainObjectData && mainObjectData.couponDatas && mainObjectData.couponDatas[0]
        tryShowNotConsistentPrompt(coupon)
        // 3. 自动使用优惠券的结果，如果与价格政策不互斥，抛出事件，执行价格政策逻辑
        if (coupon && coupon.repel_price_policy) return
        emitEvent()
    }








    async handleLogicAfterRenderEnd(pluginExecResult, options) {

        console.log("rebate coupon plugin execute 'form.render.end'")

        let res = await requireUtils.requireAsync('../objformpkgo2c/package/onsale/plugins/otc-ability-proxy')
        this.otc_proxy = res && res.default
        await this.otc_proxy.initProxy()
        let proxy = this.getProxy()
        if (!proxy) return
        let { dataGetter, dataUpdater } = options
        let sourceAction = dataGetter.getSourceAction()
        this._updateContext(options)
        this.context.mapper = this._merge_mapper(proxy.common.mapper.objectFieldsMapperFromPlugin(this.pluginParam), this.period_product_mapper)
        this.context.businessConfiguration = proxy.common.configuration.parseMap(this.pluginParam.bizStateConfig)
        this.context = proxy.common.plugin_public.richContextOnRenderEnd(options, this.context)
        await this.prepare()

        let businessConfiguration = this.context.businessConfiguration
        let { coupon, rebate } = businessConfiguration || {}
        if (!coupon && !rebate) return
        if (proxy.common.plugin_public.is_data_scarcity(this.context)) return

        let recoverData = dataGetter && dataGetter.getRecoverData && dataGetter.getRecoverData()
        if (recoverData) {
            await this._store_context(options)
            if (this.fund_rebate_amount_querier) await this.fund_rebate_amount_querier(this.context)
            return
        }

        let token = `rebate.coupon.form.render.end.${_.uuid()}`
        this.context.ui_event_hook = {
            beforeUIEvent: (function () {
                this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            }).bind(this),
            afterUIEvent: (function () {
                if (validString(token)) this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
            }).bind(this)
        }
        try {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
            await proxy.rebate_coupon.queryConditionFields(this.context)
            if (sourceAction === 'Edit' || sourceAction === 'edit') {
                dataUpdater.updateMaster(proxy.common.utility.getMainObjectData(this.context.objectDataMap, this.context.mainObjectApiName))
            } else {
                // 快消已支持使用优惠券，但是不会跳转到表单页面，不会触发form.render.end。后续若有调整需注意⚠️。
                let result = await proxy.rebate_coupon.clearAllRebatesAndCoupons(this.context)
                console.log("rebate coupon plugin update after clear")
                this._updateDataWithContext(this.context, dataUpdater, dataGetter)
            }
            if (coupon) await this.coordinateCouponLogicAfterRenderEnd(options)
            if (this.fund_rebate_amount_querier) await this.fund_rebate_amount_querier(this.context)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            this.context.ui_event_hook = null
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            this.context.ui_event_hook = null
            if (typeof error === 'object' && error.name === 'IgnoreError') return
            let str = stringifyError(error)
            if (validString(str)) this.pluginService.api.showToast(str)
        }
    }






    async handleLogicBeforeSubmit(pluginExecResult, options) {

        console.log("rebate coupon plugin execute 'form.submit.before'")

        this._updateContext(options)
        let businessConfiguration = this.context.businessConfiguration
        let { coupon, rebate } = businessConfiguration || {}
        if (!coupon && !rebate) return
        
        let proxy = this.getProxy()
        let { couponConditionFieldChanged, rebateConditionFieldChanged } = (proxy && proxy.common.utility.getMainObjectData(this.context.objectDataMap, this.context.mainObjectApiName)) || {}
        if (!couponConditionFieldChanged && !rebateConditionFieldChanged) return

        let token = `rebate.coupon.form.submit.before.${_.uuid()}`
        this.context.ui_event_hook = {
            beforeUIEvent: (function () {
                this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            }).bind(this),
            afterUIEvent: (function () {
                if (validString(token)) this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
            }).bind(this)
        }
        let { dataUpdater, dataGetter, details, object_data } = options
        try {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
            if (!proxy || proxy.common.plugin_public.is_data_scarcity(this.context)) {
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                this.context.ui_event_hook = null
                return
            }
            let result = await proxy.rebate_coupon.checkValidityOfRebateAndCoupon(this.context)
            if (result) this._updateDataWithContext(this.context, dataUpdater, dataGetter)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            this.context.ui_event_hook = null
            if (result) {
                let { minus_info, amortizeResult } = result || {}
                let { detail_minus_prompt } = minus_info || {}
                let { rebateChange, couponChange, productRebateChange } = amortizeResult || {}
                if (rebateChange || couponChange || productRebateChange) {
                    let title = ''
                    let rebateName = i18n.get("ava.rebate.object.display.in.text.name")
                    let couponName = i18n.get("ava.coupon.object.display.in.text.name")
                    if (rebateChange || productRebateChange) {
                        if (couponChange) {
                            title = i18n.get("ava.rebate.coupon.all.changed.prompt", [{ "moduleA": rebateName, "moduleB": couponName }])
                        } else {
                            title = i18n.get("ava.rebate.coupon.single.changed.prompt", [{ "module": rebateName }])
                        }
                    } else if (couponChange) {
                        title = i18n.get("ava.rebate.coupon.single.changed.prompt", [{ "module": couponName }])
                    }
                    if (title.length > 0) this.pluginService.api.alert(title)
                } else if (validString(detail_minus_prompt)) {
                    this.pluginService.api.alert(detail_minus_prompt)
                } else {
                    // 更新数据到options的object_data和details
                    let mainObjectData = proxy.common.utility.getMainObjectData(this.context.objectDataMap, this.context.mainObjectApiName) || {}
                    let detailDatas = proxy.common.utility.getObjectDatas(this.context.objectDataMap, this.context.detailObjectApiName) || []
                    Object.assign(object_data, mainObjectData)
                    let arr = details && details[this.context.detailObjectApiName]
                    let detail_mapper = proxy.common.mapper.createContextMapper(this.context, this.context.detailObjectApiName)
                    arr && arr.forEach(i => {
                        let index = i.dataIndex
                        let o = (index != null && index != '') ? detailDatas.find(e => e.dataIndex === index) : null
                        if (o && i) {
                            Object.assign(i, o)
                            return
                        }
                        let prod_key = detail_mapper.value(i, 'prod_pkg_key')
                        if (!validString(prod_key)) return
                        o = detailDatas.find(e => detail_mapper.value(e, 'prod_pkg_key') === prod_key)
                        if (o && i) Object.assign(i, o)
                    })
                    // 排序，处理提交到接口的明细顺序
                    if (arr && arr.length > 0 && proxy.rebate_coupon.sortDetailsForCommit) details[this.context.detailObjectApiName] = proxy.rebate_coupon.sortDetailsForCommit(this.context, arr)
                    return
                }
                return { consumed: true }
            }
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            this.context.ui_event_hook = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            let { skipPlugin } = pluginExecResult || {}
            skipPlugin && skipPlugin()
        }
    }



    syncContext(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'sync.context'")
        let { context, dataUpdater, dataGetter } = options
        this.context = context || this.context
        if (dataUpdater) this._updateDataWithContext(this.context, dataUpdater, dataGetter)
    }


    async _store_context(options) {
        let obj = await requireUtils.requireAsync('../objformpkgo2c/package/onsale/plugins/plugin_share')
        let share = obj && obj.getPluginShare()
        share && share.storeContext(this._createContextIdentifier(options && options.dataGetter), this.context)
    }


    async beforeFormRender(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'form.render.before'")
        try {
            await this._store_context(options)
        } catch (error) {
            console.log(`rebate coupon plugin 'form.render.before' require plugin share error: ${stringifyError(error)}`)
            console.error(error)
        }
        return pluginExecResult && pluginExecResult.preData
    }



    // 过滤优惠券产品(目前还没有)和返利产品
    filterShowObjectData({ dataList, recordType }) {
        let proxy = this.getProxy()
        if (!proxy) return
        return dataList && dataList.filter(data => !proxy.rebate_coupon.utility.isCouponProduct(data) && !proxy.rebate_coupon.utility.isRebateProduct(data))
    }





    async beforeMdRender(pluginExecResult, options) {

        console.log("rebate coupon plugin execute 'md.render.before'")

        let { preData } = pluginExecResult

        let proxy = this.getProxy()
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return preData

        let { mdBottomComs, objApiName } = options

        mdBottomComs = preData && preData.mdBottomComs || mdBottomComs

        if (objApiName !== this.context.detailObjectApiName) return { mdBottomComs, refreshUI: {}, filterShowObjectDataList: options.filterShowObjectDataList || [] }

        let rebate_com = mdBottomComs && mdBottomComs.find(com => com.name === 'rebate_coupon_plugin')
        if (rebate_com) return Object.assign({}, preData, { mdBottomComs, refreshUI: {}, filterShowObjectDataList: options.filterShowObjectDataList || [] })


        let list = []

        // 尝试在价格政策控件下面插入返利控件
        mdBottomComs && mdBottomComs.forEach(com => {
            list.push(com)
            if (com.name === 'advanced-pricing-main-object-plugins') {
                rebate_com = { name: 'rebate_coupon_plugin' }
                list.push(rebate_com)
            }
        })

        if (!rebate_com) {
            // 没有找到价格政策控件时，在通用md前插入返利控件
            list = []
            mdBottomComs && mdBottomComs.forEach(com => {
                if (com.name === 'paas_mdcount') {
                    rebate_com = { name: 'rebate_coupon_plugin' }
                    list.push(rebate_com)
                }
                list.push(com)
            })
        }

        // 如果以上均未能插入，则直接追加在最后
        if (!rebate_com) list = (mdBottomComs || []).concat([{ name: 'rebate_coupon_plugin' }])

        let originFilterFunctions = (preData && preData.filterShowObjectDataList) || options.filterShowObjectDataList || []

        return Object.assign({}, preData, { mdBottomComs: list, refreshUI: {}, filterShowObjectDataList: originFilterFunctions.concat([this.filterShowObjectData.bind(this)]) })
    }








    async handleOperationEnd(pluginExecResult, options) {
        let { dataUpdater } = options
        this._updateContext(options)
        dataUpdater.updateMaster({ rebateConditionFieldChanged: true, couponConditionFieldChanged: true })
        let proxy = this.getProxy()
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return
        // 获取返利最大可用额
        if (this.fund_rebate_amount_querier) await this.fund_rebate_amount_querier(this.context)
    }








    async afterChangeField(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'field.edit.after'")

        this._updateContext(options)
        let context = this.context

        let businessConfiguration = context && context.businessConfiguration
        let { coupon, rebate } = businessConfiguration || {}
        if (!coupon && !rebate) return

        let { dataUpdater, objApiName, fieldName } = options
        let proxy = this.getProxy()
        if (!proxy) return
        if (proxy.common.plugin_public.is_data_scarcity(this.context)) return

        let mapper = proxy.common.mapper.createContextMapper(context, context.mainObjectApiName)
        // 判断是否主对象，是否客户字段
        if (objApiName != context.mainObjectApiName || fieldName != mapper.field_name('account_id')) return
        let diff = proxy.rebate_coupon.clearReferenceDatasForMainObject(context) || {}
        dataUpdater.updateMaster(diff)
        options.changeData = Object.assign(options.changeData || {}, diff)
    }








    async changeFieldEnd(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'field.edit.end'")
        this._updateContext(options)
        let context = this.context

        let businessConfiguration = context && context.businessConfiguration
        let { coupon, rebate } = businessConfiguration || {}
        if (!coupon && !rebate) return

        let proxy = this.getProxy()
        if (!proxy) return
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return

        let { dataUpdater, dataGetter, changeData, objApiName, dataIndex } = options
        let { objectDataDiffMap, exist_delete, exist_new_create } = proxy.common.plugin_public.pluginEventOperationContext({ options, context }) || {}
        let mainObjectData = dataGetter.getMasterData()
        if (exist_delete || exist_new_create) {
            mainObjectData.rebateConditionFieldChanged = true
            mainObjectData.couponConditionFieldChanged = true
        } else {
            let res1 = proxy.rebate_coupon.judgeConditionFieldsChanged(context, { [objApiName]: { [dataIndex == null ? "0" : dataIndex]: changeData } })
            let res2 = proxy.rebate_coupon.judgeConditionFieldsChanged(context, objectDataDiffMap)
            mainObjectData.rebateConditionFieldChanged = mainObjectData.rebateConditionFieldChanged || (res1 && res1.rebateConditionFieldChanged) || (res2 && res2.rebateConditionFieldChanged)
            mainObjectData.couponConditionFieldChanged = mainObjectData.couponConditionFieldChanged || (res1 && res1.couponConditionFieldChanged) || (res2 && res2.couponConditionFieldChanged)
        }
        dataUpdater.updateMaster({
            rebateConditionFieldChanged: mainObjectData.rebateConditionFieldChanged,
            couponConditionFieldChanged: mainObjectData.couponConditionFieldChanged
        })

        // 判断是否修改了条件字段，若有修改，则获取返利最大可用额
        if (mainObjectData.rebateConditionFieldChanged) this.fund_rebate_amount_querier && this.fund_rebate_amount_querier()
    }







    async deleteDetailActionEnd(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'md.del.end'")
        return await this.handleOperationEnd(pluginExecResult, options)
    }





    async addDetailActionEnd(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'md.add.end'")
        return await this.handleOperationEnd(pluginExecResult, options)
    }




    async afterCloneDetailAction(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'md.clone.after'")
        let context = this.context
        this._updateContext(options)

        let businessConfiguration = context && context.businessConfiguration
        let { coupon, rebate } = businessConfiguration || {}
        if (!coupon && !rebate) return

        if (!context || !options || options.objApiName !== context.detailObjectApiName) return
        let proxy = this.getProxy()
        if (!proxy) return
        let { preData } = pluginExecResult || {}
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return preData

        let { copyDataIndexs, newDatas, dataUpdater } = options
        // clear rebate and coupon reference datas
        let mapper = proxy.common.mapper.createContextMapper(context, context.detailObjectApiName)
        let prod_pkg_key_count = proxy.common.utility.sumOfIdentifierCount((context.objectDataMap || {})[context.detailObjectApiName], mapper.field_name('prod_pkg_key'))
        newDatas && newDatas.forEach(data => {
            let diff1 = proxy.rebate.core.clearDetailObjectRebateInfo(mapper, data) || {}
            let diff2 = proxy.coupon.core.clearDetailObjectCouponInfo(mapper, data) || {}
            let diff = Object.assign(diff1 || {}, diff2 || {})
            let prod_pkg_key = mapper.value(data, 'prod_pkg_key')
            if (!prod_pkg_key || (prod_pkg_key_count && prod_pkg_key_count[prod_pkg_key] >= 1)) mapper.setValue(diff, 'prod_pkg_key', _.uuid())
            Object.assign(data, diff)
            dataUpdater.updateDetail(options.objApiName, data.dataIndex, diff)
        })
    }




    async cloneDetailActionEnd(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'md.clone.end'")
        return await this.handleOperationEnd(pluginExecResult, options)
    }





    async batchAddDetailActionEnd(pluginExecResult, options) {
        console.log("rebate coupon plugin execute 'md.batchAdd.end'")
        return await this.handleOperationEnd(pluginExecResult, options)
    }




    async bomQueryPriceEnd(pluginExecResult, options) {
        let { preData } = pluginExecResult || {}
        let { triggerType, changedFieldsMap, dataGetter, dataUpdater } = options || {}
        this._updateContext(options)
        if (triggerType !== 'reconfiguration' && triggerType !== 'modifySubProduct') return preData
        let proxy = this.getProxy()
        if (!proxy) return preData
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return preData

        // changedFieldsMap => objectDataDiffMap
        let objectDataDiffMap = {}
        _.each(changedFieldsMap, (map, objectApiName) => {
            let diffMap = (objectDataDiffMap[objectApiName] = objectDataDiffMap[objectApiName] || {})
            _.each(map, (fields, index) => {
                let data = dataGetter.getData(objectApiName, index)
                if (data && fields) diffMap[index] = fields.reduce((res, field_name) => {
                    res[field_name] = data[field_name]
                    return res
                }, {})
            })
        })

        let context = this.context
        let { objectDataMap, mainObjectApiName } = context || {}
        let mainObjectData = proxy.common.utility.getMainObjectData(objectDataMap, mainObjectApiName) || {}
        proxy.rebate_coupon.judgeConditionFieldsChanged(context, objectDataDiffMap)
        dataUpdater && dataUpdater.updateMaster({
            rebateConditionFieldChanged: mainObjectData.rebateConditionFieldChanged,
            couponConditionFieldChanged: mainObjectData.couponConditionFieldChanged
        })

        // 判断是否修改了条件字段，若有修改，则获取返利最大可用额
        if (mainObjectData.rebateConditionFieldChanged) this.fund_rebate_amount_querier && this.fund_rebate_amount_querier()

        return preData
    }


    async gotoSelectCoupon(pluginExecResult, options) {
        let { completeCallback, dataUpdater, dataGetter, formApis } = options || {}
        this._updateContext(options)
        this.context.coupon_hook = {
            customQuery: (async function (parameter, opt) {
                let res = await formApis.npcRun("custom.query.coupon.parameter", { parameter })
                return Object.assign(parameter || {}, res || {})
            }).bind(this),
            customAutoUse: (async function (parameter, opt) {
                let res = await formApis.npcRun("custom.auto.use.coupon.parameter", { parameter })
                return Object.assign(parameter || {}, res || {})
            }).bind(this)
        }
        let res = await requireUtils.requireAsync('../objformmain/dialogset/OTCPopupModule')
        let OTCPopupModule = res && res.default
        let obj = await requireUtils.requireAsync('../objformpkgo2c/package/onsale/rebate_coupon_adapter')
        let rebate_coupon_adapter = obj && obj.default
        OTCPopupModule && OTCPopupModule.show('coupon_select_module', {
            context: this.context,
            coordinator: rebate_coupon_adapter,
            completionHandler: completeCallback
        }, this.context.pageId)
    }





    registerQueryFundRebateMaxAvailableHandler(pluginExecResult, options) {
        let { querier } = options || {}
        if (querier && typeof querier == 'function') this.fund_rebate_amount_querier = querier
    }



    updateReadonlyState(pluginExecResult, options) {
        let { dataUpdater, dataGetter } = options || {}
        if (!dataGetter) return
        let proxy = this.getProxy()
        if (!proxy) return
        let tmp = dataGetter.getDetails()
        let details = tmp && tmp[this.context.detailObjectApiName]
        let mapper = proxy.common.mapper.createContextMapper(this.context, this.context.detailObjectApiName)
        details && details.forEach(data => {
            let is_rebate_product = proxy.rebate_coupon.utility.isRebateProduct(data, mapper.fieldMapping())
            if (!is_rebate_product) return

            /**
             * Details:
             * 设置返利产品不可编辑字段：
             * 返利业务要求：
             * 1. 非周期性返利产品：预置字段 - 结算相关字段 - 周期性相关字段
             * 2. 周期性返利产品：预置字段 - 结算相关字段 - 周期性相关字段 + 期数
             * 周期性产品要求：
             * 1. 非周期性产品只读字段：期数、服务开始时间、服务结束时间、每期结算金额、结算周期、结算期数、结算模式、结算频率
             */

            let is_period_product = proxy.period_product.utility.isPeriodProduct(data, mapper.fieldMapping())
            let fields = [proxy.period_product.standard_fields.PRICING_PERIOD, proxy.period_product.standard_fields.SERVICE_END_TIME]
            if (is_period_product) fields.push(proxy.period_product.standard_fields.SERVICE_START_TIME)
            let obj = {
                biz: 'rebate',
                priority: 11,
                dataIndex: data.dataIndex,
                fieldName: mapper.mappedFields(fields),
                objApiName: this.context.detailObjectApiName,
            }
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly(obj)
        })
    }




    apply() {
        let { rebate, coupon } = this.pluginParam.bizStateConfig || {}
        dhtWeLogService && dhtWeLogService.dhtWxLog && dhtWeLogService.dhtWxLog({
            "source": 'otc-dht-rebate-coupon-plugin',
            "extends_data": { rebate, coupon, action: 'apply' },
            "sub_module": 'object_form',
            "key": 'dht-to-form'
        })        
        fsapi.stat.kLog("sfa", 'rebate_coupon', JSON.stringify({ rebate, coupon, action: 'apply' }))
        if (Boolean(Number(rebate)) === false && Boolean(Number(coupon)) === false) return []
        return [{
            event: 'pluginService.use.after',
            functional: getEventAsyncFuncWithPerformance(this.afterRegisterPlugins.bind(this), 'pluginService.use.after', { subModule: this.pluginParam && this.pluginParam.pluginApiName, biz: 'ava_object_form', module: 'sfa' }),
        }, {
            event: "form.render.before",
            functional: getEventAsyncFuncWithPerformance(this.beforeFormRender.bind(this), 'form.render.before', { subModule: this.pluginParam && this.pluginParam.pluginApiName, biz: 'ava_object_form', module: 'sfa' }),
        }, {
            event: "form.render.end",
            functional: this.handleLogicAfterRenderEnd.bind(this),
        }, {
            event: "md.render.before",
            functional: this.beforeMdRender.bind(this),
        }, {
            event: "form.submit.before",
            functional: this.handleLogicBeforeSubmit.bind(this),
        }, {
            event: "field.edit.after",
            functional: this.afterChangeField.bind(this)
        }, {
            event: "field.edit.end",
            functional: this.changeFieldEnd.bind(this)
        }, {
            event: "md.del.end",
            functional: this.deleteDetailActionEnd.bind(this)
        }, {
            event: "md.add.end",
            functional: this.addDetailActionEnd.bind(this)
        }, {
            event: "md.clone.after",
            functional: this.afterCloneDetailAction.bind(this)
        }, {
            event: "md.clone.end",
            functional: this.cloneDetailActionEnd.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.batchAddDetailActionEnd.bind(this)
        }, {
            // 返利优惠券控件定制 -- 获取context
            event: "rebate.query.context",
            functional: this.buildContext.bind(this),
        }, {
            // 返利优惠券控件定制 -- 获取context
            event: "coupon.query.context",
            functional: this.buildContext.bind(this),
        }, {
            // 返利优惠券控件定制 -- 同步获取context
            event: "rebate.sync.query.context",
            functional: this.syncQueryContext.bind(this),
        }, {
            // 返利优惠券控件定制 -- 同步获取context
            event: "coupon.sync.query.context",
            functional: this.syncQueryContext.bind(this),
        }, {
            // 返利优惠券控件定制 -- 同步更新context
            event: "rebate.coupon.sync.context",
            functional: this.syncContext.bind(this),
        }, {
            // 监听bom取价结束，二次配置bom后，触发相关逻辑
            event: "bom.queryBomPrice.end",
            functional: this.bomQueryPriceEnd.bind(this)
        }, {
            event: "register.need.query.fund.rebate.max.available.handler",
            functional: this.registerQueryFundRebateMaxAvailableHandler.bind(this)
        }, {
            event: "coupon.plugin.select",
            functional: this.gotoSelectCoupon.bind(this)
        }, {
            event: 'rebate.update.readonly.state',
            functional: this.updateReadonlyState.bind(this)
        }]
    }
}