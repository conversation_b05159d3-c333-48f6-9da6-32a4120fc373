/*
 * @Descripttion: 
 * @Author: Lingj
 * @Date: 2020-11-09 
 */
/**
 * 函数式编程相关方法
 */

	/**
	 * compose: 组合函数从右往左顺序执行
	 * 上一个函数执行结果作为下一个执行函数如参
	 */
	const compose = (...fns) => {
		return (result) => {
			let list = [...fns];
			while (list.length > 0) {
				result = list.pop()(result)
			}
			return result;
		}

	}

	/**
	 * pipe: 组合函数从左往右顺序执行
	 * 上一个函数执行结果作为下一个执行函数如参
	 */
	const pipe = (...fns) => {
		return (result) => {
			for (let fn of fns) {
				result = fn(result)
			}
			return result;
		}
	}

	const composeAsync = (...fns) => {
		return async (result) => {
			let list = [...fns];
			while (list.length > 0) {
				result = await list.pop()(result)
			}
			return result;
		}
	}

	const pipeAsync = (...fns) => {
		return async (result) => {
			for (let fn of fns) {
				result = await fn(result)
			}
			return result;
		}
	}

	const partial =
		(fn, ...presetArgs) =>
		(...laterArgs) =>
		fn(...presetArgs, ...laterArgs);

	const mapObj = (mapperFn, o) => {
		let newObj = {},
			keys = Object.keys(o);
		for (let key of keys) {
			newObj[key] = mapperFn(o[key], key)
		}
		return newObj;
	}

	const curry=(arity,fn)=> {
		return (function nextCurried(prevArgs){
			return function curried(nextArg){
				var args = [ ...prevArgs, nextArg ];
	
				if (args.length >= arity) {
					return fn( ...args );
				}
				else {
					return nextCurried( args );
				}
			};
		})( [] );
	}


	export default{
		compose,
		composeAsync,
		pipe,
		pipeAsync,
		partial,
		mapObj,
		curry
	};

