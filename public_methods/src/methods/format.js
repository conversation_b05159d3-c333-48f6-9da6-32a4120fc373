/**
  * @desc:一些处理数据的工具方法
  * @author: wang<PERSON><PERSON>
  * @date: 12/21/21
  */

/**
 *  @desc 对象排序
 *  @param data 对象数组
 *  @param field 排序字段
 *  @param orderby true:倒序； false: 正序
 */
function sortObj(data, field, orderby) {
    var empty = [], notEmpty = [];
    data.forEach(function (item) {
        if (item.hasOwnProperty(field) && hasValue(item[field],true)) {
            notEmpty.push(item)
        } else {
            empty.push(item)
        }
    });
    var afterSort = notEmpty.sort(function (a, b) {
        if (!a.hasOwnProperty(field) || !b.hasOwnProperty(field)) {
            if (!orderby) {
                return -1
            }
            return 1
        }

        if(a[field] === Infinity && b[field] === Infinity){
            if (!orderby) {
                return -1
            }
            return 1
        }

        if (!orderby) {
            if (isNaN(a[field] - b[field])) {
                return a[field].localeCompare(b[field], "zh");
            } else {
                return a[field] - b[field]
            }

        } else {
            if (isNaN(a[field] - b[field])) {
                return b[field].localeCompare(a[field], "zh");
            } else {
                return b[field] - a[field]
            }
        }
    });
    if (orderby) {
        return empty.concat(afterSort)
    }
    return afterSort.concat(empty)
}

/**
 *  @desc 树型数据排序
 *  @param field  根据哪个字段排序
 *  @param orderby  排序顺序  0： 从小到大。 1：从大到小
 */
function sortTreeData(data, field, orderby) {
    data = sortObj.apply(this, arguments);
    data.forEach( function (item) {
        if (item.hasOwnProperty('children')) {
            item.children = sortTreeData(item.children, field, orderby)
        }
    });
    return data
}

function hasValue(data,ignoreInfinity) {
    if(ignoreInfinity){
        return (data || data === 0)
    }
    return (data || data === 0) && Math.abs(data) !== Infinity
}

/**
 * @desc 删除数组中的某个元素
 * @param arr
 * @param children
 */
function deleteArrChildren(arr, children) {
    var newChildren = children;
    if (!Array.isArray(children)) {
        newChildren = [children]
    }
    newChildren.forEach(function (c) {
        var index = arr.findIndex(item => item === c);
        if (index >= 0) arr.splice(index, 1);
    });
    return arr;
}

/**
 * @desc 乘法 精度问题
 * @param arg1
 * @param arg2
 * @returns {number}
 */
function multiplicational(arg1, arg2) {
    return CRM.util.multiplicational(...arguments);
}

/**
 * @desc 除法
 * @param arg1
 * @param arg2
 * @returns {number}
 */
function division(arg1, arg2) {
    return CRM.util.division(...arguments);
}

/**
 * @desc 加法
 * @param arg1
 * @param arg2
 * @returns {number}
 */
function accAdd(arg1, arg2) {
    return CRM.util.accAdd(...arguments);
}

/**
 ** 减法函数，用来得到精确的减法结果
 ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
 ** 调用：accSub(arg1,arg2)
 ** 返回值：arg1减去arg2的精确结果
 **/
function accSub(arg1, arg2) {
    return CRM.util.accSub(...arguments);
}

// 是否整除
function exactDivision (a, b) {
    let r = division(a, b);
    return !r.toString().includes('.');
}

/**
 * @desc 解决toFixed 方法四舍五入不准确问题
 * @param num
 * @param decPlace 小数位
 * @returns {string}
 * @private
 */
function toFixed(num, decPlace = 2){
    return CRM.util.toFixed(...arguments);
}

/**
 * @desc 格式化小数位；
 * @param data
 * @param placeNum 小数位
 * @returns {string}
 */
function formatDecimalPlace (data, placeNum) {
    return CRM.util.formatDecimalPlace(...arguments);
}


export default {
    sortObj,
    sortTreeData,
    hasValue,
    deleteArrChildren,
    multiplicational,
    division,
    accAdd,
    accSub,
    exactDivision,
    toFixed,
    formatDecimalPlace
}