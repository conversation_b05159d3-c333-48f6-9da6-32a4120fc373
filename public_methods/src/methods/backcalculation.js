/**
  * @desc: 反算配置及方法
  * @author: wangshaoh
  * @date: 2/21/22
  */

import Fromat from './format'
const {toFixed} = Fromat;

/**
 * @desc 反算公式
 * @param apiname
 * @returns {*}
 */
function getFormulaConfig(apiname) {
    return {
        SalesOrderObj:[{
            data: 'order_amount',
            pre_expression_key: 'dynamic_amount',
            pre_expression: '$order_amount$-$product_amount$',
            default_value: ['$product_amount$+$dynamic_amount$', '$dynamic_amount$+$product_amount$']
        },{
            data: 'discount',
            pre_expression_key: 'order_amount',
            pre_expression: '$product_amount$*$discount$',
            default_value: ['$order_amount$/$product_amount$']
        }],
        SalesOrderProductObj: [{ 
            data: 'subtotal',
            pre_expression_key: 'sales_price',
            pre_expression: '$subtotal$/$quantity$',
            default_value: ['$sales_price$*$quantity$', '$quantity$*$sales_price$']
        }, {
            data: 'sales_price',
            pre_expression_key: 'discount',
            pre_expression: '$sales_price$/$product_price$',
            pre_expressionMap: {
                '$discount$*$product_price$': '$sales_price$/$product_price$',
                '$product_price$*$discount$': '$sales_price$/$product_price$',

                '$discount$*$price_per_set$': '$sales_price$/$price_per_set$',
                '$price_per_set$*$discount$': '$sales_price$/$price_per_set$',
            },
            default_value: ['$discount$*$product_price$', '$product_price$*$discount$', '($price_per_set$)*$discount$', '$discount$*($price_per_set$)', '$discount$*$price_per_set$', '$price_per_set$*$discount$'],
        }, {
            data: 'subtotal', // 小计
            pre_expression_key:'discount', // 折扣 
            pre_expression: '$subtotal$*$price_book_discount$/$price_book_subtotal$',
            default_value: ['$price_book_subtotal$/$price_book_discount$*$discount$']
        }],
        ReturnedGoodsInvoiceProductObj: [{
            data: 'subtotal',
            pre_expression_key: 'returned_product_price',
            pre_expression: '$subtotal$/$quantity$',
            default_value: ['$returned_product_price$*$quantity$', '$quantity$*$returned_product_price$']
        }],
        QuoteLinesObj: [{
            data: 'sales_price',	// 报价
            pre_expression_key: 'discount',
            pre_expression: '$sales_price$/$price$',
            pre_expressionMap: {
                '$discount$*$price$': '$sales_price$/$price$',
                '$price$*$discount$': '$sales_price$/$price$',

                '$discount$*$price_per_set$': '$sales_price$/$price_per_set$',
                '$price_per_set$*$discount$': '$sales_price$/$price_per_set$',
            },
            default_value: ['$price$*$discount$', '$discount$*$price$', '$discount$*$price_per_set$', '$price_per_set$*$discount$']
        }, {
            data: 'total_amount',		// 报价小计
            pre_expression_key: 'sales_price',
            pre_expression: '$total_amount$/$quantity$',
            default_value: ['$sales_price$*$quantity$', '$quantity$*$sales_price$']

        }, {
            data: 'selling_price',			// 销售单价
            pre_expression_key: 'extra_discount',
            pre_expression: '$selling_price$/$sales_price$', // 报价
            default_value: ['$extra_discount$*$sales_price$', '$sales_price$*$extra_discount$']

        }, {
            data: 'sales_amount',			// 销售金额小计
            pre_expression_key: 'selling_price',
            pre_expression: '$sales_amount$/$quantity$',
            default_value: ['$selling_price$*$quantity$', '$quantity$*$selling_price$']

        }, {
            data: 'extra_discount_amount',	//	额外折扣小计
            pre_expression_key: 'extra_discount',
            pre_expression: '1 - $extra_discount_amount$/$total_amount$',
            default_value: ['$sales_price$*$quantity$-$selling_price$*$quantity$']

        },{
            data: 'extra_discount_amount',	//	额外折扣小计
            pre_expression_key: 'extra_discount',
            pre_expression: '1 - $extra_discount_amount$/($sales_price$*$quantity$)',
            default_value: ['$sales_price$*$quantity$*(1-$extra_discount$)']

        },{
            data: 'total_discount',	//	总折扣
            pre_expression_key: 'selling_price',
            pre_expression: '$total_discount$*$price$',
            default_value: ['$selling_price$/$price$']
        }, {
            data: 'sales_amount', // 销售金额小计
            default_value: ["$total_amount$/$discount$*$total_discount$"],
            pre_expression_key: 'total_discount',
            pre_expression: '$sales_amount$/$total_amount$*$discount$',
        }
        ],
        NewOpportunityLinesObj: [{
            data: 'sales_price',
            pre_expression_key: 'discount',
            pre_expression: '$sales_price$/$price$',
            pre_expressionMap: {
                '$discount$*$price$': '$sales_price$/$price$',
                '$price$*$discount$': '$sales_price$/$price$',

                '$discount$*$price_per_set$': '$sales_price$/$price_per_set$',
                '$price_per_set$*$discount$': '$sales_price$/$price_per_set$',
            },
            default_value: ['$price$*$discount$', '$discount$*$price$', '$discount$*$price_per_set$', '$price_per_set$*$discount$']

        }, {
            data: 'total_amount',
            pre_expression_key: 'sales_price',
            pre_expression: '$total_amount$/$quantity$',
            default_value: ['$sales_price$*$quantity$', '$quantity$*$sales_price$']
            
        }, {
            data: 'total_amount', // 小计
            default_value: ["$price_book_subtotal$/$price_book_discount$*$discount$"],
            pre_expression_key: 'discount',
            pre_expression: '$total_amount$*$price_book_discount$/$price_book_subtotal$'

        }],
        AccountsReceivableDetailObj: [{
            data: 'price_tax_amount', // 【价税合计】变化时，反算【计价数量】
            pre_expression_key: 'ar_quantity',
            pre_expression: '$price_tax_amount$/$tax_price$',
            default_value: ['$ar_quantity$*$tax_price$', '$tax_price$*$ar_quantity$', undefined]
        }],
        SalesInvoiceDetailObj: [{
            data: 'invoice_tax_amount', // 【发票金额】变化时，反算【发票数量】
            pre_expression_key: 'invoice_quantity',
            pre_expression: '$invoice_tax_amount$/$tax_price$',
            default_value: ['$invoice_quantity$*$tax_price$', '$tax_price$*$invoice_quantity$']
        }],
        SaleContractLineObj: [{
            data: 'subtotal',
            pre_expression_key: 'sales_price',
            pre_expression: '$subtotal$/$quantity$',
            default_value: ['$sales_price$*$quantity$', '$quantity$*$sales_price$']
        }, {
            data: 'sales_price',
            pre_expression_key: 'discount',
            pre_expression: '$sales_price$/$product_price$',
            pre_expressionMap: {
                '$discount$*$product_price$': '$sales_price$/$product_price$',
                '$product_price$*$discount$': '$sales_price$/$product_price$',

                '$discount$*$price_per_set$': '$sales_price$/$price_per_set$',
                '$price_per_set$*$discount$': '$sales_price$/$price_per_set$',
            },
            default_value: ['$discount$*$product_price$', '$product_price$*$discount$', '$discount$*$price_per_set$', '$price_per_set$*$discount$']
        }, {
            data: 'subtotal',
            default_value: ["$price_book_subtotal$/$price_book_discount$*$discount$"],
            pre_expression_key: 'discount',
            pre_expression: '$subtotal$/$price_book_subtotal$*$price_book_discount$'

        }],
    }[apiname]
}

/**
 * @desc 反算
 * @param backField 要反算的字段
 * @param exp 公式
 * @param data 数据
 * @param desc 描述
 * @returns {string | number}
 * @private
 */
function doFrontCount(backField = '', exp = '', data = {}, desc = {}) {
    let backFieldDes = desc[backField],
        val = null,
        type = backFieldDes.type;

    exp = exp.replace(/\$[a-zA-Z_0-9]*\$/g, function () {
        var key = arguments[0].replace(/\$/g, '');
        var field = desc[key];
        var fieldType = field ? field.type : 'currency';
        key = data[key];
        if(key === null || key === undefined || key === '') key = 0;
        // key = fieldType == 33 && key != '' && (field.data !== 'discount' || arguments[2].substr(arguments[1] + 10, 4) !== '/100')? key / 100 : key; //特殊逻辑 销售订单的折扣在公式里有可能除以100或者没除100
        key = fieldType == 'percentile' && key != '' ? key / 100 : key;
        if (key || key == 0) {
            return key;
        }
        return fieldType == 1 ? '' : ([2, 3, 35, 36].indexOf(fieldType) != -1 ? 0 : NaN);
    });

    if (type == 'text') {
        val = exp.replace(/&/g, '');
    } else {
        var myEval = eval;
        try {
            val = (myEval(exp) * 1);
            if (Infinity == val || val === -Infinity) { //不合理的计算丢弃，保持原值不动 比如1000/0
                return;
            }
            val = backFieldDes.type == 'percentile' ? val * 100 : val;
            backFieldDes.range = backFieldDes.type == 'percentile' ? [10, !backFieldDes.auto_adapt_places ? 6 : backFieldDes.decimal_places] : [backFieldDes.length || 0, backFieldDes.decimal_places || 0];
            // val = val.toFixed(backFieldDes.range[1]) * 1;
            val = toFixed(val, backFieldDes.range[1]) * 1;
        } catch (e) {
            val = NaN;
        }
    }
    return val;
}



export default {
    getFormulaConfig,
    doFrontCount,

}