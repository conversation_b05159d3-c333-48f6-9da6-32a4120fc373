/**
 * @desc: 公共处理业务方法
 * @author: wang<PERSON><PERSON>
 * @date: 12/21/21
 */
import Fromat from './format'

const { hasValue, exactDivision, division } = Fromat;

/**
 * @desc ajax请求
 * @param request ajax方法
 * @param url 
 * @param param 参数
 * @param field 需要返回结果中的哪个值
 * @returns {PromiseLike<T | never> | Promise<T | never>}
 */
function ajax(request, url, param, field = '') {
    return request({
        url: url,
        data: param,
    }).then(res => {
        if (res.Result.StatusCode == 0) {
            return field ? res.Value[field] : res.Value;
        }
    })
}

// 是否为价格政策赠品；
function isGiveaway(data = {}, fields = {}) {
    let f = Object.assign({}, { is_giveaway: 'is_giveaway', parent_gift_key: 'parent_gift_key' }, fields);

    return !!(data[is_giveaway] == '1' && data[parent_gift_key]);
}

/**
 * @desc 深拷贝
 * @param obj Array || Object
 * @returns {*[]}
 */
function deepClone(obj) {
    //判断拷贝的要进行深拷贝的是数组还是对象，是数组的话进行数组拷贝，对象的话进行对象拷贝
    var objClone = Array.isArray(obj) ? [] : {};
    //进行深拷贝的不能为空，并且是对象或者是
    if (obj && typeof obj === "object") {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (obj[key] && typeof obj[key] === "object") {
                    objClone[key] = deepClone(obj[key]);
                } else {
                    objClone[key] = obj[key];
                }
            }
        }
    }
    return objClone;
}


//=================================迁自 underscore 的底层方法==============================================
window._idCounter = 0;

// 全局自增变量
function uniqueId(prefix) {
    var id = ++window._idCounter + '';
    return prefix ? prefix + id : id;
}

function where(obj, attrs, first) {
    if (isEmpty(attrs)) return first ? null : [];
    return obj[first ? 'find' : 'filter'](value => {
        for (var key in attrs) {
            if (attrs[key] !== value[key]) return false;
        }
        return true;
    })
}

function findWhere(obj, attrs) {
    return where(obj, attrs, true)
}

function getType(data) {
    return Object.prototype.toString.call(data)
}

function isString(data) {
    return getType(data) === '[object String]';
}

function isArray(data) {
    return Array.isArray(data);
}

function isObject(data) {
    return getType(data) === '[object Object]';
}

function isNumber(data) {
    return getType(data) === '[object Number]';
}

function isFunction(data) {
    return getType(data) === '[object Function]';
}

function has(obj, key) {
    return Object.prototype.hasOwnProperty.call(obj, key);
}

function isEmpty(obj) {
    if (obj == null) return true;
    if (Array.isArray(obj) || isString(obj)) return obj.length === 0;
    for (var key in obj) if (has(obj, key)) return false;
    return true;
}

function each(obj, iterator, context) {
    if (obj == null) return;
    if (obj.forEach === Array.prototype.forEach) {
        obj.forEach(iterator, context);
    } else if (obj.length === +obj.length) {
        for (var i = 0, l = obj.length; i < l; i++) {
            if (iterator.call(context, obj[i], i, obj) === {}) return;
        }
    } else {
        for (var key in obj) {
            if (has(obj, key)) {
                if (iterator.call(context, obj[key], key, obj) === {}) return;
            }
        }
    }
}

function eq(a, b, aStack, bStack) {
    // Identical objects are equal. `0 === -0`, but they aren't identical.
    // See the Harmony `egal` proposal: http://wiki.ecmascript.org/doku.php?id=harmony:egal.
    if (a === b) return a !== 0 || 1 / a == 1 / b;
    // A strict comparison is necessary because `null == undefined`.
    if (a == null || b == null) return a === b;
    // Unwrap any wrapped objects.
    // Compare `[[Class]]` names.
    var className = toString.call(a);
    if (className != toString.call(b)) return false;
    switch (className) {
        // Strings, numbers, dates, and booleans are compared by value.
        case '[object String]':
            // Primitives and their corresponding object wrappers are equivalent; thus, `"5"` is
            // equivalent to `new String("5")`.
            return a == String(b);
        case '[object Number]':
            // `NaN`s are equivalent, but non-reflexive. An `egal` comparison is performed for
            // other numeric values.
            return a != +a ? b != +b : (a == 0 ? 1 / a == 1 / b : a == +b);
        case '[object Date]':
        case '[object Boolean]':
            // Coerce dates and booleans to numeric primitive values. Dates are compared by their
            // millisecond representations. Note that invalid dates with millisecond representations
            // of `NaN` are not equivalent.
            return +a == +b;
        // RegExps are compared by their source patterns and flags.
        case '[object RegExp]':
            return a.source == b.source &&
                a.global == b.global &&
                a.multiline == b.multiline &&
                a.ignoreCase == b.ignoreCase;
    }
    if (typeof a != 'object' || typeof b != 'object') return false;
    // Assume equality for cyclic structures. The algorithm for detecting cyclic
    // structures is adapted from ES 5.1 section 15.12.3, abstract operation `JO`.
    var length = aStack.length;
    while (length--) {
        // Linear search. Performance is inversely proportional to the number of
        // unique nested structures.
        if (aStack[length] == a) return bStack[length] == b;
    }
    // Add the first object to the stack of traversed objects.
    aStack.push(a);
    bStack.push(b);
    var size = 0, result = true;
    // Recursively compare objects and arrays.
    if (className == '[object Array]') {
        // Compare array lengths to determine if a deep comparison is necessary.
        size = a.length;
        result = size == b.length;
        if (result) {
            // Deep compare the contents, ignoring non-numeric properties.
            while (size--) {
                if (!(result = eq(a[size], b[size], aStack, bStack))) break;
            }
        }
    } else {
        // Objects with different constructors are not equivalent, but `Object`s
        // from different frames are.
        var aCtor = a.constructor, bCtor = b.constructor;
        if (aCtor !== bCtor && !(isFunction(aCtor) && (aCtor instanceof aCtor) &&
            isFunction(bCtor) && (bCtor instanceof bCtor))) {
            return false;
        }
        // Deep compare objects.
        for (var key in a) {
            if (has(a, key)) {
                // Count the expected number of properties.
                size++;
                // Deep compare each member.
                if (!(result = has(b, key) && eq(a[key], b[key], aStack, bStack))) break;
            }
        }
        // Ensure that both objects contain the same number of properties.
        if (result) {
            for (key in b) {
                if (has(b, key) && !(size--)) break;
            }
            result = !size;
        }
    }
    // Remove the first object from the stack of traversed objects.
    aStack.pop();
    bStack.pop();
    return result;
}

// 是否相等；
function isEqual(a, b) {
    return eq(a, b, [], []);
}

// 找出对应key的值
function pluck(obj, key) {
    return obj.map(function (value) {
        return value[key];
    });
}

function pick(obj) {
    var copy = {};
    var keys = Array.prototype.concat.apply(Array.prototype, Array.prototype.slice.call(arguments, 1));
    each(keys, function (key) {
        if (key in obj) copy[key] = obj[key];
    });
    return copy;
}

// 必须有值
function compact(array) {
    return array.filter(item => !!item);
}

function uniq(array, isSorted, iterator, context) {
    if (isFunction(isSorted)) {
        context = iterator;
        iterator = isSorted;
        isSorted = false;
    }
    var initial = iterator ? array.map(iterator, context) : array;
    var results = [];
    var seen = [];
    initial.forEach(function (value, index) {
        if (isSorted ? (!index || seen[seen.length - 1] !== value) : !seen.includes(value)) {
            seen.push(value);
            results.push(array[index]);
        }
    });
    return results;
}


//================================================================================

//========================================BOM相关方法==============================

/**
 * @desc 获取某个节点的所有子节点
 * @param rowId 哪个节点的子产品
 * @param details 所有从对象数据
 * @param excludeGroup 是否包含分组数据，默认包含
 * @returns []
 */
function getChildren({ rowId = '', details = [], excludeGroup = false } = {}) {
    let res = [];

    function _fn(id) {
        details.forEach(item => {
            if (item.parent_rowId === id) {
                res.push(item);
                _fn(item.rowId)
            }
        })
    }

    if (!rowId) {
        res = details.filter(item => item.parent_rowId || item._isChildren);
    } else {
        _fn(rowId);
    }
    res = uniq(res, item => item.rowId);
    if (excludeGroup) return res.filter(item => !item.isGroup);
    return res;
}

/**
 * @desc 获取某个节点的所有子节点
 * @param rootKey 根节点的虚拟key
 * @param details 所有从对象数据
 * @param excludeGroup 是否包含分组数据，默认包含
 * @returns []
 */
function getChildrenByRootKey({ rootKey = '', details = [], excludeGroup = false, allFields = {} } = {}) {
    let root_prod_pkg_key = allFields && allFields.root_prod_pkg_key || 'root_prod_pkg_key';
    let res = details.filter(item => item[root_prod_pkg_key] == rootKey && item.parent_rowId);
    if (excludeGroup) return res.filter(item => !item.isGroup);
    return res;
}

/**
 * @desc 更新rowId和pid
 * @param data
 */
function updateRowIdAndPid(data) {
    let newData = Array.isArray(data) ? data : [data];

    function _fn(list, pid) {
        list.forEach(function (item) {
            if (!isObject(item)) return;
            item.rowId = uniqueCode();
            if (pid) item.parent_rowId = item.pid = pid;
            if (item.children) {
                _fn(item.children, item.rowId)
            }
        })
    }

    _fn(newData);
    return data
}

// BOM数据组装成tree结构
function parseDataToBOM(data, rootId) {
    if (!data.length) return data;
    let newData = deepClone(data);
    return updateRowIdAndPid(parseDataToTreeForBom(newData, rootId));
}

/**
 * @desc BOM数据组装树结构
 * @param data
 * @param rootId 根结点的product_id
 * @returns {Array}
 */
function parseDataToTreeForBom(data, rootId) {
    let rootData = [];
    let subData = [];

    data.forEach(function (item) {
        if (item.parent_bom_id === rootId && !item.product_group_id) {
            rootData.push(item)
        } else {
            subData.push(item)
        }
    });

    function _findChildren(id, isGroup) {
        return subData.filter(function (item) {
            if (isGroup) {
                return item.product_group_id === id;
            } else {
                if (item.product_group_id) {
                    return
                }
                return item.parent_bom_id === id;
            }
        })
    }

    function _fn(list) {
        list.forEach(function (item) {
            if (!isObject(item)) return;
            let id = item._id;
            let children = _findChildren(id, item.isGroup);
            if (item.new_bom_path) {
                let parentBomPath = item.new_bom_path;
                if (item.isGroup) {
                    parentBomPath = item.new_bom_path.split('.');
                    parentBomPath.pop();
                    parentBomPath = parentBomPath.join('.')
                }
                children = children.filter(c => !c.new_bom_path || c.new_bom_path.includes(parentBomPath));
            }
            if (children.length) {
                item.children = deepClone(children);
                _fn(item.children)
            }
        })
    }

    _fn(rootData);
    return rootData;

}

/**
 * @desc 转化平铺数据为树形结构；每条数据都需要有rowId; 子对象需要有pid，值为主对象的rowId
 * @param data
 * @return {Array}
 */
function parseDataToTree(data = []) {
    let childrenArr = [];
    let root = data.filter(item => {
        if (item.parent_rowId) childrenArr.push(item);
        if (!item.parent_rowId) {
            return item
        }
    });

    function findChildren(parent_rowId) {
        return childrenArr.filter(item => {
            return item.parent_rowId == parent_rowId;
        })
    }

    function toTree(list) {
        list.forEach(item => {
            if (!isObject(item)) return;
            let children = findChildren(item.rowId);
            if (children.length) {
                item.children = children;
                toTree(item.children)
            }
        })
    }

    toTree(root);

    return root;
}

/**
 * @desc 根据bomId查数据
 * @param data
 * @param bomId
 * @param field
 * @returns {*}
 */
function findDataByBomId(data = [], bomId = '', field = '') {
    let res = null;
    let f = field || 'bom_id';

    function _fn(nData) {
        let len = nData.length;
        while (len--) {
            let item = nData[len];
            if (!isObject(item)) return;
            if (item._id === bomId || item[f] === bomId) {
                res = item;
                return;
            }
            if (item.children) {
                _fn(item.children)
            }
        }
    }

    _fn(data);
    return res;
}

/**
 * @desc 循环tree，特殊处理分组
 * @param data Arr
 * @param cb
 */
function forEachTreeData(data = [], cb) {
    let groupPDataList = [];

    function findPData(rowId) {
        return groupPDataList.find(item => item.rowId == rowId);
    }

    function _fn(list, pData) {
        if (!Array.isArray(list)) return;
        list.forEach(item => {
            if (!isObject(item)) return;
            if (item.isGroup && pData) groupPDataList.push(pData);

            // 如果父节点是分组，则加上分组的父节点信息；
            if (pData && pData.isGroup) {
                let groupPData = findPData(pData.parent_rowId); // 如果父级是分组，找分组的父级
                cb && cb(item, pData, groupPData);
            } else {
                cb && cb(item, pData);
            }
            if (item.children) _fn(item.children, item);
        })
    }

    if (data.length) {
        _fn(data)
    }
}

/**
 * @desc 将树型数据转为平铺；
 * @param data
 * @param delChildren 是否需要删除children；
 * @param delGroup 不需要分组；
 * @returns {Array}
 */
function parseTreeToNormal(data = [], delChildren = true, delGroup, isClone = true) {
    let res = [];
    let cloneData = isClone ? deepClone(data) : data;

    function _fn(list) {
        list.forEach(item => {
            if (!isObject(item)) return;
            if (item.children) {
                let c = item.children;
                if (delChildren) delete item.children;
                if ((delGroup && !item.isGroup) || !delGroup) res.push(item);
                _fn(c)
            } else {
                if ((delGroup && !item.isGroup) || !delGroup) res.push(item);
            }
        })
    }

    _fn(cloneData);
    return res;
}

/**
 * @desc 判断是否是BOM，返回结果和产品id
 * @param data
 * @param fields 有需要替换字段
 * @returns {{productId: *, isPackage: *}}
 */
function isBom(data = {}, fields = {}) {
    let obj = Object.assign({
        product_id: 'product_id',
        is_package: 'is_package',
    }, fields);
    let { parent_prod_pkg_key } = obj;
    let isPackage__v = obj.is_package + '__v';

    var res = {
        isPackage: data[obj.is_package],
        productId: data._id,
    };

    if (data.hasOwnProperty(isPackage__v)) {
        res.isPackage = data[isPackage__v];
        res.productId = data[obj.product_id];
    }

    if (data.pricebook_product_id__ro && data.pricebook_product_id__ro.product_id__ro && data.pricebook_product_id__ro.product_id__ro.hasOwnProperty('is_package__v')) {
        res.isPackage = data.pricebook_product_id__ro.product_id__ro.is_package__v;
        res.productId = data.pricebook_product_id__ro.product_id__ro._id;
    }

    if (data.product_id__ro && data.product_id__ro.hasOwnProperty('is_package__v')) {
        res.isPackage = data.product_id__ro.is_package__v;
        res.productId = data.product_id__ro._id;
    }

    if (data.parent_rowId || data.rebate_coupon_id || data.parent_gift_key || data[parent_prod_pkg_key]) res.isPackage = false;

    return res;
}

/**
 * @desc 给数据添加rowId
 * @param data
 * @param update 更新rowId
 */
function addRowId(data, update) {
    var isArray = Array.isArray(data);
    var newData = isArray ? data : [data];

    function _fn(list) {
        list.forEach(item => {
            if (!isObject(item)) return;
            if (!item.hasOwnProperty('rowId') || update) {
                item.rowId = uniqueCode();
            }
            if (item.hasOwnProperty('children')) {
                _fn(item.children)
            }
        })
    }

    _fn(newData)
}

/**
 * @desc 唯一code
 * @returns {*}
 */
function uniqueCode() {
    var code = new Date().getTime() + uniqueId();
    return String(code)
}

/**
 * @desc 校验子产品数量规则； 如果超过最大或最小值，回填最大或最小；如果不符合规则( 数量 = {增减数量幅度}*n)，不回填，给报错红框提示
 * @param data 行数据
 * @param curAmount 当前数量
 * @return {*}
 */
function checkChildrenQuantity({
    data = {}, curAmount = 1, $t, fields = {
        max_amount: 'max_amount',
        min_amount: 'min_amount',
        increment: 'increment'
    }
} = {}) {
    var amount = hasValue(curAmount) ? Number(curAmount) : null;
    var max_amount = hasValue(data[fields['max_amount']]) ? Number(data[fields['max_amount']]) : null;
    var min_amount = hasValue(data[fields['min_amount']]) ? Number(data[fields['min_amount']]) : null;
    var increment = hasValue(data[fields['increment']]) ? Number(data[fields['increment']]) : null;
    var res = {
        msg: '',
        status: true,
    };
    if (!hasValue(amount) || Number(amount) <= 0) {
        res.msg = $t('数量应该大于0');
        res.status = false;
        return res
    }
    if (amount > max_amount && hasValue(amount) && hasValue(max_amount)) {
        res.msg = $t('请输入小于等于{{max}}的数值', {
            max: max_amount
        });
        res.status = false;
        res.type = '1';
        res.max = max_amount;
        return res
    }
    if (amount < min_amount && hasValue(amount) && hasValue(min_amount)) {
        res.msg = $t('请输入大于等于{{min}}的数值', {
            min: min_amount
        });
        res.status = false;
        res.type = '2';
        res.min = min_amount;
        return res
    }
    if (increment && hasValue(amount)) {
        if (!exactDivision(amount, increment)) {
            res.msg = $t('数量应该为增减数量幅度({{increment}}）的整数倍', {
                increment: increment
            }) + '，' + $t('crm.bom.amount.increment', { amount: amount }); // 当前一个产品组合内子件数量为{{amount}}，校验不通过
            res.status = false;
            return res
        }
    }
    return res
}

/**
 * @desc 从对象数据处理，转BOM树；
 * @param data
 * @returns {Array}
 */
function parseTreeDataForMd({ data = [], fields = {}, mdApiName = '' } = {}, param = {}) {
    let { product_group_id, product_id, parent_prod_pkg_key, prod_pkg_key, bom_id, node_type, temp_node_group_id, root_prod_pkg_key } = fields;
    let groupData = [];
    let res = [];

    // 给临时子件补分组信息
    data.forEach(item => {
        if (item[node_type] === 'temp') {
            item[product_group_id] = item[temp_node_group_id + '__r'];
            item[product_group_id + '__v'] = item[temp_node_group_id];
        }
    });

    data.forEach(function (item, index) {
        let up = {};
        // 如果有分组信息，则插入分组信息；
        if (item[product_group_id] && item[parent_prod_pkg_key]) {
            let groupRowId = uniqueCode();
            let findGroup = _.find(groupData, function (c) {
                return c[product_id] === item[product_group_id + '__v'] && item[parent_prod_pkg_key] === c.pid;
            });
            up.rowId = item[prod_pkg_key] || uniqueCode();
            if (findGroup) {
                up.pid = item.pid = findGroup.rowId;
            } else {
                let group = {
                    [product_id]: item[product_group_id + '__v'],
                    [product_id + '__r']: item[product_group_id],
                    rowId: groupRowId,
                    pid: item[parent_prod_pkg_key],
                    parent_rowId: item[parent_prod_pkg_key],
                    [root_prod_pkg_key]: item[root_prod_pkg_key],
                    isGroup: true,
                    isFake: true,
                    groupId: item[product_group_id + '__v'],
                    [bom_id]: item[product_group_id + '__v'],
                    record_type: item.record_type,
                    object_describe_api_name: item.object_describe_api_name,
                    __insertRowId: up.rowId,
                };
                groupData.push(group);
                res.push(group);
                up.pid = item.pid = groupRowId;
            }

            if (item[node_type] === 'temp') {
                up[product_group_id] = item[product_group_id];
                up[product_group_id + '__v'] = item[product_group_id + '__v'];
            }
        } else {
            if (item[parent_prod_pkg_key]) { // 子对象
                up.rowId = item[prod_pkg_key] || uniqueCode();
                up.pid = item.pid = item[parent_prod_pkg_key];
            } else {
                up.rowId = item[prod_pkg_key] || uniqueCode();
            }
        }
        if (item.pid) up.parent_rowId = item.parent_rowId = item.pid;
        if (up.rowId && up.rowId !== item.rowId) {
            if (param && param.dataUpdater) param.dataUpdater.updateRowId(up.rowId, item.rowId);
            item.rowId = up.rowId;
        };
        delete up.rowId;
        if (param && param.dataUpdater) param.dataUpdater.updateDetail(mdApiName, item.rowId, up);
        res.push(item);
    });
    if (groupData.length) {
        if (param && param.dataUpdater) {
            groupData.forEach(g => {
                param.dataUpdater.insert(mdApiName, g.__insertRowId, [g], true);
            })
        }
    };
    return res;
}

/**
 * @desc 从对象数据处理，转BOM树；
 * @param data
 * @returns {Array}
 */
// function parseTreeDataForMd({ data = [], fields = {}, mdApiName = '' } = {}, param) {
//     let { product_group_id, product_id, parent_prod_pkg_key, prod_pkg_key, bom_id } = fields;
//     let groupData = [];
//
//     function _findParent(ppk){
//         return data.find(d => d[prod_pkg_key] === ppk);
//     }
//
//     data.forEach(function (item, index) {
//         let up = {};
//         // 如果有分组信息，则插入分组信息；
//         if (item[product_group_id]) {
//             let groupRowId = uniqueCode();
//             let findGroup = _.find(groupData, function (c) {
//                 return c[product_id] === item[product_group_id + '__v'];
//             });
//             if (findGroup) {
//                 up.parent_rowId = up.pid = item.pid = findGroup.rowId;
//             } else {
//                 let p = _findParent(item[parent_prod_pkg_key]);
//                 let group = {
//                     [product_id]: item[product_group_id + '__v'],
//                     [product_id + '__r']: item[product_group_id],
//                     rowId: groupRowId,
//                     pid: p.rowId,
//                     parent_rowId: p.rowId,
//                     isGroup: true,
//                     isFake: true,
//                     groupId: item[product_group_id + '__v'],
//                     [bom_id]: item[product_group_id + '__v'],
//                     record_type: item.record_type,
//                     object_describe_api_name: item.object_describe_api_name,
//                     trGroupUpIcon: 'crm-md-groupIcon fx-icon-fold2',
//                     trGroupDownIcon: 'crm-md-groupIcon fx-icon-unfold2',
//                 };
//                 groupData.push(group);
//                 up.parent_rowId = up.pid = item.pid = groupRowId;
//             }
//         } else {
//             if (item[parent_prod_pkg_key]) { // 子对象
//                 let p = _findParent(item[parent_prod_pkg_key]);
//                 up.parent_rowId = up.pid = item.pid = p.rowId;
//             }
//         }
//         if (item.pid) up.parent_rowId = item.parent_rowId = item.pid;
//         param.dataUpdater.updateDetail(mdApiName, item.rowId, up);
//     });
//     if (groupData.length) {
//         groupData = param.dataUpdater.add(groupData);
//     }
//     return data.concat(groupData);
// }

/**
     * @desc 给子产品添加单个包的基础数量； 子件的基础数量 = 子件数量 / 子件所属 复用bom 的数量
     * @param data 主子所有数据
     * @param mdApiName
     * @param allFields 字段映射 {quantity, root_prod_pkg_key}
     * @param param
     */
function addDefQuantity({ data = [], mdApiName = '', allFields = {} } = {}, param) {
    let { quantity, root_prod_pkg_key, amount_any, related_core_id, product_id } = allFields;
    let amountAny = amount_any + '__v';

    function _initChildren(children, parentBom) {
        function _fn(cd) {
            for (let i = 0; i < cd.length; i++) {
                let c = cd[i];
                if (c.isGroup) {
                    _fn(c.children || [])
                } else {
                    let dq = c[amountAny] ? c[quantity] : division(c[quantity], parentBom[quantity]);
                    c.defQuantity = dq;
                    param.dataUpdater.updateDetail(mdApiName, c.rowId, {
                        defQuantity: dq,
                        __parentBomRowId: parentBom.rowId,
                    });
                    if (c.children && !c[related_core_id]) _fn(c.children);
                }
            }
        }
        _fn(children);
    }

    data.forEach(item => {
        if (isBom(item, allFields).isPackage) {
            let children = getChildrenByRootKey({ rootKey: item[root_prod_pkg_key], details: data, allFields });
            if (children.length) {
                let allData = [].concat(children).concat([item]);
                let cloneData = CRM.util.cloneBomData(allData, ['rowId', 'parent_rowId', quantity, related_core_id, amountAny, product_id + '__r']);
                CRM.util.forEachTreeData(cloneData, c => {
                    if (c.isGroup) return;
                    if ((c[related_core_id] || !c.parent_rowId && !c.pid) && c.children) {
                        _initChildren(c.children, c)
                    }
                });
            }
        }
    })
}

//合并字符串数组 Array<string>
function mergeArr(arr1, arr2) {
    let arr = [].concat(arr1 || []);
    (arr2 || []).forEach((item) => {
        !arr.includes(item) && arr.push(item);
    });
    return arr;
}
//完全合并两个对象
function mergeObject(oriObj, newObj) {
    for (let key in newObj) {
        oriObj[key] = Object.assign(oriObj[key] || {}, newObj[key])
    }
    return oriObj;
}

/**
 * 合并下列数据结构的对象,对于相同的key，合并并去重它们的数组
 * {
 *     [key:string]:Array<string>
 * }
 */
function mergeArrayObj(obj1 = {}, obj2 = {}) {
    const result = {};
    for (let key of new Set([...Object.keys(obj1), ...Object.keys(obj2)])) {
        result[key] = Array.from(new Set([...(obj1[key] || []), ...(obj2[key] || [])]));
    }
    return result;
}
//使用newObj数据更新oriObj数据
function updateObject(oriObj, newObj) {
    return Object.keys(oriObj).reduce((dataMap, key) => {
        dataMap[key] = Object.assign(oriObj[key], newObj[key] || {});
        return dataMap;
    }, {});
}

//将修改的masterData,detailDataMap数据添加到changeInfo
/*changeInfo:{
    masterUpdate:object,
    mdUpdate:{
        [key:string]:object,
    }
    mdAdd:[],
    mdDel:[]
}*/
function updateChangeInfo(changeInfo, mData = {}, dDataMap = {}) {
    changeInfo.masterUpdate = Object.assign(changeInfo.masterUpdate || {}, mData);
    changeInfo.mdUpdate = mergeObject(changeInfo.mdUpdate || {}, dDataMap);
    return changeInfo;
}
//将修改的masterData,detailDataMap数据生成modifyInfo
function generateModifyInfo(
    masterData = {},
    detailDataMap = {},
    masterApi,
    detailApi
) {
    return {
        "modifyFields": {
            [masterApi]: Object.keys(masterData),
            [detailApi]: collectDataMapFields(detailDataMap)
        },
        "modifyIndex": Object.keys(detailDataMap)
    }
}
//将修改detailDataMap数据提取修改字段
function collectDataMapFields(dDataMap = {}) {
    let dFields = [];
    Object.keys(dDataMap).forEach(key => {
        Object.keys(dDataMap[key] || {}).forEach((f) => {
            !dFields.includes(f) && dFields.push(f);
        });
    });
    return dFields;
}
//根据修改字段，获取要计算的字段
function collectCalFields(
    fieldsDic = {},    //{[api:string]:any},  取自描述
    modifyFields = {},  //{[api:string]:Array<string>},
    noCountObject = [] //不需要统计字段的对象
) {
    let calFieldsObj = {};
    //特殊添加统计字段计算
    const countFields = Object.keys(fieldsDic).reduce((countMap, api) => {
        const fieldsObj = fieldsDic[api];
        if (!noCountObject.includes(api)) {
            countMap[api] = Object.keys(fieldsObj).filter(key => fieldsObj[key].type == 'count') || [];
        }
        return countMap;
    }, {})
    Object.keys(modifyFields).forEach((api, idx) => {
        const items = modifyFields[api].reduce((acc, field) => {
            if (fieldsDic[api][field]) {
                acc[field] = fieldsDic[api][field];
            }
            return acc;
        }, {}),
            data = getCalFields(items);
        if (idx == 0) {
            calFieldsObj = data;
            for (let calApi in countFields) {
                calFieldsObj[calApi] = mergeArr(countFields[calApi], data[calApi] || []);
            }
        } else {
            for (let calApi in data) {
                calFieldsObj[calApi] = mergeArr(calFieldsObj[calApi], data[calApi]);
            }
        }
    });
    return calFieldsObj;
}

function getCalFields(fields, includeDefault) {
    let calObj = {};
    for (let key in fields) {
        const fItem = fields[key],
            calFields = fItem.calculate_relation && fItem.calculate_relation.calculate_fields;
        if (calFields) {
            Object.keys(calFields).forEach((api) => {
                calObj[api] = mergeArr(calObj[api], calFields[api])
            })
        }
        if (includeDefault && fItem.default_is_expression) {
            calObj[fItem.describe_api_name] = calObj[fItem.describe_api_name] || [];
            calObj[fItem.describe_api_name].push(fItem.api_name);
        }
    }
    return calObj;
}
//按小数位格式化数值
function parseNumByDecimal(val, decimal, defaultVal, isToFix) {
    if (parseFloat(val) >= 0) {
        let valStr = val.toString(),
            dotPos = valStr.indexOf(".");
        if (dotPos >= 0) {
            if (isToFix) {
                valStr = Number(valStr).toFixed(decimal);
            } else {
                valStr = valStr.substring(0, dotPos + decimal * 1 + 1 * 1);
            }
        }
        return Number(valStr);
    } else {
        return defaultVal !== null ? defaultVal : val
    }
}

/**
 * @desc 查找数据
 * @param value 值
 * @param data 总数据
 * @param key
 * @param searchAll 查所有；可能不止一条数据符合条件
 * @returns {*}
 */
function getDataByKey(value, data = [], key = 'rowId', searchAll = false) {
    let res = searchAll ? [] : null;
    function _fn(list) {
        let len = list.length;
        while (len--) {
            let item = list[len];
            if (item[key] == value) {
                if (searchAll) {
                    res.push(item);
                } else {
                    res = item;
                    return
                }
            }
            if (item.children && item.children.length) _fn(item.children);
        }
    }
    _fn(data);
    return res;
}

/**
 * 将匹配到的从对象数据放到首条
 * @param {*} details {salesorder: [a, b, ...]}
 * @param {*} param1 {field: 'rowId', value: '112233'}
 * @returns {*} {salesorder: [target, a, b, ...]}
 */
function swapTargetDetailToFirst(details, { field, value }) {
    const result = {};
    _.each(details, (datas = [], objApiName) => {
        let firstItem = null;
        const others = [];
        datas.forEach((data) => {
            if (data[field] === value && !firstItem) {
                firstItem = data;
            } else {
                others.push(data);
            }
        })
        result[objApiName] = firstItem ? [firstItem, ...others] : others;
    })
    return result;
}

/**
 * 价目表字段配置数据范围，相关接口追加从对象details数据，过滤解析原始details
 * @param {*} details {salesorder: [a, b, ...]}
 * @param {*} priceBookDesc 价目表字段描述 {apiname: 'pricebook_id', wheres: []} || true 跳过
 * @param {*} pickTargetApiName 筛选指定对象
 * @param {*} swapToFirstParams 将当前行提升到首位
 * @returns {*} details {salesorder: [a, b, ...]}
 */
function parsePriceBookDataRangeDetails(
    details = {},
    priceBookDesc = null,
    pickTargetApiName = null,
    swapToFirstParams = null,
) {

    // 未设置筛选条件
    // priceBookDesc = true 跳过
    if (typeof priceBookDesc === 'boolean' && priceBookDesc) { }
    else if (!priceBookDesc || !priceBookDesc?.wheres?.length) return void 0;

    const swapTargetDetailToFirst = (details, { field, value }) => {
        const result = {};

        for (const [objApiName, datas] of Object.entries(details)) {
            let firstItem = null;
            const others = [];

            for (const data of datas) {
                if (data[field] === value && !firstItem) {
                    firstItem = data;
                } else {
                    others.push(data);
                }
            }

            result[objApiName] = firstItem ? [firstItem, ...others] : others;
        }

        return result;
    }

    const filterEachDetailData = (details = {}) => {
        const delAttrs = ['product_id__ro', 'children', '_cellStatus'];
        const regex = /(__r|__l)$/;
        const shouldIncludeKey = (key) => !delAttrs.includes(key) && !regex.test(key);

        const filterDatas = (datas) => {
            return datas.map((data) => {
                const nData = {};
                Object.keys(data)
                    .filter(shouldIncludeKey)
                    .forEach((key) => {
                        nData[key] = data[key];
                    });
                return nData;
            });
        }

        const result = {};
        Object.entries(details).forEach(([objApiName, datas]) => {
            result[objApiName] = filterDatas(datas);
        });

        return result;
    }

    const pick = (details, apiName) => {
        const result = {};
        Object.entries(details)
            .filter(([key]) => key === apiName)
            .forEach(([key, value]) => {
                result[key] = value;
            })
        return result;
    }

    let result = details;
    if (pickTargetApiName) {
        result = pick(result, pickTargetApiName);
    }

    if (swapToFirstParams) {
        result = swapTargetDetailToFirst(result, swapToFirstParams);
    }

    result = filterEachDetailData(result);

    return result;
}

/**
 * 价格政策从对象排序
 * 分类数据
 * detailsMap:{
 *     prod_pkg_key:{
 *         insertRowId:
 *         products:Array<detail>
 *         gifts:Array<gift>
 *     }
 * }
 * groupMap: {group_key:Array<prod_pkg_key>}
 * groupGiftsMap:{group_key:Array<gift>}
 */
function sortPricePolicyDetails(details = [], keepUnusualGifts = false) {
    if (!details?.length) return [];
    // 判断是否有价格政策数据
    const hasPricePolicy = details.some(detail => detail.price_policy_id);

    // 如果没有价格政策，仅对返利品排序
    if (!hasPricePolicy) {
        return [
            ...details.filter(detail => detail.rebate_coupon_id),
            ...details.filter(detail => !detail.rebate_coupon_id)
        ];
    }

    // 有价格政策
    let detailsMap = new Map();
    let groupMap = new Map();
    let groupGiftsMap = new Map();
    let sortedDetails = [];
    let checked = new Set();

    //提取返利品
    const rebateItems = details.filter(detail => detail.rebate_coupon_id);
    const rebateKeys = new Set(rebateItems.map(r => r.prod_pkg_key));

    for (let detail of details) {
        const { prod_pkg_key, group_key, parent_gift_key, is_giveaway } = detail;

        if (rebateKeys.has(prod_pkg_key)) continue;

        if (is_giveaway === '1' && parent_gift_key) {  //价格政策赠品

            if (group_key) {
                if (!groupGiftsMap.has(group_key)) {
                    groupGiftsMap.set(group_key, []);
                }
                groupGiftsMap.get(group_key).push(detail);
            } else {
                if (!detailsMap.has(parent_gift_key)) {
                    detailsMap.set(parent_gift_key, { products: [], gifts: [] });
                }
                detailsMap.get(parent_gift_key).gifts.push(detail);
            }
        } else if (!detail.parent_rowId) {
            if (!detailsMap.has(prod_pkg_key)) {
                detailsMap.set(prod_pkg_key, { products: [], gifts: [] });
            }

            const products = detailsMap.get(prod_pkg_key).products;
            products.push(detail, ...getChildrenByRootKey({ rootKey: detail.rowId, details: details }));

            if (group_key) {
                if (!groupMap.has(group_key)) {
                    groupMap.set(group_key, new Set());
                }
                groupMap.get(group_key).add(prod_pkg_key);
            }
        }
    }

    // 返利品
    sortedDetails.push(...rebateItems);

    // 整单赠品
    if (detailsMap.has("master")) {
        sortedDetails.push(
            ...(detailsMap.get("master")?.gifts || [])
        );
    }

    for (let detail of details) {
        const { prod_pkg_key, group_key } = detail;

        if (!checked.has(prod_pkg_key)) {
            checked.add(prod_pkg_key);

            if (group_key && !checked.has(group_key)) {
                checked.add(group_key);
                if (groupMap.has(group_key)) {
                    for (let groupMemberKey of groupMap.get(group_key)) {
                        sortedDetails.push(
                            ...(detailsMap.get(groupMemberKey)?.products || []),
                            ...(detailsMap.get(groupMemberKey)?.gifts || [])

                        );
                    }
                }
                sortedDetails.push(
                    ...(groupGiftsMap.get(group_key) || [])
                );
            } else if (!group_key) {
                sortedDetails.push(
                    ...(detailsMap.get(prod_pkg_key)?.products || []),
                    ...(detailsMap.get(prod_pkg_key)?.gifts || [])
                );
            }
        }
    }

    //保留未找到本品的赠品
    if (keepUnusualGifts && sortedDetails.length !== details.length) {
        const sortedIds = new Set(sortedDetails.map(detail => detail.prod_pkg_key));
        const missingGifts = details.filter(detail => !sortedIds.has(detail.prod_pkg_key));
        sortedDetails.push(...missingGifts);
    }
    return sortedDetails;
}

//==============================================================================

export default {
    ajax,
    deepClone,
    isGiveaway,

    uniqueCode,
    uniqueId,
    findWhere,
    isEmpty,
    each,
    isString,
    isArray,
    isObject,
    isNumber,
    isFunction,
    isEqual,
    pluck,
    pick,
    compact,
    uniq,

    getChildren,
    updateRowIdAndPid,
    parseDataToBOM,
    parseDataToTree,
    findDataByBomId,
    forEachTreeData,
    parseTreeToNormal,
    isBom,
    addRowId,
    checkChildrenQuantity,
    parseTreeDataForMd,
    getChildrenByRootKey,
    addDefQuantity,
    getDataByKey,

    mergeArr,
    mergeObject,
    mergeArrayObj,
    updateObject,
    updateChangeInfo,
    generateModifyInfo,
    collectDataMapFields,
    collectCalFields,
    getCalFields,
    parseNumByDecimal,
    swapTargetDetailToFirst,
    parsePriceBookDataRangeDetails,
    sortPricePolicyDetails
}