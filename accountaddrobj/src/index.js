/**
 * @desc: 客户地址从对象
 * @author: zhongly
 * @date: 2022/4/6
 */

 import PPM from 'plugin_public_methods'
 import Base from 'plugin_base'
 
 export default class AccountAddrObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.fieldMapping = this.getAllFields();
        this.addSyncLocation = true;
        this.isOpenAddress = false;
        let me = this;
        // 获取配置信息——客户地址的主地址与客户的地区定位同步更新
        CRM.util.getConfigValues(['is_open_account_addr_config', 'account_and_addr_sync_upd_location']).then(function(keys) {
            keys.forEach(item => {
                if(item.key == 'is_open_account_addr_config') {
                    me.isOpenAddress = item.value == '1';
                }
                if(item.key == 'account_and_addr_sync_upd_location') {
                    me.addSyncLocation = item.value == '1';
                }
            })
        })
    }

    options(){
        return {
            defMasterFields: {
                'location': 'location', 
                'address': 'address', 
                'country': 'country', 
                'country__r': 'country__r',
                'province': 'province', 
                'province__r': 'province__r',
                'city': 'city', 
                'city__r': 'city__r', 
                'district': 'district', 
                'district__r': 'district__r', 
                'town':'town',
                'town__r':'town__r',
            },
            defMdFields: {
                'contact_way': 'contact_way',
            }
        }
    }

    _mdCopyAfter(plugin, param) {
        param.dataIndex.forEach((id) => {
            let _data = param.dataGetter.getData('', id);
            _data.is_default_add && (_data.is_default_add = false);
            _data.is_ship_to_add && (_data.is_ship_to_add = false);
            _data.is_default && (_data.is_default = false);
        })
    }

    _mdDelBefore(plugin, param) {
        let flag = true;
        param.delDatas.forEach((item) => {
            if (item.is_default_add && !this.isOpenAddress) {
                flag = false;
                plugin.api.alert($t('主地址不允许删除'));
            }
        })
        !flag && plugin.skipPlugin();
    }

    _mdEditAfter(plugin, param) {
        let needResetOtherDefault_add = false, newDefault_addId, needResetOtherShip_add = false, newShip_addId;
        param.dataIndex.forEach((id) => {
            let _data = param.dataGetter.getData('', id);
            if(param.changeData && param.changeData[id] && param.changeData[id].is_ship_to_add) { 
                // 处理默认收货地址唯一
                needResetOtherShip_add = true;
                newShip_addId = id;
            }
            if(param.changeData && param.changeData[id] && param.changeData[id].is_default_add) { 
                // 处理主地址唯一
                needResetOtherDefault_add = true;
                newDefault_addId = id;
                if(this.addSyncLocation) {
                    let fields = ['location', 'address', 'country', 'country__r', 'province', 'province__r', 'city', 'city__r', 'district', 'district__r', 'town', 'town__r'];
                    fields = PPM.pick(this.fieldMapping, fields);
                    fields = Object.values(fields);
                    let changeData = {};
                    // 强制更新地址
                    fields.forEach(field => {
                        changeData[field] = _data[field] || ''
                    })
                    changeData = PPM.pick(changeData, fields)
                    if (PPM.isEmpty(changeData)) return;
                    param.dataUpdater.updateMaster(changeData);
                }
            }else{
                if (!_data.is_default_add) return;
                if(this.addSyncLocation) {
                    let fields = ['location', 'address', 'country', 'country__r', 'province', 'province__r', 'city', 'city__r', 'district', 'district__r', 'town', 'town__r'];
                    fields = PPM.pick(this.fieldMapping, fields);
                    fields = Object.values(fields);
                    let changeData = PPM.pick(param.changeData[id], fields);
                    console.log(changeData, 'changeData-非主地址字段修改');
                    if (PPM.isEmpty(changeData)) return;
                    param.dataUpdater.updateMaster(changeData);
                }
            };
        })
        if(needResetOtherDefault_add && newDefault_addId) {
            // 设置只能有一个客户主地址
            let objApiName = 'AccountAddrObj';
            let details = param.dataGetter.getDetail(objApiName);
            let mainAddr, _changeData = {};
            (details || []).forEach((data) => {
                if (data.is_default_add && data.rowId != newDefault_addId) {
                    // 设置非主地址，保证始终只有一条 主地址
                    _changeData.is_default_add = false;
                    // let fields = ['location', 'address', 'country', 'country__r', 'province', 'province__r', 'city', 'city__r', 'district', 'district__r', 'town', 'town__r'];
                    // fields = PPM.pick(this.fieldMapping, fields);
                    // fields = Object.values(fields);
                    // // 更新地址
                    // fields.forEach(field => {
                    //     if(data[field]) {
                    //         _changeData[field] = data[field]
                    //     }
                    // })
                    mainAddr = data;
                }
            })
            if (!mainAddr) return;
            if (PPM.isEmpty(_changeData)) return;
            param.dataUpdater.updateDetail(objApiName, mainAddr.rowId, _changeData);
        }
        if(needResetOtherShip_add && newShip_addId) {
            // 设置只能有一个默认收货地址
            let objApiName = 'AccountAddrObj';
            let details = param.dataGetter.getDetail(objApiName);
            let mainAddr, _shipChangeData = {};
            (details || []).forEach((data) => {
                if (data.is_ship_to_add && data.rowId != newDefault_addId) {
                    // 设置非默认收货地址，保证始终只有一条 默认收货地址
                    _shipChangeData.is_ship_to_add = false;
                    mainAddr = data;
                }
            })
            if (!mainAddr) return;
            if (PPM.isEmpty(_shipChangeData)) return;
            param.dataUpdater.updateDetail(objApiName, mainAddr.rowId, _shipChangeData);
        }
        
        
    }

    _mdBatchAddBefore(plugin, param) {
        if(param.lookupField.api_name != 'contact_id') return;
        let basicData = param.getRowBasicData();
        let masterData = param.dataGetter.getMasterData();
        basicData.account_id = masterData._id;
        return {
            object_data: basicData,
        }
    }

    _mdBatchAddAfter(plugin, param) {
        if(param.lookupField.api_name != 'contact_id') return;
        param.addDatas.forEach((item, index) => {
            item[this.fieldMapping.contact_way] = param.lookupDatas[index].mobile;
        })
    }

    _formDataChangeAfter(plugin, param) {
        return new Promise(resolve => {
            if(!this.addSyncLocation) {
                return resolve();
            }
            let fields = ['location', 'address', 'country', 'country__r', 'province', 'province__r', 'city', 'city__r', 'district', 'district__r', 'town', 'town__r'];
            let fields2 = ['country', 'province', 'city', 'district', 'town'];
            fields = PPM.pick(this.fieldMapping, fields);
            fields = Object.values(fields);
            let changeData = PPM.pick(param.changeData, fields);
            // 主对象触发从对象 changeData中无法取到省市区的__r， 需要从主对象中获取补充到changeData里
            let keys = _.keys(changeData);
            keys = _.map(_.filter(keys, (item) => {
                return _.contains(fields2, item);
            }), (key) => {
                return key + '__r';
            })
            let masterData = PPM.pick(param.dataGetter.getMasterData(), keys);
            changeData = Object.assign(changeData, masterData);
            if (PPM.isEmpty(changeData)) return resolve();
            // 只查询到 国家/省/市/区/乡镇 code，没查询到对应__r文本，调用接口查询补充（平台省市区新组建，只传code值无法回显）
            CRM.util.fetchAreaParent(changeData.town || changeData.district || changeData.city || changeData.province || changeData.country).then(function(values = []) {
                const _masterAllData = param.dataGetter.getMasterData();
                fields2.forEach(field => {
                    if(_masterAllData[field]) {
                        if(changeData[field]) {
                            let findOption = values.find(option => option.value == changeData[field]);
                            if(findOption) {
                                changeData[field + '__r'] = findOption.label;
                            }
                        }else{
                            let findOption = values.find(option => option.value == _masterAllData[field]);
                            if(findOption) {
                                changeData[field] = findOption.value;
                                changeData[field + '__r'] = findOption.label;
                            }
                        }
                    }
                })
                let objApiName = 'AccountAddrObj';
                let details = param.dataGetter.getDetail(objApiName);
                let mainAddr;
                details.forEach((data) => {
                    if (data.is_default_add) {
                        mainAddr = data;
                    }
                })
                if (!mainAddr) return resolve();
                param.dataUpdater.updateDetail(objApiName, mainAddr.rowId, changeData);
                return resolve();
            })
        })
    }

    getHook() {
        return [
            {
                event: 'md.copy.after',
                functional: this._mdCopyAfter.bind(this)
            }, {
                event: 'md.del.before',
                functional: this._mdDelBefore.bind(this)
            }, {
                event: 'md.edit.after',
                functional: this._mdEditAfter.bind(this)
            }, {
                event: 'md.batchAdd.before',
                functional: this._mdBatchAddBefore.bind(this)
            }, {
                event: 'md.batchAdd.after',
                functional: this._mdBatchAddAfter.bind(this)
            }, {
                event: 'form.dataChange.after',
                functional: this._formDataChangeAfter.bind(this)
            }
        ];
    }
}
 