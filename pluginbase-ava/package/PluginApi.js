/**
 * 提供插件api
 */
export default class PluginApi {

    constructor(pluginService) {
        let {api, run, runSync, executeCommand, executeCommandSync} = pluginService;
        this.api = api;
        this.run = run;
        this.runSync = runSync;
        this.executeCommand = executeCommand;
        this.executeCommandSync = executeCommandSync;
    }

    i18n(value) {
        return this.api && this.api.i18n && this.api.i18n(value);
    }

    showLoading() {
        this.api && this.api.showLoading && this.api.showLoading();
    }

    hideLoading() {
        this.api && this.api.hideLoading && this.api.hideLoading();
    }

    showToast(message) {
        this.api && this.api.showToast && this.api.showToast(message);
    }

    alert(message) {
        this.api && this.api.alert && this.api.alert(message);
    }

    confirm(title, msg, success, cancel) {
        this.api && this.api.confirm && this.api.confirm({title, msg, success, cancel});
    }

    confirmPromise(title, msg) {
        return new Promise(resolve => {
            this.confirm(title, msg,
                () => {
                    resolve(true)
                },
                () => {
                    resolve(false)
                });
        })
    }

    getLocal(key) {
        return this.api && this.api.getLocal && this.api.getLocal(key);
    }

    setLocal(key, value) {
        this.api && this.api.setLocal && this.api.setLocal(key, value);
    }

    openPage(config) {
        this.api && this.api.openPage && this.api.openPage(config);
    }

    getCloudCtrl(key) {
        return this.api && this.api.getCloudCtrl && this.api.getCloudCtrl(key);
    }

    async runPlugin(name, param) {
        let result = await this.run(name, param);
        if (result && result.StatusCode === 0) {
            return result.Value;
        }
    }

    runPluginSync(name, param) {
        let result = this.runSync(name, param);
        if (result && result.StatusCode === 0) {
            return result.Value;
        }
    }

    async executePluginCommand(command, params) {
        let result = await this.executeCommand(command, params);
        if (result && result.StatusCode === 0) {
            return result.Value;
        }
    }

    executePluginCommandSync(command, params) {
        let result = this.executeCommandSync(command, params);
        if (result && result.StatusCode === 0) {
            return result.Value;
        }
    }

    showSingletonLoading(token, params, pageId) {
        let api = this.api;
        let loadingManager = api && api.loadingManager && api.loadingManager(undefined, pageId);
        if (loadingManager && loadingManager.showLoading) {
            let title = params && params.title;
            if (typeof title === 'undefined') {
                title = null;//如果为undefined，需要将title赋值为null，否则ava-ui中loadingdialog组件会默认显示"加载中..."
            }
            return loadingManager.showLoading(token, Object.assign({}, params, {title}), pageId);
        } else {
            return this.showLoading();
        }
    }

    hideSingletonLoading(token, pageId) {
        let api = this.api;
        let loadingManager = api && api.loadingManager && api.loadingManager(undefined, pageId);
        if (loadingManager && loadingManager.hideLoading) {
            return loadingManager.hideLoading(token, pageId);
        } else {
            return this.hideLoading();
        }
    }
}