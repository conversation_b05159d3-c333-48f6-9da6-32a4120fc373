class ObjPluginChecker {

    constructor() {
        let priceService = 'price-service';//取价插件
        //由于推拉单业务有些场景不需要取价逻辑，所以去掉对象插件自检能力
        this.mustPlugins = {
            // SalesOrderObj: [priceService],
            // QuoteObj: [priceService],
            // NewOpportunityObj: [priceService],
            // SaleContractObj: [priceService]
        }
    }

    /**
     * 检查对象使用的插件是否符合预期
     * @param objApiName 对象的apiName
     * @param plugins 对象使用的插件
     * @param ignorePlugins 不参与检查的插件 ['price-service']
     * @return true:检查通过
     */
    check(objApiName, plugins, ignorePlugins) {
        if (!objApiName || !plugins || !plugins.length) {
            return true;
        }
        plugins = plugins.map(it => it && it.__pluginDescribe && it.__pluginDescribe.pluginApiName).filter(it => it);
        let mustPlugins = this.mustPlugins[objApiName];
        if (!mustPlugins || !mustPlugins.length) {
            return true;
        }
        if (ignorePlugins && ignorePlugins.length) {
            mustPlugins = mustPlugins.filter(it => {
                return !ignorePlugins.includes(it);
            })
        }
        return mustPlugins.length ? mustPlugins.every(must => {
            let find = plugins.find(it => it === must);
            if (!find) {
                console.log('pluginCheck miss must plugin: ' + must)
            }
            return !!find;
        }) : true;
    }
}

export default new ObjPluginChecker();