import BaseModule from "ava-ui/fxui/base_module";

export default new class AsyncCommitPopModule extends BaseModule {
    constructor() {
        super()
        this.comName = "TaskProgressModule"
    }

    refresh(options, pageId) {
        let action = 'refresh';
        let instance = this.getInstanceOfPage(pageId);
        if (!instance.listeners[action]) {//组件没有初始化时不需要执行刷新逻辑
            return;
        }
        this.emit({action, options, pageId})
    }

    onRefresh(listener, pageId) {
        this.on({action: "refresh", pageId, listener})
    }
}