import {isEmpty} from "./pluginutils";

/**
 * 获取后台配置
 */
export default class BizStateConfig {

    /**
     * @param bizStateConfig getConfigValue接口返回的结果
     * @param plugins 当前业务适配的所有插件
     */
    constructor(bizStateConfig, plugins) {
        this.bizStateConfig = Object.assign({}, bizStateConfig);
        this.plugins = plugins || [];
    }

    //是否开启了价目表
    isOpenPriceBook() {
        return this.bizStateConfig['28'] == 1;
    }

    //是否开启了可售范围
    isOpenAvailableRange() {
        return this.bizStateConfig['available_range'] == 1;
    }

    //是否强制价目表优先级
    isOpenPriceBookPriority() {
        return this.bizStateConfig['enforce_priority'] == 1;
    }

    getMatchPriceBookValidField(objectApiName) {
        let jsonStr = this.bizStateConfig['match_price_book_valid_field'];
        if (jsonStr && objectApiName) {
            let fieldMap = JSON.parse(jsonStr);
            return fieldMap && fieldMap[objectApiName]
        }
    }

    enableSelectSameProductInDifferentPriceBooks() {
        let isAllowOrderProductCopy = this.isAllowOrderProductCopy();
        let userFilterSelectedProductResult = this.bizStateConfig['whether_filter_order_select_product'];
        let tenantFilterSelectedProductResult = this.bizStateConfig['tenant_whether_filter_order_select_product'] == 0;
        let isFilterSelectedProduct = userFilterSelectedProductResult === '' ? tenantFilterSelectedProductResult : userFilterSelectedProductResult == 0;
        return isAllowOrderProductCopy && !isFilterSelectedProduct;//开启行复制且开启包含本单已选产品时可以选择不同价目表下的同一产品
    }

    isFilterSelectedProducts() {
        let isOpenAttribute = this.isOpenAttribute();
        if (isOpenAttribute) {
            return false;
        }
        let enableSelectSameProduct = this.enableSelectSameProductInDifferentPriceBooks();
        return !enableSelectSameProduct;
    }

    multiSpecDisplayStyle() {
        return this.bizStateConfig['multi_spec_display_style'];
    }

    enableRecentOrder() {
        return this.bizStateConfig['recent_order'] == 1;
    }

    enableInputCustomFields() {
        return this.bizStateConfig['input_custom_fields'] == 1;
    }

    mobileBottomSummarySetting() {
        let value = this.bizStateConfig['mobile_bottom_summary_setting'];
        return value ? JSON.parse(value) : null;
    }

    isOpenPriceBookProductValidPeriod() {
        return this.bizStateConfig['price_book_product_valid_period'] == 1;
    }

    isOpenPriceBookProductTieredPrice() {
        return this.bizStateConfig['price_book_product_tiered_price'] == 1;
    }

    isOrder2QuoteDefaultValueCover() {
        return this.bizStateConfig['order_to_quote_default_value_cover'] == 0;
    }

    isReverseOrderDiscount() {
        let value = this.bizStateConfig['16'];
        let array = value ? value.split(",") : [];
        return array && array.length && array[0] == 1;
    }

    isOpenCpq() {
        let has = this.hasPlugin('bom');
        return has && this.bizStateConfig['cpq'] == 1;
    }

    isOpenSimpleCpq() {
        let has = this.hasPlugin('bom');
        return has && this.bizStateConfig['simple_cpq'] == 1;
    }

    isOpenPricePolicy() {
        return this.hasPlugin('price_policy');
    }

    isOpenAttribute() {
        let has = this.hasPlugin('attribute');
        return has && this.bizStateConfig['is_open_attribute'] == 1;
    }

    isOpenMultiCurrency() {
        return this.bizStateConfig['mccurrency'] == 1;//币种插件会更新该配置
    }

    isOpenMultipleUnit() {
        return this.hasPlugin('multi-unit');
    }

    isOpenCustomerAccount() {
        return this.bizStateConfig['29'] == 1;
    }

    isAllowOrderProductCopy() {
        return this.bizStateConfig['43'] == 1;
    }

    updateBizStateConfig(key, value) {
        if (!isEmpty(key)) {
            this.bizStateConfig = Object.assign({}, this.bizStateConfig, {[key]: value});
        }
    }

    getPlugin(pluginApiName) {
        if (this.plugins && this.plugins.length) {
            return this.plugins.find(item => item.pluginApiName === pluginApiName);
        }
    }

    hasPlugin(pluginApiName) {
        let plugin = this.getPlugin(pluginApiName);
        return !!plugin;
    }

    isOrderMobileEditPageSummarySetting() {
        return this.bizStateConfig['order_mobile_edit_page_summary_setting'] == 1;
    }

    isOpenMultiUnitPriceBook() {
        let status = this.bizStateConfig['multi_unit_price_book'];
        return status == '1' || status === true;
    }

    isOpenTestCalculate() {
        return this.bizStateConfig['is_test_calculate'] == 1;
    }

    isOpenQuoteHistoryPrice() {
        return this.bizStateConfig['quote_history_price'] == 1;
    }

    getMultiUnitConfig() {
        let multiUnitModeConfig = this.bizStateConfig['multi_unit_mode_config'];
        let multiUnitShowType = this.bizStateConfig['multi_unit_show_type'];
        let multiUnitLinkage = this.bizStateConfig['multi_unit_linkage'];
        return {
            modeConfig: multiUnitModeConfig,
            showType: multiUnitShowType,
            linkage: multiUnitLinkage
        }
    }

    //是否支持手工改价
    isOpenManualChangePrice() {
        let change_price_type = this.bizStateConfig['change_price_type'];
        return change_price_type === 'direct';//direct：直接改价，indirect：间接改价
    }

    //过滤母件数据
    isOpenBomDeleteRoot() {
        return this.bizStateConfig['bom_delete_root'] == 1;
    }

    //选产品组合不进入选配配置页
    isNotShowBom(){
        return this.bizStateConfig['not_show_bom'] == 1;
    }

    //整单额外调整，是否分摊
    dynamicAllowAmortize() {
        return this.bizStateConfig['dynamic_allow_amortize'] == 1;
    }

    //是否使用商城分类
    useShopCategory() {
        return this.bizStateConfig['use_shop_category'] == 1;
    }

    //是否启用订单异步化
    asyncCreateOrder() {
        return this.bizStateConfig['async_create_order'] == 1;
    }

    // 是否开启销售合同约束
    isOpenSaleContractConstraint() { return this.bizStateConfig['contract_constraint_mode'] == 1; }
    
    // 回款计划是否与订单绑定
    paymentPlanOrderBindingStatus() {
        return this.bizStateConfig['payment_plan_order_binding_status'] == 1;
    }

    // 获取回款多来源映射配置
    orderPaymentMappingRule() {
        return this.bizStateConfig['order_payment_mapping_rule'];
    }

    // 是否开启回款多来源
    isOpenOrderPaymentMultiSource() {
        return this.bizStateConfig['is_open_order_payment_multi_source'] == 1;
    }

    // 是否开启优惠券
    isOpenCoupon() {
        return this.bizStateConfig['coupon'] == 1;
    }
    
    // 是否开启返利
    isOpenRebate() {
        return this.bizStateConfig['rebate'] == 1;
    }

    // 是否开启快消自动核销逻辑
    accountsReceivableAutoMatchButtonSwitchKey() {
        return this.bizStateConfig['accounts_receivable_auto_match_button_switch_key'] == 2;
    }

    //复制映射是否重新取价开关，转换用的一个，复制自定义用的订单的
    applicablePriceSystem(requestSource, objectApiName) {
        if (requestSource === 'convert') {
            return this.bizStateConfig['get_price_when_convert'] == 1;//转换重新取价
        } else if (requestSource === 'copy') {
            if (isEmpty(objectApiName)) {
                return false;
            }
            let configKey = {
                SalesOrderObj: 'get_price_when_copy',//订单复制重新取价
                QuoteObj: 'get_price_when_copy_quote',//报价单复制重新取价
                SaleContractObj: 'get_price_when_copy_contract',//销售合同复制重新取价
                NewOpportunityObj: 'get_price_when_copy_newopportunity',//商机复制重新取价
            }
            let key = configKey[objectApiName];
            if (isEmpty(key)) {
                key = configKey.SalesOrderObj
            }
            return this.bizStateConfig[key] == 1;
        }
        return false;
    }

    // 获取结算单多来源映射配置
    settlementDetailMappingRule() {
        return this.bizStateConfig['settlement_detail_mapping_rule'];
    }

    // 是否开启产品组合默认最新版本
    isCpqEnabledLatestVersion() {
        return this.bizStateConfig['cpq_enabled_latest_version'] == 1;
    }

    // 是否开启快捷创建应收
    isOpenArQuickAdd() {
        return this.bizStateConfig['is_open_ar_quick_add'] == 1;
    }

    //是否启用周期性产品插件
    enablePeriodicProductPlugin(objectApiName) {
        let value = this.bizStateConfig['periodic_product_plugin'];
        if (isEmpty(objectApiName) || isEmpty(value)) {
            return false;
        }
        let result = JSON.parse(value);
        return result && result.length && result.includes(objectApiName);
    }

    // 是否开启积分
    isOpenSfaLoyaltyPluginSwitchApplySalesOrderObj() {
        return this.bizStateConfig['sfa_loyalty_plugin_switch_apply_SalesOrderObj'] == 'true';
    }

    // 是否开启分层定价
    isOpenStratifiedPricing() {
        return this.bizStateConfig['stratified_pricing'] == 1;
    }
}