import fsapi from 'fs-hera-api'
import {parseFheRst} from "fs-hera-api/api/http/fhepost"
import requireUtils from 'fs-hera-api/api/utils/requireUtils';


export function parseHttpRst(rst, opt) {
    return parseFheRst(rst, opt)
}

export function request(http, {url, data, cacheRule}) {
    if (http) {
        return http({url, data, cacheRule})
            .then(rst => {
                return parseHttpRst(rst);
            }).catch(e => {
                let error = fsapi.util.exceptionCompat(e);
                let content = JSON.stringify({url, error: error || 'unknownError'});
                console.log("sfa", 'serverError', content);
                fsapi.stat.kLog("sfa", 'serverError', content);
                throw error;
            })
    }
    return Promise.reject('Plugin：http can not be null');
}

/**
 * @desc 加法
 */
export function add(arg1, arg2) {
    arg1 = Number(arg1);
    arg2 = Number(arg2);
    let r1, r2, m;
    try {
        r1 = arg1.toString().split(".")[1].length
    } catch (e) {
        r1 = 0
    }
    try {
        r2 = arg2.toString().split(".")[1].length
    } catch (e) {
        r2 = 0
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (multiply(arg1, m) + multiply(arg2, m)) / m
}

/**
 * @desc 乘法
 */
export function multiply(arg1, arg2) {
    arg1 = Number(arg1);
    arg2 = Number(arg2);
    arg1 = arg1 === undefined || arg1 === null ? '' : arg1;
    arg2 = arg2 === undefined || arg2 === null ? '' : arg2;
    let m = 0, s1 = arg1.toString(), s2 = arg2.toString();
    try {
        m += s1.split(".")[1].length
    } catch (e) {
    }
    try {
        m += s2.split(".")[1].length
    } catch (e) {
    }
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)
}

/**
 * @desc 除法
 */
export function divide(arg1, arg2) {
    arg1 = Number(arg1);
    arg2 = Number(arg2);
    if (arg2 == 0) return arg1;
    let t1 = 0, t2 = 0, r1, r2;
    try {
        t1 = arg1.toString().split(".")[1].length
    } catch (e) {
    }
    try {
        t2 = arg2.toString().split(".")[1].length
    } catch (e) {
    }
    r1 = Number(arg1.toString().replace(".", ""));
    r2 = Number(arg2.toString().replace(".", ""));
    let m = t2 - t1;
    return multiply((r1 / r2), Math.pow(10, m).toFixed(Math.abs(m)));
}

/**
 * @desc 是否整数倍
 * @param {*} num 数值
 * @param {*} multiple 倍数
 */
export function is_multiple(num, multiple) {
    let s = divide(num, multiple);
    return !s.toString().includes('.');
}

export function uuid() {
    var s = [];
    var hexDigits = '0123456789abcdef';
    for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-';

    var uuid = s.join('');
    return uuid.replace(new RegExp('-', 'gm'), '');
}

export function isObject(val) {
    return typeof val === 'object' && val != undefined;
}

export function isArray(val) {
    return Object.prototype.toString.call(val) === '[object Array]';
}

export function isEmpty(val) {
    return isObject(val) ? Object.keys(val).length === 0 : (typeof val === "boolean" || typeof val === "number") ? false : !val;
}

export function each(val, fn) {
    return isArray(val) ? val.forEach(fn) : isObject(val) ? Object.keys(val).forEach((key) => {
        fn(val[key], key);
    }) : []
}

export function extend(obj1 = {}, obj2 = {}) {
    obj1 = isObject(obj1) ? obj1 : {};
    obj2 = isObject(obj2) ? obj2 : {};
    return Object.assign(obj1, obj2);
}

export function isSame(a, b) {
    //如果a和b本来就全等
    if (a === b) {
        //判断是否为0和-0
        return a !== 0 || 1 / a === 1 / b;
    }
    //判断是否为null和undefined
    if (a == null || b == null) {
        return a === b;
    }
    //接下来判断a和b的数据类型
    var classNameA = toString.call(a),
        classNameB = toString.call(b);
    //如果数据类型不相等，则返回false
    if (classNameA !== classNameB) {
        return false;
    }
    //如果数据类型相等，再根据不同数据类型分别判断
    switch (classNameA) {
        case '[object RegExp]':
        case '[object String]':
            //进行字符串转换比较
            return '' + a === '' + b;
        case '[object Number]':
            //进行数字转换比较,判断是否为NaN
            if (+a !== +a) {
                return +b !== +b;
            }
            //判断是否为0或-0
            return +a === 0 ? 1 / +a === 1 / b : +a === +b;
        case '[object Date]':
        case '[object Boolean]':
            return +a === +b;
    }
    //如果是对象类型
    if (classNameA == '[object Object]') {
        //获取a和b的属性长度
        var propsA = Object.getOwnPropertyNames(a),
            propsB = Object.getOwnPropertyNames(b);
        if (propsA.length != propsB.length) {
            return false;
        }
        for (var i = 0; i < propsA.length; i++) {
            var propName = propsA[i];
            //如果对应属性对应值不相等，则返回false
            if (a[propName] !== b[propName]) {
                return false;
            }
        }
        return true;
    }
    //如果是数组类型
    if (classNameA == '[object Array]') {
        if (JSON.stringify(a) == JSON.stringify(b)) {
            return true;
        }
        return false;
    }
}

export function cloneDeep(obj) {
    if (obj === null) {
        return null;
    }
    if (obj === undefined) {
        return null;
    }
    if (typeof obj !== 'object') {
        return obj;
    }
    return JSON.parse(JSON.stringify(obj));
}

export function formatDataDecimalPlaces(objectData, objectDescribe, filterFields) {
    let fields = objectDescribe && objectDescribe.fields;
    if (!objectData || !fields) {
        return;
    }
    let fieldTypes = ['currency', 'percentile', 'number'];
    each(objectData, (value, key) => {
        let field = fields[key];
        let filter = filterFields && filterFields.includes(key);
        if (field && !filter) {
            let fieldType = field.type;
            let decimal_places = field.decimal_places;
            if (fieldTypes.includes(fieldType) && decimal_places !== undefined && decimal_places !== null) {
                objectData[key] = formatValueDecimalPlaces(value, field.decimal_places)
            }
        }
    })
}

export function formatValueDecimalPlaces(value, decimalPlaces) {
    if (value === undefined || value === null || value === '') {
        return value
    }
    if (decimalPlaces <= 0) {
        return (Number(+value).toFixed(0) + '').split('.')[0];
    }
    value = Number(+value).toFixed(decimalPlaces);
    return value + ''
}

export function getPercentileFieldDecimalPlaces(field) {
    let {auto_adapt_places = false, decimal_places = 0} = field || {};
    return auto_adapt_places ? decimal_places : 6;
}

export function getPercentileFieldDecimalPlacesFromDescribe(fieldApiName, describe) {
    let field = fieldApiName && describe && describe.fields && describe.fields[fieldApiName];
    return getPercentileFieldDecimalPlaces(field);
}

export function isConvert(sourceAction) {
    return sourceAction && (['Convert', 'convert', 'Mapping', 'mapping'].includes(sourceAction));
}

export function isEdit(sourceAction) {
    return ['Edit', 'edit'].includes(sourceAction);
}

export function isDraft(sourceAction) {
    return ['Draft', 'draft'].includes(sourceAction);
}

export function isClone(sourceAction) {
    return ['Clone', 'clone'].includes(sourceAction);
}

export function equals(a, b) {
    if ((a === undefined || a == null) && (b === undefined || b == null)) {//a、b同时为undefined或null时认为相等
        return true;
    }
    return isSame(a, b);
}

export function getPriceBookParamAndSimplifyDetails(isMasterField, masterObjApiName, detailObjApiName, priceBookFieldName, dataGetter) {
    let describe = dataGetter.getDescribe && dataGetter.getDescribe(isMasterField ? masterObjApiName : detailObjApiName);
    let priceBookField = describe && describe.fields && describe.fields[priceBookFieldName];
    let hasFilters = !!(priceBookField && priceBookField.wheres && priceBookField.wheres.length);
    let detailDatas = dataGetter.getDetail(detailObjApiName);
    return simplifyDetails(hasFilters, {[detailObjApiName]: detailDatas});
}

/**
 * 精简details数据
 * @param hasFilters 是否有过滤条件
 * @param details details数据
 * @return {undefined|*}
 */
export function simplifyDetails(hasFilters, details) {
    if (!hasFilters || isEmpty(details)) {
        return undefined;//返回undefined，请求接口时不加这个入参
    }
    //有过滤条件，对details数据精简
    each(details, (detailDataList, objectApiName) => {
        details[objectApiName] = detailDataList && detailDataList.map(it => {
            let copyData = cloneDeep(it);
            delete copyData.product_id__ro;
            delete copyData.children;
            delete copyData.key_sub_lines;
            each(copyData, (value, key) => {
                delete copyData[key + '__r'];
                delete copyData[key + '__l'];
            })
            return copyData;
        }) || [];
    });
    return details;
}

export function getPriceFieldApiName(detailObjApiName) {
    let objFields = {
        SalesOrderProductObj: 'product_price',
        SaleContractLineObj: 'product_price',
        QuoteLinesObj: 'price',
        NewOpportunityLinesObj: 'price'
    };
    return objFields[detailObjApiName] || 'price';
}

export function getSalesPriceFieldApiName(detailObjApiName) {
    let objFields = {
        SalesOrderProductObj: 'sales_price',
        SaleContractLineObj: 'sales_price',
        QuoteLinesObj: 'sales_price',
        NewOpportunityLinesObj: 'sales_price'
    };
    return objFields[detailObjApiName] || 'sales_price';
}

export function getSubtotalFieldApiName(detailObjApiName) {
    let objFields = {
        SalesOrderProductObj: 'subtotal',
        SaleContractLineObj: 'subtotal',
        QuoteLinesObj: 'total_amount',
        NewOpportunityLinesObj: 'total_amount'
    };
    return objFields[detailObjApiName] || 'subtotal';
}

export function getUnitFieldName(detailObjApiName) {
    let objFields = {
        SalesOrderProductObj: 'unit',
        SaleContractLineObj: 'unit',
        QuoteLinesObj: 'quote_lines_unit',
        NewOpportunityLinesObj: 'new_opporutnity_lines_unit'
    };
    return objFields[detailObjApiName] || 'unit';
}

export function uniq(arr) {
    let res = [];
    let obj = {};
    for (let i = 0; i < arr.length; i++) {
        if (!obj[arr[i]]) {
            obj[arr[i]] = 1;
            res.push(arr[i]);
        }
    }
    return res;
}

export function i18n(key, args) {
    return fsapi.i18n.get(key, args);
}

export function requireAsync(path) {
    return requireUtils.requireAsync(path)
}

export function getEa() {
    let isUpEa = fsapi.config.isUpEa();
    if (isUpEa) {
        return fsapi.config.getUpEaInfoSync().upEa;
    } else {
        return fsapi.config.fsInfo && fsapi.config.fsInfo.accountInfo && fsapi.config.fsInfo.accountInfo.enterpriseAccount;
    }
}