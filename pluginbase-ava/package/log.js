class Log {

    constructor() {
        try {
            const stat = require('fs-hera-api/api/stat/index');
            const jsapi = require('fs-hera-api/api/jsapi/index');
            this.stat = stat.default || stat;
            this.jsapi = jsapi.default || jsapi;
        } catch (e) {
            console.log('require stat module failed');
        }
    }

    tickPluginUsed(pluginDescribe) {
        let {pluginApiName, objectApiName} = pluginDescribe || {};
        this.getAppId(appId => {
            pluginApiName = pluginApiName && pluginApiName.replace && pluginApiName.replace('_', '-') || 'unknownPlugin';
            appId = appId && appId.replace && appId.replace('_', '-') || 'crm';//默认为crm应用
            // console.log('pluginApiName:' + pluginApiName + "," + "appId:" + appId);
            this.sendLog({
                actionid: "crmbiz",
                eventId: 'fs-crm-sfa-plugin-used',
                eventType: 'pv',
                biz: 'FS-CRM',
                module: pluginApiName,
                subModule: appId,//默认为crm应用
                objectApiName,
            });
        })
    }

    sendLog(params) {
        this.stat && this.stat.sendLog && this.stat.sendLog('crmbiz', params);
    }

    tickCPS(pageId, params) {
        this.stat && this.stat.tickCPS && this.stat.tickCPS(pageId, params);
    }

    kLog(type, tag, content) {
        this.stat && this.stat.kLog && this.stat.kLog(type, tag, content);
    }

    getAppId(callback) {
        if (this.jsapi && this.jsapi.getPageTTData) {
            this.jsapi.getPageTTData({
                key: "appId",
                onSuccess: function (rst) {
                    callback && callback(rst && rst.appId)
                },
                onFail: function (err) {
                    callback && callback()
                    console.log('plugin get appid fail err = ' + err)
                }
            });
            return;
        }
        callback && callback()
    }
}

export default new Log();