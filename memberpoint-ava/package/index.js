import _ from 'fs-hera-api/api/utils/util'
import i18n from 'fs-hera-api/api/18n/index'
import requireUtils from 'fs-hera-api/api/utils/requireUtils'

import operator from 'ava-metadata-lib/paasOperators'

import { validString } from 'paas-base-ability/utils/util'
import { stringifyError, isErrorObject } from 'paas-base-ability/utils/error-utility'


export default class MemberPointPlugin {
    constructor(pluginService, pluginParam) {

        this.pluginService = pluginService
        this.pluginParam = pluginParam

        let { params, objectApiName } = pluginParam.describe || {}
        let { fieldMapping } = params || {}
        this.context = {
            mapper: { [objectApiName]: fieldMapping },
            requestId: _.uuid(),
            mainObjectApiName: objectApiName,
            check_point_result: {}
        }
    }


    isNotDht(entrySource) { return !(entrySource === "dht_policy_qorder" || entrySource === "dht_policy_cart") }
    isEdit(sourceAction) { return ['edit', 'editdraft'].indexOf(sourceAction && sourceAction.toLowerCase()) >= 0 }


    _getFieldMapping(fieldName) {
        const mainFieldMapping = this.context.mapper && this.context.mapper[this.context.mainObjectApiName]
        return mainFieldMapping && mainFieldMapping[fieldName] || fieldName
    }


    checkMemberAccount(item) {
        if (item) {
            if (item.cash_exchange === 0
                || item.cash_exchange === null
                || item.cash_exchange === ''
                || item.cash_exchange === undefined) return {
                    error: new Error(i18n.get('ava.member.account.no.cash.exchange.prompt'))
                }
        }
    }

    async checkPointsChange(params) {
        let loyalty = await requireUtils.requireAsync('../../../../otc-base-ability/loyalty/member-point/index')
        return loyalty && loyalty.checkPointsChange(params)
    }


    async _updateAndCalculate(pluginExecResult, options) {

        let { formApis, dataUpdater, dataGetter, params } = options || {}
        if (!formApis) return

        let { check_point_result } = params || {}
        let { loyaltyAmount, pointsDetails, memberAmount, memberTotalAmount } = check_point_result || {}
        this.context.check_point_result = check_point_result || {}
        let mainObjectData = dataGetter.getMasterData()
        let objApiName = this.context.mainObjectApiName
        let main_field_mapping = this.context.mapper && this.context.mapper[this.context.mainObjectApiName]

        let loyalty_amount_field = main_field_mapping && main_field_mapping['loyalty_amount'] || 'loyalty_amount'
        let loyalty_detail_field = main_field_mapping && main_field_mapping['loyalty_detail'] || 'loyalty_detail'

        let diff = {
            [loyalty_detail_field]: pointsDetails ? JSON.stringify(pointsDetails) : null,
            // 自定义的虚拟字段，非真实对象字段
            __member_amount: memberAmount,
            __member_total_amount: memberTotalAmount,
        }
        if (Math.abs(loyaltyAmount) !== Math.abs(mainObjectData[loyalty_amount_field])) diff[loyalty_amount_field] = loyaltyAmount || null

        // 触发计算
        dataUpdater.updateMaster(diff)
        let changeFields = [loyalty_amount_field, loyalty_detail_field]
        try {
            return await formApis.triggerCalAndUIEvent({
                objApiName,
                changeFields,
                filterFields: { [objApiName]: changeFields }
            })
        } catch (error) {
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            return { error }
        }
    }



    async checkPoints(params) {
        let { mainObjectData } = params || {}
        let token = `check_points_${_.uuid()}`
        let loyalty = await requireUtils.requireAsync('../../../../otc-base-ability/loyalty/member-point/index')
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.page_id).showLoading(token, null, this.context.page_id)
        try {
            let res = await loyalty.checkPoints(mainObjectData)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.page_id).hideLoading(token, this.context.page_id)
            return res
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.page_id).hideLoading(token, this.context.page_id)
            
            let str = stringifyError(error)
            if (validString(str)) this.pluginService.api.showToast(str)
            
            if (isErrorObject(error)) return { error }
            // Check if the error is an object, not an Error object, and has a 'FailureCode' property
            if (typeof error === 'object' && Object.hasOwnProperty.call(error, 'FailureCode')) {
                // Create a new Error object using the 'FailureMessage' from the error
                let err = new Error(error.FailureMessage)
                // Assign the 'FailureCode' to the new Error object
                err.code = loyalty.codeToStatus(error.FailureCode)
                // Return the new Error object wrapped in an object
                return { error: err }
            }
            return { error: new Error(str) }
        }
    }



    async requestLoyaltyMemberList(params) {
        let { search_query_info } = params || {}
        let loyalty = await requireUtils.requireAsync('../../../../otc-base-ability/loyalty/member-point/index')
        if (!loyalty) return null
        let token = `loyalty_member_list_${_.uuid()}`
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.page_id).showLoading(token, null, this.context.page_id)
        try {
            let res = await loyalty.queryLoyaltyMemberList({ search_query_info })
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.page_id).hideLoading(token, this.context.page_id)
            res && res.forEach(data => {
                if (data) data.member_account_name = validString(data.display_name) ? data.display_name : data.member_name
            })
            return res
        } catch (error) {
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.page_id).hideLoading(token, this.context.page_id)
        }
    }


    async selectMemberAccounts(pluginExecResult, options) {
        console.log("member point plugin execute 'select.member.accounts'")

        let { params } = options || {}
        let { type, mainObjectData } = params || {}
        let main_field_mapping = this.context.mapper && this.context.mapper[this.context.mainObjectApiName]
        let field_name = type === 'account' ? 'account_id' : 'partner_id'
        let sub_type = type === 'partner' ? 'partner' : 'account'
        field_name = main_field_mapping && main_field_mapping[field_name] || field_name
        if (!validString(mainObjectData[field_name])) {
            let { dataGetter } = options || {}
            let describe = dataGetter && dataGetter.getDescribe(mainObjectData.object_describe_api_name)
            let fields = describe && describe.fields
            let field = fields && fields[field_name]
            this.pluginService.api.showToast(i18n.get('ava.object_form.plese_select_first', [field && field.label]))
            return
        }

        let filters = [{ field_name, field_values: [mainObjectData[field_name]], operator: operator.EQ }, { field_name: 'member_sub_type', field_values: [sub_type], operator: operator.EQ }]
        let values = await Promise.all([requireUtils.requireAsync('../../objformmain/dialogset/OTCPopupModule'), requireUtils.requireAsync('../../objformpkgo2c/package/onsale/plugins/otc_plugin_adapter')])
        let obj = values && values[0]
        let OTCPopupModule = obj && obj.default
        if (!OTCPopupModule) return
        obj = values[1]
        if (!obj) return
        let { only_exec_one_action } = obj
        return await only_exec_one_action((function (obj) {
            let { resolve, render_end } = obj || {}
            OTCPopupModule.show('select_member_account_module', {
                mapper: this.context.mapper,
                ...params,
                filters,
                checkMemberAccount: this.checkMemberAccount.bind(this),
                requestLoyaltyMemberList: this.requestLoyaltyMemberList.bind(this),
                initial_render_end_completion: render_end,
                completionHandler: (function (params) {
                    resolve && resolve(params)
                })
            }, this.context.page_id)
        }).bind(this), 5000)
    }


    async beforeFormRender(pluginExecResult, options) {
        console.log("member point plugin execute 'form.render.before'")
        let { dataGetter } = options || {}
        if (dataGetter) {
            this.context.page_id = dataGetter.getPageId()
            this.context.sourceAction = dataGetter.getSourceAction()
        }
    }


    async afterFormRender(pluginExecResult, options) {
        console.log("member point plugin execute 'form.render.after'")
        let { preData } = pluginExecResult || {}
        let { dataGetter, dataUpdater, triggerCalFields } = options || {}
        let sourceAction = dataGetter && dataGetter.getSourceAction()
        if (this.isEdit(sourceAction)) return preData

        let main_field_mapping = this.context.mapper && this.context.mapper[this.context.mainObjectApiName]
        let loyalty_amount_field = main_field_mapping && main_field_mapping['loyalty_amount'] || 'loyalty_amount'
        let loyalty_detail_field = main_field_mapping && main_field_mapping['loyalty_detail'] || 'loyalty_detail'

        dataUpdater.updateMaster({ [loyalty_amount_field]: 0, [loyalty_detail_field]: null })
        if (triggerCalFields) {
            triggerCalFields.push(loyalty_amount_field)
            triggerCalFields.push(loyalty_detail_field)
        }
    }


    async fieldEditAfter(pluginExecResult, options) {
        console.log("member point plugin execute 'field.edit.after'")
        let { dataUpdater, changeData, objApiName } = options || {}
        if (objApiName !== this.context.mainObjectApiName) return

        let main_field_mapping = this.context.mapper && this.context.mapper[this.context.mainObjectApiName]
        let arr = ['account_id', 'partner_id'].map(i => main_field_mapping && main_field_mapping[i])
        let need_clear = false
        Object.keys(changeData || {}).forEach(f => { need_clear = need_clear || arr.indexOf(f) >= 0 })

        if (!need_clear) return

        // Details: 更换客户或合作伙伴时，清除使用的积分相关数据
        let loyalty_amount_field = main_field_mapping && main_field_mapping['loyalty_amount'] || 'loyalty_amount'
        let loyalty_detail_field = main_field_mapping && main_field_mapping['loyalty_detail'] || 'loyalty_detail'

        changeData[loyalty_amount_field] = 0
        changeData[loyalty_detail_field] = null
        dataUpdater.updateMaster({ [loyalty_amount_field]: 0, [loyalty_detail_field]: null })
    }


    async updateCheckPointResult(pluginExecResult, options) {
        console.log("member point plugin execute 'update.by.check.point.result'")

        /**
         * 1. 更新check_point_result
         * 2. 触发计算triggerCalAndUIEvent
         * 3. 更新后将主对象数据返回
         */

        let result = await this._updateAndCalculate(pluginExecResult, options)
        let { dataGetter } = options || {}
        let mainObjectData = dataGetter && dataGetter.getMasterData()
        return { ...result, mainObjectData: _.cloneDeep(mainObjectData) }
    }


    async confirmChangeMemberPoints(pluginExecResult, options) {
        console.log("member point plugin execute 'confirm.member.points'")
        return await this._updateAndCalculate(pluginExecResult, options)
    }


    async showMemberPointSelector(options, pre_params) {
        let { dataGetter, formApis } = options || {}

        // `sfa_loyalty_plugin_switch_apply_${mainObjectApiName}`           代表开通了销售订单的积分插件
        // `sfa_loyalty_plugin_partner_switch_apply_${mainObjectApiName}`   代表在销售订单中使用积分可以选择合作伙伴
        let key = `sfa_loyalty_plugin_partner_switch_apply_${this.context.mainObjectApiName}`
        let value = this.pluginParam.bizStateConfig && this.pluginParam.bizStateConfig[key]
        // Details: server奇葩开关值不是'1'/'0'而是字符串的true/false
        if (typeof value === 'string') value = value === 'true'
        // Details: 订货通里客户自主下单，不能替伙伴用伙伴的积分
        let partner_member_selectable = Boolean(Number(value)) && this.isNotDht(dataGetter.getEntrySource())

        let obj = await requireUtils.requireAsync('../../objformmain/dialogset/OTCPopupModule')
        let OTCPopupModule = obj && obj.default

        OTCPopupModule && OTCPopupModule.show('change_member_points_module', {
            scene: 'NORMAL',
            mapper: this.context.mapper,
            sourceAction: dataGetter.getSourceAction(),
            mainObjectData: _.cloneDeep(dataGetter.getMasterData()),

            partner_member_selectable,
            account_member_selectable: true,

            confirm: (async function (params) {
                return await formApis.npcRun("confirm.member.points", { params })
            }).bind(this),
            checkPoints: this.checkPoints.bind(this),
            checkPointsChange: this.checkPointsChange.bind(this),
            checkMemberAccount: this.checkMemberAccount.bind(this),
            selectMemberAccounts: (async function (params) {
                return await formApis.npcRun("select.member.accounts", { params })
            }).bind(this),
            requestLoyaltyMemberList: this.requestLoyaltyMemberList.bind(this),
            updateAndCalcAfterCheckPoints: (async function (params) {
                return await formApis.npcRun("update.by.check.point.result", { params })
            }).bind(this),
            ...pre_params
        }, this.context.page_id)
    }




    async changeMemberPoints(pluginExecResult, options) { return await this.showMemberPointSelector(options) }



    _mergePointsDetails(result) {
        let { pointsDetails } = result || {}
        if (!pointsDetails) return result

        let tmp = _.cloneDeep(pointsDetails)
        let { pointsDetails: loyalty_detail } = this.context.check_point_result || {}

        // 1. 将loyalty_detail的基础数据更新到check_point_result
        tmp.forEach(item => {
            if (!item) return
            let origin = loyalty_detail && loyalty_detail.find(i => i.memberId === item.memberId)
            if (origin) Object.assign(item, origin)
        })

        return Object.assign(result, { pointsDetails: tmp })
    }


    async beforeFormSubmit(pluginExecResult, options) {
        let { details, object_data, dataUpdater, dataGetter, formApis } = options || {}
        let res = await this.checkPoints({ mainObjectData: object_data })
        let { loyaltyAmount, pointsDetails, error } = res || {}
        // 非已知场景报错
        if (error && ['NOT_ENABLE_PLAN', 'NOT_EXIST_POINT_POOL', 'INSUFFICIENT_POINTS'].indexOf(error.code) === -1) return { consumed: true }

        let { mapper } = this.context
        let main_field_mapping = mapper && mapper[object_data.object_describe_api_name]
        let loyalty_detail_field = main_field_mapping && main_field_mapping['loyalty_detail'] || 'loyalty_detail'

        let loyalty = await requireUtils.requireAsync('../../../../otc-base-ability/loyalty/member-point/index')
        let change_info = loyalty && loyalty.checkPointsChange({ object_data, mapper, check_point_result: res })
        let { amount_changed, changed_accounts } = change_info || {}
        if (!error && !amount_changed && (changed_accounts || []).length === 0) {
            let diff = { [loyalty_detail_field]: pointsDetails }
            Object.assign(object_data, diff)
            dataUpdater.updateMaster(diff)
            return
        }
        await this.showMemberPointSelector(options, { show_warn_tip: this.isEdit(dataGetter && dataGetter.getSourceAction()), change_info, scene: 'CHECK_BEFORE_SUBMIT', check_point_result: res })
        wx.nextTick((function () {
            // 会员积分使用情况有变化，请查看
            this.pluginService.api.showToast(i18n.get('ava.member.points.changed.prompt')) 
        }).bind(this))
        return { consumed: true }
    }


    apply() {
        return [{
            event: "form.render.before",
            functional: this.beforeFormRender.bind(this),
        }, {
            event: "form.render.after",
            functional: this.afterFormRender.bind(this)
        }, {
            event: "field.edit.after",
            functional: this.fieldEditAfter.bind(this),
        }, {
            event: "form.submit.before",
            functional: this.beforeFormSubmit.bind(this)
            // }, {
            //     event: "custom.item.in.settlement.component",
            //     functional: this.changeFieldEnd.bind(this)
        }, {
            event: "change.member.points",
            functional: this.changeMemberPoints.bind(this)
        }, {
            event: "select.member.accounts",
            functional: this.selectMemberAccounts.bind(this)
        }, {
            event: "confirm.member.points",
            functional: this.confirmChangeMemberPoints.bind(this)
        }, {
            event: 'update.by.check.point.result',
            functional: this.updateCheckPointResult.bind(this)
        }]
    }
}