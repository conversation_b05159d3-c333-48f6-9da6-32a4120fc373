
/Users/<USER>/code/fxiaoke/web/plugins/bom/src/package/add.js
    ，
    【
    】
    【
    】
/Users/<USER>/code/fxiaoke/web/plugins/gantt/src/popup.js
    ：
    ：
/Users/<USER>/code/fxiaoke/web/plugins/message/ava/src/index.js
    名字
    111必须啊不知道22222
    必须啊不知道
    须
    知
    超时2小时未回复
    含有敏感词
    客户
    图片
    名字
    客户
    文件
    客户
    开始聊天前请仔细阅读服务须知事项
    客户需同意存档聊天记录
    服务须知
    名字
    名字
    图片啊
/Users/<USER>/code/fxiaoke/web/plugins/message/web/src/App.vue
    名字
    111必须啊不知道22222
    必须啊不知道
    须
    知
    超时2小时未回复
    含有敏感词
    客户
    图片
    名字
    客户
    文件
    名字
    邀请你加入群聊
    技术支持群，进入可查看详情
    客户
    开始聊天前请仔细阅读服务须知事项
    客户需同意存档聊天记录
    服务须知
    名字
    名字
    图片啊
/Users/<USER>/code/fxiaoke/web/plugins/message/web/src/package/components/fileAttachMsg.vue
    当前环境不支持预览
/Users/<USER>/code/fxiaoke/web/plugins/message/web/src/package/components/imageMsg.vue
    当前环境不支持预览
/Users/<USER>/code/fxiaoke/web/plugins/message/web/src/package/components/videoMsg.vue
    当前环境不支持预览
/Users/<USER>/code/fxiaoke/web/plugins/message/web/src/package/components/voiceMsg.vue
    当前环境不支持播放
/Users/<USER>/code/fxiaoke/web/plugins/multiunit/src/package/MultiUnitApi.js
    MultiUnitApi：http can not be null
    未知异常
/Users/<USER>/code/fxiaoke/web/plugins/pluginbase-ava/package/pluginutils.js
    Plugin：http can not be null
/Users/<USER>/code/fxiaoke/web/plugins/price-policy-ava-plugin/index.js
    发生了意外或网络连接中断，草稿保存失败。
    发生了意外或网络连接中断，将退出当前页面，带来的不便十分抱歉，请上传日志并联系客服
    发生了意外，将尝试保存到草稿箱。
    更换
    将清空已选价目表和产品，确认更换？
    更换
    将清空已选产品，确认更换？
/Users/<USER>/code/fxiaoke/web/plugins/price_policy/src/index.js
    获取价格政策配置失败，需要刷新重试
    当前客户无政策
    。
    ，
/Users/<USER>/code/fxiaoke/web/plugins/priceservice/src/index.js
    【
    】，
/Users/<USER>/code/fxiaoke/web/plugins/priceservice-ava/package/MdBatchAdd.js
    ，
/Users/<USER>/code/fxiaoke/web/plugins/priceservice-ava/package/getprice/Clone.js
    ；
/Users/<USER>/code/fxiaoke/web/plugins/public_methods/src/methods/project.js
    ，
/Users/<USER>/code/fxiaoke/web/plugins/quoter-ava/package/quoter/index.js
    【
    】