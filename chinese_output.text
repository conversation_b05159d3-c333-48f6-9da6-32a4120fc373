/Users/<USER>/mypackage/newworking/fs/plugins/mccurrency-ava/EventHandler.js
                    let message = `更换${fieldLabel}将清空产品，确认更换？`;

/Users/<USER>/mypackage/newworking/fs/plugins/rebate-coupon-ava-plugin/index.js
        不同operateType时有其他参数：

/Users/<USER>/mypackage/newworking/fs/plugins/rebate-coupon-ava-plugin/index.js
                    if (rebateChange) title += '返利单'

/Users/<USER>/mypackage/newworking/fs/plugins/rebate-coupon-ava-plugin/index.js
                    if (couponChange) title += (title.length > 0 ? '和' : '') + '优惠券'

/Users/<USER>/mypackage/newworking/fs/plugins/rebate-coupon-ava-plugin/index.js
                    this.pluginService.api.alert(title + "有变动，请重新选择！")

/Users/<USER>/mypackage/newworking/fs/plugins/multiunit/src/package/handler/FieldChangeAfter.js
                this.multiUnitContext.alert && this.multiUnitContext.alert('多单位产品单位不可为空');

/Users/<USER>/mypackage/newworking/fs/plugins/multiunit/src/package/utils.js
        return isMultiUnit === '是' || isMultiUnit === 'true'

/Users/<USER>/mypackage/newworking/fs/plugins/multiunit/src/package/MultiUnitApi.js
            throw '未知异常'

/Users/<USER>/mypackage/newworking/fs/plugins/rebate/src/package/gift.ts
			info += "没有符合规则配置单位的产品，所以无法选中";

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/getprice/Clone.js
            let tip1 = '当前产品价格为可售范围对应的价目表的最新价格！';

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/getprice/Clone.js
            let tip2 = '当前产品价格为可售范围对应的产品的最新价格！';

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/getprice/Clone.js
            let tip = '组合产品须点击“配置”获取最新配置价格！';

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/getprice/Clone.js
            let tip = `产品：${removedInfo} 未在该客户可售范围中，已移除！`;

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/event/FieldEvent.js
                            let message = `更换${fieldLabel}将清空已选价目表和产品，确认更换？`;

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/event/FieldEvent.js
                    let message = `更换${fieldLabel}，不在新价目表允销范围的产品将会被删除，是否继续?`;

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/event/FieldEvent.js
                    let accountLabel = accountIdField && accountIdField.label || '客户';

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/event/FieldEvent.js
                    this.priceServiceContext.showToast(`请先选择${accountLabel}`);

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/event/MdEvent.js
                this.showToast(`请先选择${priceBookField.label}`);

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/event/MdEvent.js
                this.showToast(`请先选择${accountField.label}`);

/Users/<USER>/mypackage/newworking/fs/plugins/priceservice-ava/PriceServiceApi.js
            throw '未知异常'

/Users/<USER>/mypackage/newworking/fs/plugins/accountaddrobj/src/index.js
                plugin.api.alert('主地址不允许删除');

/Users/<USER>/mypackage/newworking/fs/plugins/price_policy/src/index.js
			let msg = !this.policyConfig ? "获取价格政策配置失败，需要刷新重试" : "当前客户无政策";

