import {isEmpty, uniq, uuid} from "../../pluginbase-ava/package/pluginutils";
import {<PERSON>culateHandler} from "./CalculateHandler";

export class DetailFieldEdit {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context;
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
        this.calculateHandler = new CalculateHandler(context);
    }

    async fieldEditAfter(pluginExecResult, options) {
        let {objApiName, fieldName} = options;
        let {sales_price, subtotal, discount} = this.fieldMapping.getDetailFields(objApiName);
        if ([sales_price, subtotal, discount].includes(fieldName)) {
            return this.calculateDynamicDetail(options);
        }
    }

    async fieldEditEnd(pluginExecResult, options) {
        let {objApiName, dataIndex, changeData, calUiRst} = options;
        let updateData = calUiRst && calUiRst.updateDetails && calUiRst.updateDetails[objApiName] && calUiRst.updateDetails[objApiName][dataIndex];
        let {price_book_price, actual_unit, quantity} = this.fieldMapping.getDetailFields(objApiName);
        let changedFields = Object.keys(Object.assign({}, changeData, updateData));
        let resetDynamicAmount = changedFields.some(it => [price_book_price, actual_unit, quantity].includes(it));
        if (resetDynamicAmount) {
            await this.calculateHandler.resetMasterDetailDynamicAmount(options, [dataIndex]);
        }
    }

    calculateDynamicDetail(options) {
        let {masterObjApiName, objApiName, fieldName, changeData, dataIndex, dataGetter, dataUpdater, formApis} = options;
        let objectData = dataGetter.getData(objApiName, dataIndex);
        let oldValue = objectData && objectData[fieldName];
        let pageId = dataGetter.getPageId();
        let token = 'change_price_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let details = [Object.assign({}, objectData, changeData, {rowId: dataIndex, modified_old_value: oldValue})];
        return this.requestApi.calculateDynamicDetail(masterObjApiName, objApiName, fieldName, details)
            .then(async rst => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                let {master = {}, details} = rst || {};
                let currentResult = details && details.length && details.find(it => it.rowId === dataIndex);
                if (!isEmpty(currentResult)) {//计算从
                    dataUpdater.updateDetail(objApiName, dataIndex, currentResult)
                    let changeFields = uniq(Object.keys(currentResult));
                    await formApis.triggerCalAndUIEvent({
                        objApiName: objApiName,
                        modifiedDataIndexs: [dataIndex],
                        changeFields,
                        filterFields: {[objApiName]: changeFields}
                    });
                }
                if (!isEmpty(master)) {//计算主
                    dataUpdater.updateMaster(master);
                    let changeFields = uniq(Object.keys(master));
                    await formApis.triggerCalAndUIEvent({
                        objApiName: masterObjApiName,
                        changeFields,
                        filterFields: {[masterObjApiName]: changeFields}
                    });
                }
            })
            .catch(err => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                this.pluginApi.showToast(err);
            })
    }
}