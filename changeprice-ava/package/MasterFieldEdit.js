import {isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";

export class MasterFieldEdit {

    constructor(context) {
        let {fieldMapping, pluginApi, requestApi} = context;
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async fieldEditAfter(pluginExecResult, options) {
        let {fieldName, changeData} = options;
        let {discount, order_amount, form_account_id, dynamic_amount} = this.fieldMapping.getMasterFields();
        if (fieldName === form_account_id) {
            Object.assign(changeData, {[dynamic_amount]: 0});
        } else if ([discount, order_amount].includes(fieldName)) {
            await this.calculateDynamicMaster(options);
        }
    }

    async calculateDynamicMaster(options) {
        let {masterObjApiName, fieldName, changeData, dataGetter} = options;
        let pageId = dataGetter.getPageId();
        let token = 'change_price_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let masterData = dataGetter.getMasterData();
        let oldValue = masterData && masterData[fieldName];
        await this.requestApi.calculateDynamicMaster(masterObjApiName, fieldName, Object.assign({}, masterData, changeData, {
            modified_old_value: oldValue
        })).then(rst => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            let updateData = rst && rst.master;
            if (!isEmpty(updateData)) {
                Object.assign(changeData, updateData);
            }
        }).catch(err => {
            this.pluginApi.showToast(err);
            this.pluginApi.hideSingletonLoading(token, pageId);
        });
    }
}