import log from "../../pluginbase-ava/package/log";
import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import ChangePriceApi from "./ChangePriceApi";
import {MasterFieldEdit} from './MasterFieldEdit'
import {DetailFieldEdit} from './DetailFieldEdit'
import {FormRender} from "./FormRender";
import {MdBatchAdd} from "./MdBatchAdd";
import {CalculateHandler} from "./CalculateHandler";

export default class changePrice {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, params, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        let context = {
            fieldMapping: new FieldMapping(params, {
                masterFields: {
                    form_account_id: 'account_id',
                    dynamic_amount: "dynamic_amount",
                    discount: "discount",//quote_discount
                    order_amount: "order_amount",//quote_amount
                    product_amount: "product_amount",//quote_product_sum
                },

                detailFields: {
                    actual_unit: 'actual_unit',
                    dynamic_amount: 'dynamic_amount',
                    price_book_price: 'price_book_price',
                    product_price: 'product_price',
                    quantity: 'quantity',
                    discount: 'discount',//total_discount
                    sales_price: 'sales_price',//selling_price
                    subtotal: 'subtotal',//sales_amount
                }
            }),
            bizStateConfig: this.bizStateConfig,
            pluginApi: new PluginApi(pluginService),
            requestApi: new ChangePriceApi(pluginService.api.request)
        }
        this.masterFieldEdit = new MasterFieldEdit(context);
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.formRender = new FormRender(context);
        this.mdBatchAdd = new MdBatchAdd(context);
        this.calculateHandler = new CalculateHandler(context);
    }

    formRenderBefore(pluginExecResult, options) {
        return this.formRender.formRenderBefore(pluginExecResult, options);
    }

    formRenderEnd(pluginExecResult, options) {
        return this.formRender.formRenderEnd(pluginExecResult, options);
    }

    mdBatchAddAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddAfter(pluginExecResult, options);
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneAfter(pluginExecResult, options);
    }

    mdDelEnd(pluginExecResult, options) {
        return this.mdBatchAdd.mdDelEnd(pluginExecResult, options);
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.fieldEditAfter(pluginExecResult, options);
        } else {
            return this.detailFieldEdit.fieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldEditEnd(pluginExecResult, options);
        }
    }

    bomReconfigurationQueryBomPriceBefore(pluginExecResult, options) {
        return this.calculateHandler.resetMasterDetailDynamicAmount(options, [options.dataIndex]);
    }

    apply() {
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        return isOpenManualChangePrice ? [
            {
                event: 'form.render.before',
                functional: this.formRenderBefore.bind(this)
            },
            {
                event: "form.render.end",
                functional: this.formRenderEnd.bind(this)
            },
            {
                event: "md.batchAdd.after",
                functional: this.mdBatchAddAfter.bind(this)
            },
            {
                event: 'md.clone.after',
                functional: this.mdCloneAfter.bind(this)
            },
            {
                event: 'md.del.end',
                functional: this.mdDelEnd.bind(this)
            },
            {
                event: 'field.edit.after',
                functional: this.fieldEditAfter.bind(this)
            },
            {
                event: 'field.edit.end',
                functional: this.fieldEditEnd.bind(this)
            },
            {
                event: 'bom.reconfiguration.queryBomPrice.before',
                functional: this.bomReconfigurationQueryBomPriceBefore.bind(this)
            }] : [];
    }
}