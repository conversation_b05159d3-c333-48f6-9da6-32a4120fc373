import {request} from "../../pluginbase-ava/package/pluginutils";
import requestIdCreator from "./requestIdCreator";

export default class ChangePriceApi {

    constructor(http) {
        this.http = http;
    }

    calculateDynamicMaster(masterObjectApiName, modifiedField, master) {
        let requestId = requestIdCreator.getRequestId()
        return request(this.http, {
            url: '/FHH/EM1HNCRM/API/v1/object/price_policy/service/calculateDynamicMaster',
            data: {
                requestId,
                masterObjectApiName,
                modifiedField,
                master,
            }
        }).then(rst => {
            return rst;
        });
    }

    calculateDynamicDetail(masterObjectApiName, detailObjectApiName, modifiedField, details) {
        let requestId = requestIdCreator.getRequestId();
        return request(this.http, {
            url: '/FHH/EM1HNCRM/API/v1/object/price_policy/service/calculateDynamicDetail',
            data: {
                requestId,
                masterObjectApiName,
                detailObjectApiName,
                modifiedField,
                details,
            }
        }).then(rst => {
            return rst;
        });
    }
}