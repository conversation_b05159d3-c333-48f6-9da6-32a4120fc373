import {CalculateHand<PERSON>} from "./CalculateHandler";

export class MdBatchAdd {

    constructor(context) {
        let {fieldMapping} = context || {};
        this.fieldMapping = fieldMapping;
        this.calculateHandler = new CalculateHandler(context);
    }

    async mdBatchAddAfter(pluginExecResult, options) {
        await this.calculateHandler.resetMasterDynamicAmount(options);
    }

    async mdCloneAfter(pluginExecResult, options) {
        await this.calculateHandler.resetMasterDynamicAmount(options);
        //将新增的从对象的额外调整设置为0
        let {objApiName, newDatas} = options || {};
        let {dynamic_amount} = this.fieldMapping.getDetailFields(objApiName);
        newDatas && newDatas.length && newDatas.forEach(detailData => {
            Object.assign(detailData, {[dynamic_amount]: 0});
        })
        let preData = pluginExecResult && pluginExecResult.preData;
        let preObjectFilterFields = preData
            && preData.extraCalUiParams
            && preData.extraCalUiParams.filterFields
            && preData.extraCalUiParams.filterFields[objApiName];
        return Object.assign({}, preData, {
            extraCalUiParams: Object.assign({}, preData && preData.extraCalUiParams, {
                filterFields: Object.assign({}, preData && preData.extraCalUiParams && preData.extraCalUiParams.filterFields,
                    {[objApiName]: [...(preObjectFilterFields || []), dynamic_amount]}),
            })
        });
    }

    async mdDelEnd(pluginExecResult, options) {
        await this.calculateHandler.resetMasterDynamicAmount(options);
    }
}