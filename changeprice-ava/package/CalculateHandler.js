import {isEmpty} from "../../pluginbase-ava/package/pluginutils";

export class CalculateHandler {

    constructor(context) {
        let {fieldMapping} = context;
        this.fieldMapping = fieldMapping;
    }

    async resetMasterDetailDynamicAmount(options, dataIndexList) {
        await this.resetDetailDynamicAmount(options, dataIndexList)
        await this.resetMasterDynamicAmount(options);
    }

    async resetMasterDynamicAmount(options) {
        let {dynamic_amount} = this.fieldMapping.getMasterFields();
        let {masterObjApiName, dataGetter, dataUpdater, formApis} = options || {};
        let masterData = dataGetter.getMasterData();
        let orgValue = masterData && masterData[dynamic_amount];
        if (orgValue != 0) {
            dataUpdater.updateMaster({[dynamic_amount]: 0});
            await formApis.triggerCalAndUIEvent({
                objApiName: masterObjApiName,
                changeFields: [dynamic_amount]
            });
        }
    }

    async resetDetailDynamicAmount(options, dataIndexList) {
        if (!dataIndexList || !dataIndexList.length) {
            return;
        }
        let modifiedDataIndexs = [];
        let {objApiName, dataGetter, dataUpdater, formApis} = options;
        objApiName = isEmpty(objApiName) ? this.fieldMapping.getFirstDetailObjApiName() : objApiName;
        let {dynamic_amount} = this.fieldMapping.getDetailFields(objApiName);
        let detailDataList = dataGetter.getDetail(objApiName);
        detailDataList && detailDataList.length && detailDataList.forEach(detailData => {
            let {dataIndex, [dynamic_amount]: orgValue} = detailData;
            if (dataIndexList.includes(dataIndex) && (orgValue != 0)) {
                modifiedDataIndexs.push(dataIndex);
                dataUpdater.updateDetail(objApiName, dataIndex, {[dynamic_amount]: 0});
            }
        })
        if (modifiedDataIndexs.length) {
            await formApis.triggerCalAndUIEvent({
                objApiName: objApiName,
                modifiedDataIndexs: modifiedDataIndexs,
                changeFields: [dynamic_amount]
            });
        }
    }
}