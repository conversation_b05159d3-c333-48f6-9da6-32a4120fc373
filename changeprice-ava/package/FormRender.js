import requestIdCreator from "./requestIdCreator";
import {<PERSON><PERSON>Hand<PERSON>} from "./CalculateHandler";
import {isEdit} from "../../pluginbase-ava/package/pluginutils";

export class FormRender {

    constructor(context) {
        let {fieldMapping} = context;
        this.fieldMapping = fieldMapping;
        this.calculateHandler = new CalculateHandler(context);
    }

    formRenderBefore(pluginExecResult, options) {
        requestIdCreator.initRequestId();
    }

    formRenderEnd(pluginExecResult, options) {
        //价格政策插件会处理额外调整逻辑
        // let {dataGetter} = options;
        // let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        // if (!sourceAction || isEdit(sourceAction)) {
        //     return;
        // }
        // let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        // let detailDataList = dataGetter.getDetail(objApiName);
        // let dataIndexList = detailDataList && detailDataList.length && detailDataList.map(it => it.dataIndex);
        // return this.calculateHandler.resetMasterDetailDynamicAmount(Object.assign({}, options, {objApiName}), dataIndexList);
    }
}