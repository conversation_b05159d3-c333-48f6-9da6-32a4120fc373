---
description: 检查漏洞、cr、检查方法漏洞
globs: 
alwaysApply: false
---
## 检查代码漏洞规则：

1. 首先读取、理解代码注释
   - 自动读取方法上一行的注释
   - 理解方法的目的和使用场景
   - 注意注释中提到的触发条件和前置条件
   - 特别关注"只有"、"必须"等限定词
   - 特别关注“编辑”场景，必须要有编辑字段判断

2. 优先根据注释检查方法在场景上是否完善
   - 检查代码是否实现了注释中描述的所有条件判断
   - 检查是否缺少必要的前置验证
   - 对于条件触发的方法，必须验证触发条件是否得到检查
   - 反问自己是否理解了代码注释

3. 检查方法实现是否完整
   - 代码逻辑必须覆盖注释中描述的所有场景
   - 注释中的限定条件必须在代码中有对应的验证逻辑

## 工作流程规范：

1. 收到用户请求后，第一步必须获取并理解相关规则：
   - 使用 fetch_rules 获取相关规则
   - 仔细阅读和理解规则内容
   - 将规则作为后续工作的指导方针

2. 制定符合规则的工作计划：
   - 根据规则要求设计检查/实现步骤
   - 确保计划中的每个步骤都对应规则要求
   - 在计划中标注规则对应点
 

3. 严格按规则执行：
   - 每个步骤都要对照规则进行
   - 保持规则意识，不偏离规则框架
   - 确保输出符合规则要求

4. 输出结果时的自查：
   - 检查是否完整执行了规则要求
   - 验证输出是否符合规则标准
   - 确保建议或改进方案都基于规则

## 可用指令：

Cursor rules are user provided instructions for the AI to follow to help work with the codebase.
They may or may not be relevent to the task at hand. If they are, use the fetch_rules tool to fetch the full rule.
Some rules may be automatically attached to the conversation if the user attaches a file that matches the rule's glob, and wont need to be fetched.

check: 检查漏洞、cr
test: 单元测试规则