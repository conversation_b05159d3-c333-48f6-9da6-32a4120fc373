import os
import re
import io
from os import path
from turtle import Turtle

url = './'
outputUrl = './chinese_output.text';
reg = re.compile(u".*[\$t\(\"crm\.)|\$t\(\'crm\.)]{0,}[\u4e00-\u9fa5]+.*[\"\)|\'\)]{0,}")

# 检查是否包含特殊字符
def checkStr (str):
    flag = False;

    # 包含特殊字符
    blackList = [
        '$t(',
        '//',
        '<!--',
        '-->',
        '/*',
        '@param',
        'i18n(',
        'console.log(',
        'console.error(',
    ];

    # 以特殊字符开始
    beginWithBlackList = [
        '*'
    ]

    for item in blackList:
        if str.find(item) != -1:
            flag = True

    if not flag:
        _str = str.strip();

        for item in beginWithBlackList:
            if _str.startswith(item):
                flag = True
                break;

    return flag

# 检查是否属于需要查找中文的文件
def checkFile(file) :
    flag = True;

    # 不用查找中文的文件路径
    fileBlackList = [
        # plugins 不用查找

        'dist',
        'node_modules',
        ''
    ]

    # 只查找以下扩展名文件
    extWhitList = [
        '.js',
        '.ts',
        '.html',
        '.vue'
    ];

    for item in fileBlackList:
        if file.find(item) != -1:
            flag = False

    if flag:
        flag = False

        for item in extWhitList:
            if file.endswith(item):
                flag = True
                break

    return flag

# 查找中文
def scannerFile (url):
    file = os.listdir(url)

    for f in file:

        real_url = path.join(url, f)

        # 不在黑名单中的文件才查找中文
        if path.isfile(real_url):
            if checkFile(real_url):
                file_path = path.abspath(real_url)
                print(real_url)
                try:
                    with io.open(file_path,encoding="utf-8") as file_obj:
                        content = file_obj.read()
                        res = re.findall(reg, content)
                        if res:
                            for r in res:
                                # 不包含特殊字符的中文才记录输出
                                if not checkStr(r):
                                    print(real_url);
                                    outFile.write(file_path + '\n' + r + '\n\n')
                except Exception as instance:
                    print(type(instance))
                    print(real_url)

        elif path.isdir(real_url):
            scannerFile(real_url)

        else:
            pass

outFile = open(outputUrl, 'w')
scannerFile(url)
