import _ from 'fs-hera-api/api/utils/util'
import fsapi from 'fs-hera-api'
import requireUtils from 'fs-hera-api/api/utils/requireUtils'


import { validString, clone } from "paas-base-ability/utils/util"
import { buildDiffMap, objectMergeWithMaxDeep } from 'paas-base-ability/common/common'
import { stringifyError, IgnoreError } from "paas-base-ability/utils/error-utility"
import { getEventAsyncFuncWithPerformance } from "paas-base-ability/common/performance_monitor"


import dialogset from 'ava-ui/fxui/DialogCenter/DialogSet'

import { dhtWeLogService } from 'dht-services/DhtWeLogService'


export default class PricePolicyPlugin {
    constructor(pluginService, pluginParam) {

        this.pluginService = pluginService
        this.pluginParam = pluginParam

        let { params, objectApiName } = pluginParam.describe || {}
        let { details, fieldMapping } = params || {}
        let first = details && details[0]

        /**
         * 初始化context上部分数据
         * @type {{detailObjectApiName: *, mainObjectApiName: string, requestId: string}}
         */
        this.context = {
            requestId: _.uuid(),
            mainObjectApiName: objectApiName,
            detailObjectApiName: first && first.objectApiName,
            multi_unit_module: {
                multiUnitCalcPrice: (async function (params) {
                    let { giftMap, context } = params || {}
                    let proxy = this.getProxy()
                    let obj = {
                        ...this.context.plugin_exec_options,
                        // 主对象
                        masterData: proxy && proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName),
                        // 执行取价的数据列表
                        triggerObjectDataList: Object.values(giftMap || {}),
                        // 执行取价后，是否需要通过dataUpdater更新数据
                        doNotUpdateData: true,
                    }
                    let result = await this.pluginService.run("multi-unit.triggerBatchCalc", obj)
                    if (!result || result.StatusCode !== 0) return null
                    let {
                        // 更新的数据
                        updateResult,
                        // 接口返回的数据
                        calcResult,
                    } = result.Value || {}
                    let dataDiffMap = {}
                    if (updateResult) {
                        let detail_mapper = proxy.common.mapper.createContextMapper(context, context.detailObjectApiName)
                        for (let key in updateResult) {
                            let diff = updateResult[key]
                            let item = obj.triggerObjectDataList.find(i => i.dataIndex === key || i.data_index === key)
                            if (item) dataDiffMap[detail_mapper.value(item, 'prod_pkg_key')] = diff
                        }
                    }
                    return { dataDiffMap }
                }).bind(this),
            },
            real_price_module: {
                getRealPrice: (async function (params) {
                    let { giftMap, context } = params || {}
                    let { mapper, detailObjectApiName, mainObjectApiName } = context || {}
                    let proxy = this.getProxy()
                    let obj = {
                        ...this.context.plugin_exec_options,
                        // 主对象
                        masterData: proxy && proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName),
                        // 执行取价的数据列表
                        triggerObjectDataList: Object.values(giftMap || {}),
                        // 执行取价后，是否需要通过dataUpdater更新数据
                        doNotUpdateData: true,
                        fieldMapping: {
                            detailFields: { ...mapper[detailObjectApiName] },
                            masterFields: { ...mapper[mainObjectApiName] },
                        }
                    }
                    let result = await this.pluginService.run("price-service.triggerBatchCalc", obj)
                    if (!result || result.StatusCode !== 0) return null
                    let {
                        // 更新的数据
                        updateResult,
                        // 删除的数据
                        deleteResult,
                        // 取价结果，接口返回的数据
                        getRealPriceResult,
                    } = result.Value || {}
                    let dataDiffMap = {}
                    if (updateResult && updateResult.updateResult) {
                        let detail_mapper = proxy.common.mapper.createContextMapper(context, context.detailObjectApiName)
                        for (let key in updateResult.updateResult) {
                            let diff = updateResult.updateResult[key]
                            let item = obj.triggerObjectDataList.find(i => i.dataIndex === key || i.data_index === key)
                            if (item) dataDiffMap[detail_mapper.value(item, 'prod_pkg_key')] = diff
                        }
                    }
                    return { dataDiffMap }
                }).bind(this),
            },
            multi_currency_module: {
                checkMultiCurrencyStatus: (async function (params) {
                    let res = await this.pluginService.run("mccurrency.isOpenMultiCurrency.sync", { ...this.context.plugin_exec_options })
                    return (res && res.StatusCode === 0) ? { status: res.Value } : null
                }).bind(this),
            },
            period_product_module: {
                requestPeriodProductCalculate: (async function (params) {
                    let { context, giftMap } = params || {}
                    if (!giftMap) return null
                    let objectDataList = Object.values(giftMap) || []
                    if (objectDataList.length === 0) return null
                    let obj = {
                        objectDataList,
                        doNotUpdateData: true,
                        ...this.context.plugin_exec_options,
                    }
                    let res = await this.pluginService.run("period-product.triggerBatchCalc", obj)
                    if (!res || res.StatusCode !== 0) return null

                    let map = res.Value
                    if (!map) return null

                    let detailDiffMap = {}
                    let proxy = this.getProxy()
                    let detail_mapper = proxy && proxy.common.mapper.createContextMapper(context, context.detailObjectApiName)
                    for (let index in map) {
                        let o = objectDataList.find(i => i.dataIndex === index)
                        let key = detail_mapper && detail_mapper.value(o, 'prod_pkg_key')
                        if (key != null) detailDiffMap[key] = map[index]
                    }
                    return { detailDiffMap }
                }).bind(this)
            }
        }
    }





    /************************************************************************ Memory Insufficient Related Logic ************************************************************************/


    // 在内存不足时，存储重要数据
    onSaveInstanceState(pluginExecResult, options) {
        console.log("price policy plugin execute 'form.saveState.before.sync'")
        if (options.saveData) options.saveData[`${this.context.mainObjectApiName}_policy_plugin_store_key`] = {
            seriesId: this.context.seriesId,
            policyLimits: this.context.policyLimits,
            groupMap: this.context.groupMap,
            masterPricePolicy: this.context.masterPricePolicy,
            objectMatchGiftMap: this.context.objectMatchGiftMap,
            detailPricePolicyMap: this.context.detailPricePolicyMap,
            policyConfiguration: this.context.policyConfiguration,
            businessConfiguration: this.context.businessConfiguration,
        }
    }


    // 通过key恢复存储的数据
    restoreInstanceState(recoverData) {
        let key = `${this.context.mainObjectApiName}_policy_plugin_store_key`
        let data = recoverData && recoverData[key]
        if (!data) return
        this.context = Object.assign(this.context || {}, data)
    }


    /******************************************************************************************************************************************************************/




    /************************************************************************ 遇到报错存草稿 ************************************************************************/


    saveDraftFailAlert(pageId) {
        dialogset.alert.show({
            text: (fsapi.i18n.get("ava.otc.catch.error.save.draft.fail.prompt") || "发生了意外或网络连接中断，草稿保存失败。"),//l-18nIgnore
            btnText: fsapi.i18n.get("jsapi.common.alert.known"),
            onClose: fsapi.util.throttle(function () { wx.nextTick(() => { wx.navigateBack({ delta: 1 }) }) }),
            onClick: fsapi.util.throttle(function () { wx.nextTick(() => { wx.navigateBack({ delta: 1 }) }) }),
        }, pageId)
    }


    tryToSaveDraftForOperationError(formApis, error, pageId, prompt) {
        console.log("try to save draft for plugin operation error!")
        if (!formApis || !formApis.isSupportDraft()) {
            console.log("try to naviback for not supporting draft or no formApis!")
            dialogset.alert.show({
                text: (fsapi.i18n.get("ava.otc.catch.error.exit.page.prompt") || "发生了意外或网络连接中断，将退出当前页面，带来的不便十分抱歉，请上传日志并联系客服"),//l-18nIgnore
                btnText: fsapi.i18n.get("jsapi.common.alert.known"),
                onClose: fsapi.util.throttle(function () { console.log("navi back alert on close"); wx.nextTick(() => { wx.navigateBack({ delta: 1 }) }) }),
                onClick: fsapi.util.throttle(function () { console.log("navi back alert on click"); wx.nextTick(() => { wx.navigateBack({ delta: 1 }) }) }),
            }, pageId)
            return true
        }
        if (!pageId) pageId = this.currentPageId()
        console.log("try to show alert to save draft!")

        let closeCompletion = (async function () {
            try {
                await formApis.triggerSaveDraft({ background: false, close: true })
                console.log("save draft success!")
            } catch (error) {
                console.log("save draft fail!")
                wx.nextTick((function () { this.saveDraftFailAlert(pageId) }).bind(this))
            }
        }).bind(this)

        dialogset.alert.show({
            text: validString(prompt) ? prompt : (fsapi.i18n.get("ava.otc.catch.error.try.save.draft.prompt") || "发生了意外，将尝试保存到草稿箱。"),//l-18nIgnore
            onClose: closeCompletion,
            onClick: closeCompletion
        }, pageId)

        return true
    }


    /******************************************************************************************************************************************************************/



    /************************************************************************ 内部更新数据方法 ************************************************************************/


    // 更新上下文数据
    _updateContext(options) {
        let { dataGetter, dataUpdater, formApis } = options
        if (!dataGetter) return
        let mainObjectData = dataGetter.getMasterData()
        this.context.requestId = mainObjectData.requestId || this.context.requestId
        if (!mainObjectData.requestId && dataUpdater && dataUpdater.updateMaster) dataUpdater.updateMaster({ requestId: this.context.requestId })
        this.context.page_code = dataGetter.getPageOptions('_dataCode')
        this.context.pageId = dataGetter.getPageId()
        this.context.sourceAction = dataGetter.getSourceAction()
        this.context.entrySource = dataGetter.getEntrySource()
        this.context.plugin_exec_options = options
        let mainObjectApiName = mainObjectData.object_describe_api_name
        let proxy = this.getProxy()
        if (!proxy) return
        let objectDataMap = { [mainObjectApiName]: { [proxy.common.define.MAIN_OBJECT_INDEX]: mainObjectData } }
        let map = dataGetter.getDetails()
        let mapper = this.context.mapper
        _.each(map, (list, objectApiName) => {
            let dataMap = {}
            let fieldMapping = mapper && mapper[objectApiName]
            let prod_pkg_key_field_name = fieldMapping && fieldMapping['prod_pkg_key'] || 'prod_pkg_key'
            list && list.forEach(element => {
                if (!element[prod_pkg_key_field_name]) element[prod_pkg_key_field_name] = _.uuid()
                // TODO: 索引考虑直接使用prod_pkg_key
                let index = element.dataIndex || element.data_index
                if (index != null) dataMap[index] = element
            })
            objectDataMap[objectApiName] = dataMap
            if (objectApiName !== this.context.detailObjectApiName) return
            _.each(this.context.objectMatchGiftMap, (objectGiftMap, objApiName) => {
                _.each(objectGiftMap, (m, parent) => {
                    let tmp = {}
                    _.each(m, (gifts, rule_id) => {
                        let arr = []
                        gifts && gifts.forEach(data => {
                            let prod_pkg_key = data && data[prod_pkg_key_field_name]
                            let index = data.dataIndex || data.data_index
                            let o = list && list.find(i => {
                                let i_index = i.dataIndex || i.data_index
                                return i && (i[prod_pkg_key_field_name] === prod_pkg_key || (validString(i_index) && validString(index) && i_index === index))
                            })
                            if (o) arr.push(o)
                            else arr.push(data)
                        })
                        tmp[rule_id] = arr
                    })
                    objectGiftMap[parent] = tmp
                })
            })
        })
        this.context.objectDataMap = objectDataMap
        this.context.index_keygen = formApis && formApis.createNewDataIndex
        this.context.ui_event_handler = !formApis ? null : {
            doUIEvent: (async function (parameter) {
                let {
                    bizInfo,
                    event_id,
                    seriesId,
                    detailObjectData,
                    masterObjectData,
                    maskFieldApiNames,
                    masterLayoutApiName,
                } = parameter || {}
                try {
                    if (validString(this.context.token)) this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(this.context.token, this.context.pageId)
                    let result = await formApis.triggerCalAndUIEvent({
                        beforeUiPost: (function (params) {
                            if (!params || !params.data) return
                            params.data.detail_object_data = detailObjectData
                            params.data.object_data = masterObjectData
                            params.bizInfo = bizInfo || params.bizInfo
                        }).bind(this),
                        objApiName: masterObjectData && masterObjectData.object_describe_api_name,
                        uiEventId: event_id,
                    })
                    if (validString(this.context.token)) this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(this.context.token, null, this.context.pageId)
                    return result && result.uiRst
                } catch (error) {
                    throw new IgnoreError(stringifyError(error))
                }
            }).bind(this)
        }
    }


    // 根据上下文，更新主从对象数据
    _updateDataWithContext(context, dataUpdater, dataGetter) {
        this.backupObjectData()
        let proxy = this.getProxy()
        if (!proxy) return
        let mapper = context.mapper
        for (let objectApiName in context.objectDataMap) {
            let dataMap = context.objectDataMap[objectApiName]
            if (objectApiName === context.mainObjectApiName) {
                let mainObjectData = dataMap && dataMap[Object.keys(dataMap)[0]]
                let diff = buildDiffMap(_.cloneDeep(mainObjectData), dataGetter && dataGetter.getMasterData())
                if (diff) dataUpdater.updateMaster(diff)
            } else {
                let fieldMapping = mapper && mapper[objectApiName]
                let list = Object.values(dataMap)
                list = list && list.filter(d => {
                    if (proxy.price_policy.gift.utility.isPolicyGift(d, fieldMapping)) return Number(d[fieldMapping && fieldMapping['quantity'] || 'quantity']) > 0
                    return true
                })
                dataUpdater.updateDetailByApiName(objectApiName, proxy.price_policy.sortDetails(list, context), true)
            }
        }
    }


    /******************************************************************************************************************************************************************/



    insertPeriodProductModule() {
        this.context.period_product_module = { mapper: this.period_product_mapper }
        // TODO: periodProduct
    }


    _merge_mapper(mapper, other) {
        if (!other) return mapper
        if (!mapper) return other
        for (let objectApiName in other) {
            let other_map = other[objectApiName]
            mapper[objectApiName] = Object.assign(mapper[objectApiName] || {}, other_map)
        }
        return mapper
    }


    /******************************************************************************************************************************************************************/






    getProxy() {
        return this.otc_proxy && this.otc_proxy.getProxy()
    }



    backupObjectData() { this.context.origin = clone(this.context.objectDataMap) }


    getCurrentData(object_data, context, proxy) {
        if (object_data.object_describe_api_name === context.mainObjectApiName) {
            return proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName)
        } else {
            let index = proxy.price_policy.utility.findDetailIndex(context, object_data)
            let detailMap = context.objectDataMap && context.objectDataMap[context.detailObjectApiName]
            return detailMap && detailMap[index] || object_data
        }
    }



    _ui_event_type(context) {
        if (!context) return null
        let { policyAfterEventResult, policyCancelAfterEventResult } = context

        let uiEventType = null
        if (policyCancelAfterEventResult) uiEventType = 'cancel'
        else if (policyAfterEventResult) uiEventType = 'after'

        return uiEventType
    }


    _createContextIdentifier(dataGetter) {
        if (!dataGetter) return 'price_policy'
        let pageCode = dataGetter.getPageOptions('_dataCode')
        return `${pageCode}_price_policy`
    }


    currentPageId() {
        let page = fsapi.page.getTopPage()
        return page && page.getPageId()
    }


    tryShowChangeInfo() {
        let policyChangeInfo = this.context.policyChangeInfo || []
        let exist_main = policyChangeInfo.find(obj => obj.is_main_object) != null
        let arr = []
        policyChangeInfo.forEach(info => {
            if (!info.is_main_object) arr.push(info.label)
        })
        let message = arr && arr.join(",")
        let msg_arr = []
        if (message && message.length > 0) {
            msg_arr.push(fsapi.i18n.get(exist_main ? 'ava.price.policy.exist.main.changed.prompt.for.form.edit.action' : 'ava.price.policy.changed.prompt.for.form.edit.action', [{ message }]))
        } else if (policyChangeInfo.length > 0) {
            msg_arr.push(fsapi.i18n.get("ava.price.policy.only.main.changed.prompt.for.form.edit.action"))
        }
        if (this.context.removed_gifts_info && validString(this.context.removed_gifts_info.prompt)) msg_arr.push(this.context.removed_gifts_info.prompt)
        if (msg_arr.length > 0) this.pluginService.api.alert(msg_arr.join('\n\n'))
    }




    async _store_context(options) {
        try {
            let obj = await requireUtils.requireAsync('../objformpkgo2c/package/onsale/plugins/plugin_share')
            let share = obj && obj.getPluginShare()
            share && share.storeContext && share.storeContext(this._createContextIdentifier(options && options.dataGetter), this.context)
        } catch (error) {
            console.log(`price policy plugin 'form.render.before' require plugin share error: ${stringifyError(error)}`)
            console.error(error)
        }
    }



    async _require_object_hook() {
        try {
            let obj_hook = await requireUtils.requireAsync('../otc-base-ability/object/object_hook')
            return obj_hook && obj_hook.get_object_hook && obj_hook.get_object_hook(this.context.mainObjectApiName)
        } catch (error) {
            console.log(`price policy plugin 'pluginService.use.after' require object hook error: ${stringifyError(error)}`)
            console.error(error)
        }
    }



    async _require_proxy() {
        let proxy = this.getProxy()
        if (proxy) return proxy
        try {
            let res = await requireUtils.requireAsync('../objformpkgo2c/package/onsale/plugins/otc-ability-proxy')
            this.otc_proxy = res && res.default
            if (this.otc_proxy) await this.otc_proxy.initProxy()
            return this.getProxy()
        } catch (error) {
            console.log(`price policy plugin require otc proxy error: ${stringifyError(error)}`)
            console.error(error)
        }
    }


    async _query_multi_currency_status(options) {
        let res = await this.pluginService.run("mccurrency.isOpenMultiCurrency.sync")
        return (res && res.StatusCode === 0) ? { status: res.Value } : null
    }


    async queryContext(pluginExecResult, options) {
        console.log("price policy plugin execute 'query.context'")
        if (options && options.dataGetter) this._updateContext(options)
        return this.context
    }


    syncQueryContext(pluginExecResult, options) {
        console.log("price policy plugin execute 'sync.query.context'")
        if (options && options.dataGetter) this._updateContext(options)
        return this.context
    }


    syncContext(pluginExecResult, options) {
        console.log("price policy plugin execute 'sync.context'")
        let { context, dataUpdater, dataGetter } = options
        this.context = context || this.context
        if (dataUpdater) this._updateDataWithContext(this.context, dataUpdater, dataGetter)
    }







    async afterRegisterPlugins(pluginExecResult, options) {
        console.log("price policy plugin execute 'pluginService.use.after'")
        let plugins = pluginExecResult && pluginExecResult.api.getPlugins()
        if (!plugins || plugins.length === 0) return

        let period_product_plugin = plugins.find(i => i.pluginApiName === 'period_product')
        if (!period_product_plugin) return
        let { objectApiName, params } = period_product_plugin
        let { details, fieldMapping } = params || {}

        let map = { [objectApiName]: fieldMapping }
        details && details.forEach(e => { map[e.objectApiName] = e.fieldMapping })

        this.period_product_mapper = map
    }








    // 触发手动匹配
    async triggerManualMatch(formApis, params) {
        if (!formApis) return
        formApis.npcRun((async function (utility) {
            let { dataUpdater, dataGetter } = utility
            this._updateContext(utility)
            let token = `policy.trigger.manual.match.${_.uuid()}`
            this.context.token = token
            try {
                let { objectDataMap, mainObjectApiName } = this.context || {}
                let proxy = await this._require_proxy()
                if (proxy.common.plugin_public.is_data_scarcity(this.context)) return
                if (proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) {
                    this.pluginService.api.showToast(fsapi.i18n.get('ava.policy.control.coupon.conflict.tip'))
                    this.context.token = null
                    return
                }
                // 校验客户
                let mapper = proxy.common.mapper.createContextMapper(this.context, mainObjectApiName)
                if (!validString(mapper.value(proxy.common.utility.getMainObjectData(objectDataMap, mainObjectApiName), 'account_id'))) {
                    let field = mapper.field('account_id')
                    let label = field && field.label || fsapi.i18n.get('AccountObj.attribute.self.display_name')
                    let msg = fsapi.i18n.get('ava.object_form.plese_select_first', [label])
                    formApis && formApis.focusShowMasterFieldError && formApis.focusShowMasterFieldError(field.api_name, msg, true)
                    this.context.token = null
                    return
                }

                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
                this.context.objectDataDiffMap = this.context.topChangeInfo = {}
                await proxy.price_policy.triggerManualMatch(null, this.context)
                this._updateDataWithContext(this.context, dataUpdater, dataGetter)
                dataUpdater && dataUpdater.updateMaster({ exist_delete: false, exist_new_create: false, exist_condition_fields_changed: false })
                proxy.common.plugin_public.resetPolicyReadonlyFields({ context: this.context, dataUpdater })
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                this.context.token = null
                await this.notifyAfterPolicyEvent(this.context, utility)
            } catch (error) {
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                this.context.token = null
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
        }).bind(this))
    }


    async _fill_context_data(options) {
        let values = await Promise.all([this._require_proxy(), this._require_object_hook(), this._query_multi_currency_status(options)])
        let proxy = values && values[0]
        if (!proxy) return

        let hook = values && values[1]
        if (hook) this.context.hooks = [hook]

        this.context.mapper = this._merge_mapper(proxy.common.mapper.objectFieldsMapperFromPlugin(this.pluginParam), this.period_product_mapper)
        this.context.businessConfiguration = proxy.common.configuration.parseMap(this.pluginParam.bizStateConfig)

        let { status } = values && values[2] || {}
        if (status !== null) this.context.businessConfiguration.multi_currency_status = status

        let { dataGetter } = options || {}
        if (!dataGetter) return await this._store_context(options)

        let mainObjectData = dataGetter.getMasterData()
        let mainObjectApiName = mainObjectData && mainObjectData.object_describe_api_name
        let describeAndLayout = dataGetter.getDescribeLayout()
        let mainObjectDescribe = dataGetter.getDescribe(mainObjectApiName)

        let formLayoutMap = (describeAndLayout.record_type && describeAndLayout.layout) ? { [mainObjectApiName]: { [describeAndLayout.record_type]: describeAndLayout.layout } } : {}
        _.each(describeAndLayout && describeAndLayout.detailObjectList, element => {
            let { objectApiName, layoutList } = element
            let map = {}
            _.each(layoutList, e => {
                let { record_type, detail_layout } = e
                map[record_type] = detail_layout
            })
            formLayoutMap[objectApiName] = map
        })

        let objectDescribeMap = { [mainObjectApiName]: mainObjectDescribe }
        let map = dataGetter.getDetails()
        _.each(map, (list, objectApiName) => { objectDescribeMap[objectApiName] = dataGetter.getDescribe(objectApiName) })

        this.context = Object.assign(this.context || {}, {
            pageId: dataGetter.getPageId(),
            requestId: mainObjectData.requestId || this.context.requestId,
            page_code: dataGetter.getPageOptions("_dataCode"),
            formLayoutMap,
            objectDescribeMap,
        })

        await this._store_context(options)
    }


    /**
     *
     * @param pluginExecResult
     * @param options
     * @returns {Promise<*>}
     */
    async beforeFormRender(pluginExecResult, options) {
        console.log("price policy plugin execute 'form.render.before'")

        await this._fill_context_data(options)

        let { preData } = pluginExecResult || {}

        let proxy = this.getProxy()
        if (!proxy) return preData

        let { businessConfiguration } = this.context || {}

        let match_mode = businessConfiguration && businessConfiguration[proxy.common.configuration.configKey.match_mode.value]
        if (match_mode !== proxy.price_policy.define.POLICY_MATCH_MODE.ONCE) return preData

        if (proxy.common.plugin_public.is_data_scarcity(this.context)) return preData

        let tmp = (options.buttons || []).find(i => i.action === 'manual_match_policy')
        if (!tmp) {
            let { formApis } = options || {}
            options.buttons && options.buttons.push({
                action: "manual_match_policy",
                action_type: "default",
                api_name: "manual_match_policy_button_default",
                label: fsapi.i18n.get("ava.price.policy.manual.match.btn.text"), // 计算促销
                onClick: _.debounce((async function (params) {
                    return await this.triggerManualMatch(formApis, params)
                }).bind(this), 500)
            })
        }
        return preData
    }










    // 抛出价格政策执行完成事件，通知进行监听的其他业务插件执行相关逻辑
    async notifyAfterPolicyEvent(context, options) {
        let { objectDataDiffMap, mainObjectApiName, detailObjectApiName } = context || {}
        let uiEventType = this._ui_event_type(context)
        console.log("price policy plugin emit event 'policy.match.event.exec.end'")
        await this.pluginService.run("policy.match.event.exec.end", Object.assign(options || {}, { objectDataDiffMap, mainObjectApiName, detailObjectApiName, uiEventType }))
        console.log("price policy plugin notify 'after-policy' event finish")
    }









    async pageReadyAfterAutoUseCoupon(pluginExecResult, options) {
        console.log("price policy plugin execute 'coupon.auto.use.end.after.page.ready'")
        this._updateContext(options)
        let token = `policy.coupon.auto.use.end.after.page.ready.${_.uuid()}`
        this.context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            let proxy = await this._require_proxy()
            if (!proxy
                || proxy.common.plugin_public.is_data_scarcity(this.context)
                || proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) {
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                return
            }
            let { dataUpdater, dataGetter } = options
            await proxy.price_policy.processingLogicAfterPageReady(null, this.context)
            // TODO:
            this._updateDataWithContext(this.context, dataUpdater, dataGetter)
            proxy.common.plugin_public.resetPolicyReadonlyFields({ context: this.context, dataUpdater })
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            this.tryShowChangeInfo()
            await this.notifyAfterPolicyEvent(this.context, options)
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
        }
    }







    async handleLogicAfterRenderEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'form.render.end'")

        let { dataUpdater, dataGetter } = options
        let token = `policy.form.render.end.${_.uuid()}`
        this.context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        this._updateContext(options)
        try {
            let proxy = await this._require_proxy()
            if (!proxy) {
                this.form_render_end = true
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                this.context.token = null
                return
            }

            this.context = proxy.common.plugin_public.richContextOnRenderEnd(options, this.context)

            if (proxy.common.plugin_public.is_data_scarcity(this.context)) {
                this.form_render_end = true
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                this.context.token = null
                return
            }

            let recoverData = dataGetter && dataGetter.getRecoverData && dataGetter.getRecoverData()
            if (recoverData) {
                // 由于内存不足被kill掉后恢复的页面
                this.restoreInstanceState(recoverData)
                await this._store_context(options)
                this.form_render_end = true
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                this.context.token = null
                return
            }

            let { businessConfiguration, objectDataMap, mainObjectApiName } = this.context || {}
            let openCoupon = businessConfiguration && businessConfiguration[proxy.common.configuration.configKey.coupon.value]
            let mainObjectData = proxy.common.utility.getMainObjectData(objectDataMap, mainObjectApiName)
            let { couponInstanceInfo, couponInstanceConsumed } = mainObjectData || {}
            let isFromCouponDetail = !fsapi.util.isEmpty(couponInstanceInfo) || couponInstanceConsumed
            let { repel_price_policy } = couponInstanceInfo || {}
            if (openCoupon && isFromCouponDetail && repel_price_policy) {
                console.log("price policy plugin prepare for page ready in 'form.render.end' hook method")
                // TODO: node
                await proxy.price_policy.prepareForPageReady(null, this.context)
                this._updateDataWithContext(this.context, dataUpdater, dataGetter)
            } else {
                // TODO: node
                this.context = await proxy.price_policy.pageReady(null, this.context)
                this._updateDataWithContext(this.context, dataUpdater, dataGetter)
                proxy.common.plugin_public.resetPolicyReadonlyFields({ context: this.context, dataUpdater })
                await this.notifyAfterPolicyEvent(this.context, options)
            }
            this.form_render_end = true
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            this.context.token = null
            this.tryShowChangeInfo()
        } catch (error) {
            this.form_render_end = true
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            this.context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
        }
    }













    clearDetailFieldConfiguration() {
        let proxy = this.getProxy()
        if (!proxy) return
        return [{
            field_name: "account_id",
            extra_clear_fields: (function () { return [] }),
            tip: (function (label) {
                // ava.object_form.onsale.change_field_tip_1 更换${label}将清空已选价目表和产品，确认更换？
                let tip1 = fsapi.i18n.get('ava.object_form.onsale.change_field_tip_1', [label]) || `更换${label}将清空已选价目表和产品，确认更换？`//l-18nIgnore
                // ava.object_form.onsale.change_field_tip_2 更换${label}将清空已选产品，确认更换？
                let tip2 = fsapi.i18n.get('ava.object_form.onsale.change_field_tip_2', [label]) || `更换${label}将清空已选产品，确认更换？`//l-18nIgnore
                let isOpenPriceBook = this.context.businessConfiguration && this.context.businessConfiguration[proxy.common.configuration.configKey.is_open_price_book.value]
                return isOpenPriceBook ? tip1 : tip2
            }).bind(this)
        }, {
            field_name: "price_book_id",
            extra_clear_fields: (function () { return [] }),
            tip: (function (label) {
                // ava.object_form.onsale.change_priceook_tip 更换价目表将清空已选产品，确认更换？
                return fsapi.i18n.get("ava.object_form.onsale.change_priceook_tip")
            }).bind(this)
        }]
    }





    isClearDetailFieldChanged(options, changedFields) {
        if (!changedFields || changedFields.length === 0) return false
        let { objApiName, dataGetter } = options
        let context = this.context
        if (objApiName !== context.mainObjectApiName) return false
        let config = this.clearDetailFieldConfiguration()
        let res = false
        let proxy = this.getProxy()
        if (!proxy) return
        let main_mapper = proxy.common.mapper.createContextMapper(context, context.mainObjectApiName)
        changedFields.forEach(f => {
            if (config.find(i => main_mapper.field_name(i.field_name) === f)) res = true
        })
        return res
    }







    async checkValidityBeforeSubmit(options) {
        let context = this.context
        let proxy = await this._require_proxy()
        if (!proxy) return false
        if (proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) return false
        // 1. 是否开启手动匹配
        // 2. 是否存在计算过促销的标记
        // 3. 是否曾增删明细
        // 4. 是否曾修改过条件字段
        let { businessConfiguration, objectDataMap, mainObjectApiName } = context || {}
        let match_mode = businessConfiguration && businessConfiguration[proxy.common.configuration.configKey.match_mode.value]
        if (match_mode !== 'once') return false

        let mainObjectData = proxy.common.utility.getMainObjectData(objectDataMap, mainObjectApiName)
        let { exist_delete, exist_new_create, exist_condition_fields_changed } = mainObjectData || {}
        let triggerMatch = exist_delete || exist_new_create || exist_condition_fields_changed
        if (!triggerMatch) return false

        // 校验客户
        let mapper = proxy.common.mapper.createContextMapper(context, mainObjectApiName)
        if (!validString(mapper.value(mainObjectData, 'account_id'))) {
            let { formApis } = options || {}
            let field = mapper.field('account_id')
            let label = field && field.label || fsapi.i18n.get('AccountObj.attribute.self.display_name')
            let msg = fsapi.i18n.get('ava.object_form.plese_select_first', [label])
            formApis && formApis.focusShowMasterFieldError && formApis.focusShowMasterFieldError(field.api_name, msg, true)
            return true
        }
        let token = `policy.form.submit.before.${_.uuid()}`
        this.context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        // TODO: node
        context = await proxy.price_policy.triggerManualMatch(null, context)
        let { dataUpdater, dataGetter } = options || {}
        dataUpdater && dataUpdater.updateMaster({ exist_delete: false, exist_new_create: false, exist_condition_fields_changed: false })
        this._updateDataWithContext(context, dataUpdater, dataGetter)
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
        await this.notifyAfterPolicyEvent(this.context, options)
        // "ava.price.policy.changed.after.manual.match.before.submit.prompt": "本次编辑的信息将影响促销结果，请再次确认！"
        this.pluginService.api.alert(fsapi.i18n.get("ava.price.policy.changed.after.manual.match.before.submit.prompt"))
        return true
    }





    async handleLogicBeforeSubmit(pluginExecResult, options) {
        console.log("price policy plugin execute 'form.submit.before'")
        this._updateContext(options)
        let { details, object_data } = options
        // 防止requestId丢失
        if (object_data && !validString(object_data.requestId)) object_data.requestId = this.context.requestId

        let context = this.context
        let proxy = await this._require_proxy()
        if (!proxy) return

        let { hasPricePolicy } = this.context.policyConfiguration || {}
        if (!hasPricePolicy) return

        if (proxy.common.plugin_public.is_data_scarcity(this.context)
            || proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) return

        let res = await this.checkValidityBeforeSubmit(options)
        if (res) return { consumed: true }

        let main_mapper = proxy.common.mapper.createContextMapper(context, context.mainObjectApiName)
        let detail_mapper = proxy.common.mapper.createContextMapper(context, context.detailObjectApiName)

        if (main_mapper.value(object_data, 'gift_map')) delete object_data[main_mapper.field_name('gift_map')]
        _.each(details, (datas, objectApiName) => {
            if (objectApiName !== context.detailObjectApiName) return
            _.each(datas, d => {
                if (detail_mapper.value(d, 'gift_map')) delete d[detail_mapper.field_name('gift_map')]
            })
        })
        if (details && details[context.detailObjectApiName]) details[context.detailObjectApiName] = proxy.price_policy.sortDetailsForCommit(context, details[context.detailObjectApiName])
    }





    judgeTriggerMatchOnAddManualGift(context, proxy, objectDataDiffMap) {
        // 修改了非临时赠品的明细，并修改了条件字段，则触发价格政策匹配
        // 未修改非临时赠品的明细，但修改了主对象的条件字段，则触发价格政策匹配
        let { objectDataMap, mapper, mainObjectApiName, detailObjectApiName } = context || {}
        let main_changed_fields = []
        let detail_changed_fields = []
        let main_condition_fields = proxy.price_policy.objectConditionFields(context, mainObjectApiName) || []
        let detail_condition_fields = proxy.price_policy.objectConditionFields(context, detailObjectApiName) || []
        Object.keys(objectDataDiffMap || {}).forEach(objectApiName => {
            let diffMap = objectDataDiffMap[objectApiName]
            if (objectApiName === context.mainObjectApiName) {
                // 收集主对象修改的字段
                Object.keys(diffMap || {}).forEach(index => {
                    let diff = diffMap[index]
                    main_changed_fields = main_changed_fields.concat(Object.keys(diff || {}))
                })
            } else if (objectApiName === context.detailObjectApiName) {
                let dataMap = objectDataMap[objectApiName]
                // 收集从对象修改的字段
                Object.keys(diffMap || {}).forEach(index => {
                    let data = dataMap && dataMap[index]
                    if (proxy.manual_gift.utility.isManualGift(data, proxy.common.mapper.objectFieldMap(mapper, detailObjectApiName))) delete diffMap[index]
                    let diff = diffMap[index]
                    detail_changed_fields = detail_changed_fields.concat(Object.keys(diff || {}))
                })
            }
        })
        let is_main_condition_fields_changed = main_changed_fields.reduce((res, element) => { return res || main_condition_fields.includes(element) }, false)
        let is_detail_condition_fields_changed = detail_changed_fields.reduce((res, element) => { return res || detail_condition_fields.includes(element) }, false)

        return { is_main_condition_fields_changed, is_detail_condition_fields_changed }
    }







    markForOnceMatch(dataUtility, mainObjectData, operationContext, trigger_no_amortize_match, pre) {
        let { objectDataDiffMap, exist_delete, exist_new_create } = operationContext || {}
        let context = this.context
        let { businessConfiguration } = context || {}
        let proxy = this.getProxy()
        let match_mode = businessConfiguration && businessConfiguration[proxy.common.configuration.configKey.match_mode.value]
        if (match_mode !== proxy.price_policy.define.POLICY_MATCH_MODE.ONCE) return false

        let { exist_delete: pre_exist_delete, exist_new_create: pre_exist_new_create, exist_condition_fields_changed: pre_exist_condition_fields_changed } = pre || {}
        let { exist_delete: c_exist_delete, exist_new_create: c_exist_new_create, exist_condition_fields_changed: c_exist_condition_fields_changed } = mainObjectData || {}
        let mainDiff = { exist_delete: exist_delete || c_exist_delete || pre_exist_delete, exist_new_create: exist_new_create || c_exist_new_create || pre_exist_new_create }
        if (pre_exist_condition_fields_changed || c_exist_condition_fields_changed) mainDiff.exist_condition_fields_changed = true
        if (trigger_no_amortize_match) {
            mainDiff.exist_condition_fields_changed = true
        } else if (proxy) {
            proxy.price_policy.compatibilityProcessingMultiUnitFields(context)
            if (proxy.price_policy.verifyFieldsInCondition(objectDataDiffMap, context)) mainDiff.exist_condition_fields_changed = true
        }
        let { dataUpdater } = dataUtility || {}
        dataUpdater && dataUpdater.updateMaster(mainDiff)
        return true
    }











    async tryConfirmChangeAccount(message) {
        return new Promise((resolve, reject) => { this.pluginService.api.confirm({ title: '', msg: message, success: () => resolve({ success: true }), cancel: () => resolve({ cancel: true }) }) })
    }




    async forceTriggerPolicyMatch(pluginExecResult, options) {

        let { addOpt, dataUpdater, dataGetter } = options || {}
        let { isAddManualGift } = addOpt || {}

        let context = this.context
        let proxy = await this._require_proxy()
        if (!proxy) return
        // 判断是否开启价格政策
        let isOpenPricePolicy = context.businessConfiguration && context.businessConfiguration[proxy.common.configuration.configKey.is_open_price_policy.value]
        if (!isOpenPricePolicy) return context

        if (proxy.common.plugin_public.is_data_scarcity(this.context)
            || proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) return

        let operationContext = proxy.common.plugin_public.pluginEventOperationContext({ options, context }) || {}
        let { objectDataDiffMap } = operationContext || {}

        let manual_gift_judge_result =  isAddManualGift ? this.judgeTriggerMatchOnAddManualGift(context, proxy, objectDataDiffMap) : null
        let { is_main_condition_fields_changed, is_detail_condition_fields_changed } = manual_gift_judge_result || {}
        if (isAddManualGift && manual_gift_judge_result && !is_main_condition_fields_changed && !is_detail_condition_fields_changed) return

        let mainObjectData = proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName)
        // 手动匹配逻辑处理
        if (this.markForOnceMatch({ dataUpdater, dataGetter }, mainObjectData, operationContext)) return
        context.objectDataDiffMap = {}
        context.topChangeInfo = objectDataDiffMap || {}
        let token = `policy.force.match.${_.uuid()}`
        context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            // TODO: node
            let res = await proxy.price_policy.triggerMatchByDataDiffMap(null, context)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            this._updateDataWithContext(context, dataUpdater, dataGetter)
            // 更新只读必填状态
            proxy.common.plugin_public.resetPolicyReadonlyFields({ context, dataUpdater })
            await this.notifyAfterPolicyEvent(context, options)
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
            let { skipPlugin } = pluginExecResult || {}
            skipPlugin && skipPlugin()
        }
    }






    isTriggerNoAmortizeMatch(change_field, objectApiName) {
        let proxy = this.getProxy()
        let { businessConfiguration, mainObjectApiName, detailObjectApiName, objectDataMap, policyConfiguration } = this.context || {}
        let enable_manual_change_price = businessConfiguration && businessConfiguration[proxy.common.configuration.configKey.change_price_type.value] === 'direct'
        if (!enable_manual_change_price) return false

        let main_mapper = proxy.common.mapper.createContextMapper(this.context, mainObjectApiName)
        let detail_mapper = proxy.common.mapper.createContextMapper(this.context, detailObjectApiName)
        let mainObjectConditionFields = main_mapper.mappedFields(['order_amount', 'discount']), detailObjectConditionFields = detail_mapper.mappedFields(['sales_price', 'discount', 'subtotal'])

        let is_main = objectApiName === mainObjectApiName
        let judgeConditionFields = is_main ? mainObjectConditionFields : detailObjectConditionFields
        let isConditionFieldsChanged = isConditionFieldsChanged || judgeConditionFields.some(it => change_field === it)

        if (!isConditionFieldsChanged) return false
        // 判断是否使用了整单不分摊规则
        let mainObjectData = proxy.common.utility.getMainObjectData(objectDataMap, mainObjectApiName)
        let rule_ids = main_mapper.value(mainObjectData, proxy.price_policy.standard_fields.POLICY_RULE_IDS)
        let usingMasterNoAmortizeRule = rule_ids && (policyConfiguration.masterNoAmortizeRuleIdList || []).some(it => { return rule_ids.includes(it) })
        return !!usingMasterNoAmortizeRule
    }







    async afterFieldChanged(pluginExecResult, options) {
        console.log("price policy plugin execute 'field.edit.after'")


        if (!this.form_render_end) {
            let { objApiName, changeData } = options || {}
            let content = `do event: 'field.edit.after' before 'form.render.end' objApiName: ${objApiName}, changeData: ${JSON.stringify(changeData || {})}`
            changeData && Object.keys(changeData).forEach(it => delete changeData[it])
            // 上报并发
            fsapi.stat.kLog('sfa', 'triggerConcurrentExecution', content)
            this.pluginService.api.showToast(fsapi.i18n.get('ava.policy.concurrent.before.render.end.prompt'))
            let { skipPlugin } = pluginExecResult || {}
            skipPlugin && skipPlugin()
            return
        }

        this._updateContext(options)

        let proxy = await this._require_proxy()
        if (!proxy) return
        // 临时如此处理
        let context = this.context
        let isOpenPricePolicy = context.businessConfiguration && context.businessConfiguration[proxy.common.configuration.configKey.is_open_price_policy.value]
        if (!isOpenPricePolicy) return

        if (proxy.common.plugin_public.is_data_scarcity(this.context)) return

        this.backupObjectData()
    }










    async changeFieldEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'field.edit.end'")

        if (!this.form_render_end) {
            let { objApiName, changeData } = options || {}
            let content = `do event: 'field.edit.after' before 'form.render.end' objApiName: ${objApiName}, changeData: ${JSON.stringify(changeData || {})}`
            // 上报并发
            fsapi.stat.kLog('sfa', 'triggerConcurrentExecution', content)
        }

        this._updateContext(options)

        let proxy = await this._require_proxy()
        if (!proxy) return
        let context = this.context
        let { businessConfiguration, mainObjectApiName } = context || {}
        let isOpenPricePolicy = businessConfiguration && businessConfiguration[proxy.common.configuration.configKey.is_open_price_policy.value]
        if (!isOpenPricePolicy) return

        if (proxy.common.plugin_public.is_data_scarcity(this.context)
            || proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) return

        let { dataUpdater, dataGetter, fieldName, changeData, objApiName, dataIndex } = options

        let operationContext = proxy.common.plugin_public.pluginEventOperationContext({ options, context }) || {}
        let { objectDataDiffMap } = operationContext || {}

        let compare_result = proxy.common.utility.compareObjectDataMap(context.objectDataMap, context.origin)
        let { objectDataDiffMap: otherDiffMap } = compare_result || {}
        if (otherDiffMap) {
            if (proxy.price_policy.utility.filterDetailDiff) otherDiffMap = proxy.price_policy.utility.filterDetailDiff(context, otherDiffMap, [dataIndex])
            objectMergeWithMaxDeep(otherDiffMap || {}, objectDataDiffMap || {}, 2)
            objectDataDiffMap = otherDiffMap
        }
        context.objectDataDiffMap = {}
        context.topChangeInfo = objectDataDiffMap || {}

        let main_mapper = proxy.common.mapper.createContextMapper(context, mainObjectApiName)

        let tmp = Object.values(context.topChangeInfo[mainObjectApiName] || {})
        let main_diff = tmp && tmp[0]
        let mainChangedData = Object.assign({}, main_diff)
        if (objApiName === mainObjectApiName) Object.assign(mainChangedData, changeData)


        let trigger_no_amortize_match = this.isTriggerNoAmortizeMatch(objectDataDiffMap)
        let mainObjectData = proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName)
        // TODO: 这里注意若未触发价格政策匹配，context里topChangeInfo和objectDataDiffMap未清空，但手动触发价格政策逻辑处理了，所以没事
        let once = this.markForOnceMatch({ dataUpdater, dataGetter }, mainObjectData, operationContext, trigger_no_amortize_match)

        let token = `policy.field.edit.end.${_.uuid()}`
        context.token = token
        if (Object.keys(mainChangedData).indexOf(main_mapper.field_name('account_id')) >= 0) {
            try {
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
                // TODO: node
                context.policyConfiguration = await proxy.price_policy.findPricePolicyConfig(null, context)
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            } catch (error) {
                this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
                this.tryToSaveDraftForOperationError(options.formApis, error)
            }
        }

        if (once) return

        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            if (trigger_no_amortize_match) { // 处理手工改价逻辑
                // TODO: node
                await proxy.price_policy.triggerNoAmortizeMatch(null, context)
            } else await proxy.price_policy.triggerMatchByDataDiffMap(null, context) // TODO: node
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            this._updateDataWithContext(context, dataUpdater, dataGetter)
            // 更新只读必填状态
            proxy.common.plugin_public.resetPolicyReadonlyFields({ context, dataUpdater })
            await this.notifyAfterPolicyEvent(context, options)
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
            let { skipPlugin } = pluginExecResult || {}
            skipPlugin && skipPlugin()
        }
    }















    filterShowObjectData({ dataList, recordType }) {
        let proxy = this.getProxy()
        if (!proxy) return
        let list = proxy.price_policy.sortSameRecordTypeDetails(dataList, this.context)
        return list && list.filter(data => !proxy.price_policy.gift.utility.isPolicyGift(data, proxy.common.mapper.objectFieldMap(this.context.detailObjectApiName, this.context.mapper)))
    }




    async beforeMdRender(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.render.before'")

        let { preData } = pluginExecResult

        let { mdBottomComs, objApiName } = options

        if (objApiName !== this.context.detailObjectApiName) return

        let proxy = await this._require_proxy()
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return

        mdBottomComs = preData && preData.mdBottomComs || mdBottomComs

        let list = []

        let cmpt = null
        // 尝试在返利控件前面插入价格政策控件
        mdBottomComs && mdBottomComs.forEach(com => {
            if (com.name === 'rebate_coupon_plugin') {
                cmpt = { name: 'advanced-pricing-main-object-plugins' }
                list.push(cmpt)
            }
            list.push(com)
        })

        if (!cmpt) {
            // 没有找到价格政策控件时，在通用md前插入返利控件
            list = []
            mdBottomComs && mdBottomComs.forEach(com => {
                if (com.name === 'paas_mdcount') {
                    cmpt = { name: 'advanced-pricing-main-object-plugins' }
                    list.push(cmpt)
                }
                list.push(com)
            })
        }

        // 如果以上均未能插入，则直接追加在最后
        if (!cmpt) list = (mdBottomComs || []).concat([{ name: 'advanced-pricing-main-object-plugins' }])

        let originFilterFunctions = (preData && preData.filterShowObjectDataList) || options.filterShowObjectDataList || []

        return Object.assign({}, preData || {}, { mdBottomComs: list, refreshUI: {}, filterShowObjectDataList: originFilterFunctions.concat([this.filterShowObjectData.bind(this)]) })
    }





    beforeMdItemRender(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.item.render.before.sync'")
        let { preData } = pluginExecResult || {}
        let { objApiName, recordType, objectData, renderType } = options || {}

        if (objApiName !== this.context.detailObjectApiName) return preData

        let proxy = this.getProxy()
        if (proxy && proxy.common.plugin_public.is_data_scarcity(this.context)) return

        let { footerComs, headerComs, bellowTitleComs } = preData || {}

        return Object.assign(preData || {}, {
            headerComs: (headerComs || []).concat([{ name: "policy-detail-header" }]),
            bellowTitleComs: (bellowTitleComs || []).concat([{ name: "change_combine_policy_btn" }]),
            footerComs: (footerComs || []).concat([{ name: "detail-policy-gift-detail" }, { name: "detail-policy-gift-group" }]),
        })
    }















    async deleteDetailActionEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.del.end'")

        this._updateContext(options)

        let context = this.context

        let proxy = await this._require_proxy()
        if (!proxy) return
        // 判断是否开启价格政策
        let isOpenPricePolicy = context.businessConfiguration && context.businessConfiguration[proxy.common.configuration.configKey.is_open_price_policy.value]
        if (!isOpenPricePolicy) return

        if (proxy.common.plugin_public.is_data_scarcity(this.context)
            || proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) return

        let operationContext = proxy.common.plugin_public.pluginEventOperationContext({ options, context }) || {}
        let { objectDataDiffMap } = operationContext || {}

        // TODO: 这里注意若未触发价格政策匹配，context里topChangeInfo和objectDataDiffMap未清空，但手动触发价格政策逻辑处理了，所以没事
        if (this.markForOnceMatch(options, proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName), operationContext)) return

        let token = `policy.md.del.end.${_.uuid()}`
        context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            // TODO: node
            let res = await proxy.price_policy.triggerMatchAfterDeleteDetails(null, context)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            let { dataUpdater, dataGetter } = options || {}
            dataUpdater && dataUpdater.updateMaster({ exist_delete: true })
            this._updateDataWithContext(context, dataUpdater, dataGetter)
            proxy.common.plugin_public.resetPolicyReadonlyFields({ context, dataUpdater })
            await this.notifyAfterPolicyEvent(context, options)
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
            let { skipPlugin } = pluginExecResult || {}
            skipPlugin && skipPlugin()
        }
    }






    async afterDeleteDetailAction(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.del.after'")
        this._updateContext(options)
    }




    async addDetailActionEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.add.end'")
        this._updateContext(options)
        return await this.forceTriggerPolicyMatch(pluginExecResult, options)
    }



    async afterAddDetailAction(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.add.after'")
        this._updateContext(options)
    }




    async cloneDetailActionEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.clone.end'")
        this._updateContext(options)
        return await this.forceTriggerPolicyMatch(pluginExecResult, options)
    }



    async afterCloneDetailAction(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.clone.after'")
        let context = this.context
        this._updateContext(options)
        if (!context || !options || options.objApiName !== context.detailObjectApiName) return
        // clear price policy reference datas
        let { copyDataIndexs, newDatas, dataUpdater } = options
        let proxy = await this._require_proxy()
        if (!proxy) return

        if (proxy.common.plugin_public.is_data_scarcity(this.context)) return

        let mapper = proxy.common.mapper.createContextMapper(context, context.detailObjectApiName)
        let prod_pkg_key_count = proxy.common.utility.sumOfIdentifierCount((context.objectDataMap || {})[context.detailObjectApiName], mapper.field_name('prod_pkg_key'))
        newDatas && newDatas.forEach(data => {
            let diff = proxy.price_policy.utility.clearPricePolicyInfo(data, mapper.fieldMapping()) || {}
            let prod_pkg_key = mapper.value(data, 'prod_pkg_key')
            if (!prod_pkg_key || (prod_pkg_key_count && prod_pkg_key_count[prod_pkg_key] >= 1)) mapper.setValue(diff, 'prod_pkg_key', _.uuid())
            Object.assign(data, diff)
            dataUpdater.updateDetail(options.objApiName, data.dataIndex, diff)
        })
    }





    async batchAddDetailActionEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.batchAdd.end'")
        this._updateContext(options)
        return await this.forceTriggerPolicyMatch(pluginExecResult, options)
    }



    async afterBatchAddDetailAction(pluginExecResult, options) {
        console.log("price policy plugin execute 'md.batchAdd.after'")
        this._updateContext(options)
    }





    async bomQueryPriceEnd(pluginExecResult, options) {
        console.log("price policy plugin execute 'bom.queryBomPrice.end'")
        let { preData } = pluginExecResult || {}
        let { triggerType, changedFieldsMap, dataGetter, dataUpdater } = options || {}
        this._updateContext(options)
        if (triggerType !== 'reconfiguration') {
            this.backupObjectData()
            return preData
        }
        let proxy = await this._require_proxy()
        if (!proxy) {
            this.backupObjectData()
            return preData
        }
        if (proxy.common.plugin_public.is_data_scarcity(this.context)
            || proxy.coupon.utility.isUsingPolicyExclusiveCoupon(this.context)) return preData
        // changedFieldsMap => objectDataDiffMap
        let objectDataDiffMap = {}
        _.each(changedFieldsMap, (map, objectApiName) => {
            let diffMap = (objectDataDiffMap[objectApiName] = objectDataDiffMap[objectApiName] || {})
            _.each(map, (fields, index) => {
                let data = dataGetter.getData(objectApiName, index)
                if (data && fields) diffMap[index] = fields.reduce((res, field_name) => {
                    res[field_name] = data[field_name]
                    return res
                }, {})
            })
        })

        let context = this.context
        context.objectDataDiffMap = {}
        context.topChangeInfo = objectDataDiffMap || {}
        let mainObjectData = proxy.common.utility.getMainObjectData(context.objectDataMap, context.mainObjectApiName)
        // TODO: 这里注意若未触发价格政策匹配，context里topChangeInfo和objectDataDiffMap未清空，但手动触发价格政策逻辑处理了，所以没事
        if (this.markForOnceMatch({ dataGetter, dataUpdater }, mainObjectData, { objectDataDiffMap })) {
            this.backupObjectData()
            return
        }

        let token = `policy.bom.queryBomPrice.end.${_.uuid()}`
        context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            // TODO: node
            let res = await proxy.price_policy.triggerMatchByDataDiffMap(null, context)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            this._updateDataWithContext(context, dataUpdater, dataGetter)
            // 更新只读必填状态
            proxy.common.plugin_public.resetPolicyReadonlyFields({ context, dataUpdater })
            await this.notifyAfterPolicyEvent(context, options)
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
        }
    }





    async afterChangeGifts(pluginExecResult, options) {
        let { object_data, rule, gifts, dataUpdater, dataGetter } = options
        this._updateContext(options)
        let proxy = await this._require_proxy()
        if (!proxy) return this.context

        let context = this.context
        let token = `policy.after.change.gift.${_.uuid()}`
        context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            await proxy.price_policy.afterChangeProducts(null, context, object_data, rule, gifts)
            proxy.common.plugin_public.resetPolicyReadonlyFields({ context, dataUpdater })
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            this._updateDataWithContext(context, dataUpdater, dataGetter)
            return context
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            // TODO: 可以不存草稿么？
            this.tryToSaveDraftForOperationError(options.formApis, error)
        }
    }


    async changePricePolicy(pluginExecResult, options) {
        let { object_data, policy, dataUpdater, dataGetter } = options
        this._updateContext(options)
        let context = this.context
        let proxy = await this._require_proxy()
        if (!proxy) return { context, object_data }

        let token = `change.policy.${_.uuid()}`
        context.token = token
        this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).showLoading(token, null, this.context.pageId)
        try {
            let res = await proxy.price_policy.changePricePolicy(null, context, object_data, policy.selected ? null : policy)
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (res && res.changePolicyFailure) this.pluginService.api.showToast(fsapi.i18n.get("ava.change.price.policy.with.target.fail.prompt"))
            this._updateDataWithContext(context, dataUpdater, dataGetter)
            await this.notifyAfterPolicyEvent(context, options)
        } catch (error) {
            this.pluginService.api.loadingManager && this.pluginService.api.loadingManager(void 0, this.context.pageId).hideLoading(token, this.context.pageId)
            context.token = null
            if (typeof error !== 'object' || error.name !== 'IgnoreError') {
                let str = stringifyError(error)
                if (validString(str)) this.pluginService.api.showToast(str)
            }
            this.tryToSaveDraftForOperationError(options.formApis, error)
        }

        object_data = this.getCurrentData(object_data, context, proxy)

        return { context, object_data }
    }





    apply() {
        let { price_policy } = this.pluginParam.bizStateConfig || {}
        dhtWeLogService && dhtWeLogService.dhtWxLog && dhtWeLogService.dhtWxLog({
            "source": 'otc-dht-price-policy-plugin',
            "extends_data": { price_policy, action: 'apply' },
            "sub_module": 'object_form',
            "key": 'dht-to-form'
        })
        
        fsapi.stat.kLog("sfa", 'price_policy', JSON.stringify({ price_policy, action: 'apply' }))
        
        if (Boolean(Number(price_policy)) === false) return []
        return [{
            event: 'pluginService.use.after',
            functional: getEventAsyncFuncWithPerformance(this.afterRegisterPlugins.bind(this), 'pluginService.use.after', { subModule: this.pluginParam && this.pluginParam.pluginApiName, biz: 'ava_object_form', module: 'sfa' }),
        }, {
            event: "form.render.before",
            functional: getEventAsyncFuncWithPerformance(this.beforeFormRender.bind(this), 'form.render.before', { subModule: this.pluginParam && this.pluginParam.pluginApiName, biz: 'ava_object_form', module: 'sfa' }),
        }, {
            event: "form.render.end",
            functional: this.handleLogicAfterRenderEnd.bind(this),
        }, {
            event: "field.edit.after",
            functional: this.afterFieldChanged.bind(this)
        }, {
            event: "field.edit.end",
            functional: this.changeFieldEnd.bind(this)
        }, {
            event: "md.render.before",
            functional: this.beforeMdRender.bind(this),
        }, {
            event: "md.item.render.before.sync",
            functional: this.beforeMdItemRender.bind(this),
        }, {
            event: "form.submit.before",
            functional: this.handleLogicBeforeSubmit.bind(this),
        }, {
            event: "md.del.end",
            functional: this.deleteDetailActionEnd.bind(this)
        }, {
            event: "md.del.after",
            functional: this.afterDeleteDetailAction.bind(this)
        }, {
            event: "md.add.end",
            functional: this.addDetailActionEnd.bind(this)
        }, {
            event: "md.add.after",
            functional: this.afterAddDetailAction.bind(this)
        }, {
            event: "md.clone.after",
            functional: this.afterCloneDetailAction.bind(this)
        }, {
            event: "md.clone.end",
            functional: this.cloneDetailActionEnd.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.batchAddDetailActionEnd.bind(this)
        }, {
            event: "md.batchAdd.after",
            functional: this.afterBatchAddDetailAction.bind(this)
        }, {
            event: "form.saveState.before.sync",
            functional: this.onSaveInstanceState.bind(this)
        }, {
            // 价格政策控件定制 -- 更换赠品
            event: "policy.after.change.gifts",
            functional: this.afterChangeGifts.bind(this)
        }, {
            // 价格政策控件定制 -- 更换政策
            event: "change.policy",
            functional: this.changePricePolicy.bind(this)
        }, {
            // 价格政策控件定制 -- 获取context
            event: "price.policy.query.context",
            functional: this.queryContext.bind(this),
        }, {
            // 价格政策控件定制 -- 同步获取context
            event: "price.policy.sync.query.context",
            functional: this.syncQueryContext.bind(this),
        }, {
            // 价格政策控件定制 -- 同步更新context
            event: "price.policy.sync.context",
            functional: this.syncContext.bind(this)
        }, {
            // 监听bom取价结束，二次配置bom后，触发匹配逻辑
            event: "bom.queryBomPrice.end",
            functional: this.bomQueryPriceEnd.bind(this)
        }, {
            event: "coupon.auto.use.end.after.page.ready",
            functional: this.pageReadyAfterAutoUseCoupon.bind(this)
        }]
    }
}

