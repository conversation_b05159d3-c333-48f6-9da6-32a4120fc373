import {isEmpty} from "../../pluginbase-ava/package/pluginutils";

/**
 * 计算额外调整，目的是保证整单折扣不变
 * 触发场景：明细添加、删除、编辑、行复制、bom二次配置
 */

class CalculateDynamicAmount {

    constructor() {
        this.lastEditFieldName = null;//上一次编辑字段的apiName
        this.lastMasterData = null;//产品合计变更前的主对象数据
        this.context = null;//插件上下文
    }

    init(context) {
        this.context = context;
        this.lastEditFieldName = null;//上一次编辑字段的apiName
        this.lastMasterData = null;//产品合计变更前的主对象数据
    }

    /**
     * 最后修改的字段是否是整单折扣
     * @return {boolean}
     */
    isLastEditDiscount() {
        return this.lastEditFieldName === 'discount';
    }

    /**
     * 记录最后修改的字段：额外调整或整单折扣。
     * @param fieldName 修改的字段
     */
    recordLastEditFieldName(fieldName) {
        this.lastEditFieldName = fieldName;
    }

    /**
     * 记录上次主对象数据
     * @param options
     */
    recordLastMasterData(options) {
        this.lastMasterData = options && options.dataGetter && options.dataGetter.getMasterData && options.dataGetter.getMasterData();
    }

    /**
     * 触发计算额外调整
     * @param options
     * @return {Promise}
     */
    async calculateDynamicAmount(options) {
        if (this.isLastEditDiscount()) {
            let {dataGetter, dataUpdater, formApis} = options;
            let master = dataGetter.getMasterData();
            let details = dataGetter.getDetail('SalesOrderProductObj');
            let lastDiscount = this.lastMasterData && this.lastMasterData.discount;
            let result = this.context && this.context.requestApi
                && this.context.requestApi.calculateDynamicAmount && await this.context.requestApi.calculateDynamicAmount(master, details, lastDiscount);
            if (!isEmpty(result)) {
                let dynamicAmount = result.dynamicAmount;
                dataUpdater.updateMaster({dynamic_amount: dynamicAmount})
                await formApis.triggerCalAndUIEvent({
                    objApiName: 'SalesOrderObj',
                    changeFields: ['dynamic_amount'],
                    triggerUiField: 'dynamic_amount'
                });
            }
        }
    }
}

export default new CalculateDynamicAmount();