import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import {MasterFieldEdit} from "./MasterFieldEdit";
import log from "../../pluginbase-ava/package/log";
import calculateDynamicAmount from "./calculateDynamicAmount";
import DynamicAmountApi from './DynamicAmountApi'
import {isEmpty} from "../../pluginbase-ava/package/pluginutils";

export default class DynamicAmount {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        let context = {
            bizStateConfig: this.bizStateConfig,
            pluginApi: new PluginApi(pluginService),
            requestApi: new DynamicAmountApi(pluginService.api.request),
        }
        this.masterFieldEdit = new MasterFieldEdit(context);
        calculateDynamicAmount.init(context);
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditAfter(pluginExecResult, options);
        } else {
            return this.recordLastMasterData(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditEnd(pluginExecResult, options);
        } else {
            let updateMaster = options && options.calUiRst && options.calUiRst.updateMaster;
            let updateMasterFields = isEmpty(updateMaster) ? [] : Object.keys(updateMaster);
            if (updateMasterFields.includes('product_amount')) {//修改了产品合计，计算额外调整
                return this.calculateDynamicAmount(pluginExecResult, options);
            }
        }
    }

    recordLastMasterData(pluginExecResult, options) {
        return calculateDynamicAmount.recordLastMasterData(options);
    }

    async calculateDynamicAmount(pluginExecResult, options) {
        await calculateDynamicAmount.calculateDynamicAmount(options);
    }

    apply() {
        let dynamicAllowAmortize = this.bizStateConfig.dynamicAllowAmortize();
        return dynamicAllowAmortize ? [
            {
                event: "field.edit.after",
                functional: this.fieldEditAfter.bind(this)
            }, {
                event: "field.edit.end",
                functional: this.fieldEditEnd.bind(this)
            }, {
                event: "md.batchAdd.before",
                functional: this.recordLastMasterData.bind(this)
            }, {
                event: "md.batchAdd.end",
                functional: this.calculateDynamicAmount.bind(this)
            }, {
                event: "md.clone.after",
                functional: this.recordLastMasterData.bind(this)
            }, {
                event: "md.clone.end",
                functional: this.calculateDynamicAmount.bind(this)
            }, {
                event: "md.del.after",
                functional: this.recordLastMasterData.bind(this)
            }, {
                event: "md.del.end",
                functional: this.calculateDynamicAmount.bind(this)
            }, {
                event: "bom.reconfiguration.queryBomPrice.before",
                functional: this.recordLastMasterData.bind(this)
            }, {
                event: "bom.reconfiguration.end",
                functional: this.calculateDynamicAmount.bind(this)
            }
        ] : [];
    }
}