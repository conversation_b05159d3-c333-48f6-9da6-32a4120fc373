import {request} from "../../pluginbase-ava/package/pluginutils";

export default class DynamicAmountApi {
    constructor(http) {
        this.http = http;
    }

    calculateDynamicAmount(master, details, beforeDiscount) {
        return request(this.http, {
            url: `FHE/EM1ANCRM/API/v1/object/price_policy/service/calculate_dynamic_amount`,
            data: {master, details, beforeDiscount}
        }).then(rst => {
            return rst || {};
        });
    }
}