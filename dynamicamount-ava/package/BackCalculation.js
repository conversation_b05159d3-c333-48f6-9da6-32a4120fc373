import {
    add,
    divide,
    formatDataDecimalPlaces,
    formatValueDecimalPlaces,
    getPercentileFieldDecimalPlacesFromDescribe,
    isEmpty,
    multiply
} from "../../pluginbase-ava/package/pluginutils";

export class BackCalculation {

    constructor(context) {
        let {bizStateConfig} = context;
        this.bizStateConfig = bizStateConfig;
    }

    orderAmountEditAfter(options) {
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        let isReverseOrderDiscount = this.bizStateConfig.isReverseOrderDiscount();
        let dynamicAllowAmortize = this.bizStateConfig.dynamicAllowAmortize();
        if (isOpenManualChangePrice || !dynamicAllowAmortize) {
            return;
        }
        let {masterObjApiName, dataGetter, changeData} = options;
        if (isEmpty(changeData)) {
            return;
        }
        let objectDescribe = dataGetter.getDescribe(masterObjApiName);
        let orderAmountField = objectDescribe && objectDescribe.fields && objectDescribe.fields['order_amount'];
        let defaultValue = orderAmountField && orderAmountField.default_value;
        let isMatch = this.isFieldFormulaMatchedDefault('product_amount', 'dynamic_amount', defaultValue, '+');
        if (!isMatch) {
            return;
        }
        let masterData = dataGetter.getMasterData();
        let {product_amount, order_amount: _order_amount, dynamic_amount} = masterData || {};
        if (isEmpty(product_amount)) {
            product_amount = '0';
        }
        if (isEmpty(_order_amount)) {
            _order_amount = 0;
        }
        if (isEmpty(dynamic_amount)) {
            dynamic_amount = '0';
        }
        let {order_amount} = changeData;
        if (isEmpty(order_amount)) {
            order_amount = '0';
        }
        let productAmount = parseFloat(product_amount);
        let oldOrderAmount = parseFloat(_order_amount);
        let newOrderAmount = parseFloat(order_amount);
        let dynamicAmount = parseFloat(dynamic_amount);
        let newDynamicAmount = add(add(newOrderAmount, -oldOrderAmount), dynamicAmount);
        let newDiscount;
        if (productAmount !== 0 && isReverseOrderDiscount) {
            let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe('discount', objectDescribe);
            newDiscount = multiply(divide(newOrderAmount, productAmount), 100);
            newDiscount = formatValueDecimalPlaces(newDiscount, decimalPlaces);
        }
        let updateData = Object.assign({
            dynamic_amount: "" + newDynamicAmount
        }, (!isEmpty(newDiscount)) && {discount: "" + newDiscount})
        formatDataDecimalPlaces(updateData, objectDescribe, ['discount']);
        Object.assign(changeData, updateData);
    }

    discountEditAfter(options) {
        let dynamicAllowAmortize = this.bizStateConfig.dynamicAllowAmortize();
        if (!dynamicAllowAmortize) {
            return;
        }
        let {dataGetter, changeData, masterObjApiName, fieldName} = options;
        if (isEmpty(changeData)) {
            return;
        }
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(masterObjApiName);
        let discountField = objectDescribe && objectDescribe.fields && objectDescribe.fields[fieldName];
        let defaultValue = discountField && discountField.default_value;
        let matched = this.isFieldFormulaMatchedDefault('order_amount', 'product_amount', defaultValue, '/');
        if (!matched) {
            return;
        }
        let masterData = dataGetter.getMasterData();
        let {product_amount, order_amount, dynamic_amount} = masterData || {};
        if (isEmpty(product_amount)) {
            product_amount = 0;
        }
        if (isEmpty(order_amount)) {
            order_amount = 0;
        }
        if (isEmpty(dynamic_amount)) {
            dynamic_amount = 0;
        }
        let {discount} = changeData;
        if (isEmpty(discount)) {
            discount = 0;
        }
        let productAmount = parseFloat(product_amount);
        let orderAmount = parseFloat(order_amount);
        let dynamicAmount = parseFloat(dynamic_amount);
        let newDiscount = divide(parseFloat(discount), 100);
        let newOrderAmount = multiply(productAmount, newDiscount);
        let newDynamicAmount = add(add(newOrderAmount, -orderAmount), dynamicAmount)
        let updateData = {
            order_amount: "" + newOrderAmount,
            dynamic_amount: "" + newDynamicAmount
        };
        formatDataDecimalPlaces(updateData, objectDescribe);
        Object.assign(changeData, updateData);
    }

    isFieldFormulaMatchedDefault(variable1, variable2, defaultFormula, operator = '*') {
        let symbol_$ = '$';
        return defaultFormula === `${symbol_$}${variable1}${symbol_$}${operator}${symbol_$}${variable2}${symbol_$}`;
    }
}