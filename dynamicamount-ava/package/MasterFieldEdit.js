import {BackCalculation} from './BackCalculation';
import calculateDynamicAmount from "./calculateDynamicAmount";

export class MasterFieldEdit {

    constructor(context) {
        let {pluginApi, requestApi} = context;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
        this.backCalculation = new BackCalculation(context);
    }

    masterFieldEditAfter(pluginExecResult, options) {
        let {fieldName} = options;
        if (fieldName === 'order_amount') {
            return this.backCalculation.orderAmountEditAfter(options);
        } else if (fieldName === 'discount') {
            return this.backCalculation.discountEditAfter(options);
        }
    }

    masterFieldEditEnd(pluginExecResult, options) {
        let {fieldName} = options;
        if (['dynamic_amount', 'discount'].includes(fieldName)) {
            return calculateDynamicAmount.recordLastEditFieldName(fieldName);//记录最后修改的字段
        }
    }
}