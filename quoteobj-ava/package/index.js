import log from "../../pluginbase-ava/package/log";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import objPluginChecker from "../../pluginbase-ava/package/objPluginChecker";
import {DetailFieldEdit} from "./DetailFieldEdit";
import {QuoteTestCalculate} from "./QuoteTestCalculate";
import {SelectHistoryQuote} from "./SelectHistoryQuote";

export default class QuoteObj {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        let context = {
            bizStateConfig: new BizStateConfig(bizStateConfig, pluginService.api.getPlugins()),
            pluginApi: new PluginApi(pluginService),
        }
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.testCalculate = new QuoteTestCalculate(context);
        this.selectHistoryQuote = new SelectHistoryQuote(context);
    }

    pluginServiceUseAfter(pluginExecResult, options) {
        let {plugins, ignore} = options;
        let result = objPluginChecker.check('QuoteObj', plugins, ignore);
        if (!result) {//检查不通过，则拦截页面加载
            return false;
        }
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEditAfter(pluginExecResult, options);
        } else {
            return this.detailFieldEdit.detailFieldEditAfter(pluginExecResult, options);
        }
    }

    masterFieldEditAfter(pluginExecResult, options) {
        let {changeData, fieldName} = options;
        if (fieldName === 'account_id') {
            //删除更换客户，清空商机逻辑，通过配置过滤条件控制
            // let accountId = changeData.account_id;
            // if (!(typeof accountId === 'undefined')) {
            //     Object.assign(changeData, {
            //         opportunity_id: null,
            //         opportunity_id__r: null,
            //         new_opportunity_id: null,
            //         new_opportunity_id__r: null
            //     });
            // }
        }
    }

    mdRenderBefore(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let preMdBottomComs = preData && preData.mdBottomComs || [];
        let mdCountCmpt = preMdBottomComs && preMdBottomComs.find(it => it.name === 'paas_mdcount');
        let appendCmpt = [];
        if (!mdCountCmpt) {
            appendCmpt.push({name: 'paas_mdcount'});
        }
        appendCmpt.push({name: 'quotetestcalculate'});
        return Object.assign({}, preData, {mdBottomComs: [...preMdBottomComs, ...appendCmpt]});
    }

    mdItemRenderBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData && pluginExecResult.preData;
        let bellowTitleComs = preData && preData.bellowTitleComs || [];
        return Object.assign({}, preData, {
            bellowTitleComs: [...bellowTitleComs, {name: "historyquote"}]
        });
    }

    async testCalculateAfter(pluginExecResult, options) {
        return await this.testCalculate.testCalculateAfter(pluginExecResult, options);
    }

    async selectHistoryQuoteAfter(pluginExecResult, options) {
        return await this.selectHistoryQuote.selectHistoryQuoteAfter(pluginExecResult, options);
    }

    apply() {
        return [
            {
                event: 'pluginService.use.after',
                functional: this.pluginServiceUseAfter.bind(this)
            },
            {
                event: 'field.edit.after',
                functional: this.fieldEditAfter.bind(this)
            },
            {
                event: "md.render.before",
                functional: this.mdRenderBefore.bind(this),
            },
            {
                event: "md.item.render.before.sync",
                functional: this.mdItemRenderBeforeSync.bind(this),
            },
            {
                event: "QuoteObj.testCalculate.after",
                functional: this.testCalculateAfter.bind(this),
            },
            {
                event: "QuoteObj.selectHistoryQuote.after",
                functional: this.selectHistoryQuoteAfter.bind(this),
            }
        ]
    }
}
