import {divide, each, uuid} from "../../pluginbase-ava/package/pluginutils";

export class SelectHistoryQuote {

    constructor(context) {
        let {bizStateConfig, pluginApi} = context || {};
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
    }

    selectHistoryQuoteAfter(pluginExecResult, options) {
        return this.historyQuoteByQuoteLines(options)
    }

    historyQuoteByQuoteLines(options) {
        let {dataIndex, quoteLinesData, dataGetter, dataUpdater, formApis} = options;
        let detailDataList = dataGetter.getDetail('QuoteLinesObj');
        let objectData = detailDataList && detailDataList.length && detailDataList.find(it => {
            return it.dataIndex === dataIndex;
        });
        if (objectData && quoteLinesData) {
            function deleteFields(objData) {
                if (objData) {
                    ['_id', 'name', 'quote_id', 'quote_id__r'].forEach(it => {
                        delete objData[it];
                    });
                }
            }

            let isOpenCPQ = this.bizStateConfig.isOpenCpq();
            let isOpenSimpleCPQ = this.bizStateConfig.isOpenSimpleCpq();
            if (isOpenCPQ || isOpenSimpleCPQ) {//开启了CPQ，处理子件数据
                let srcDataList = this.pluginApi.runPluginSync('bom.getSubDetailsFromPkg.sync', Object.assign(options, {
                    objApiName: 'QuoteLinesObj',
                    dataIndex
                }));
                let {prod_pkg_key: orgDstProdPkgKey, quantity: pkgQuantity} = quoteLinesData;
                delete quoteLinesData['prod_pkg_key'];
                delete quoteLinesData['root_prod_pkg_key'];
                let newProdPkgKey = uuid();
                dataUpdater.updateDetail('QuoteLinesObj', dataIndex, {
                    prod_pkg_key: newProdPkgKey,
                    root_prod_pkg_key: newProdPkgKey
                });
                let dstDataList = quoteLinesData['key_sub_lines'];
                if (dstDataList && dstDataList.length) {
                    //此处逻辑后续迁移到bom插件
                    dstDataList.forEach(dstData => {
                        let {quantity: subQuantity} = dstData;
                        let unitQuantity = pkgQuantity == 0 ? 0 : divide(subQuantity, pkgQuantity);
                        Object.assign(dstData, {amount_origin_subBom: unitQuantity});
                    });
                    let deleteDataIndexList = [];
                    srcDataList && srcDataList.length && srcDataList.forEach(srcDetail => {
                        let {bom_id: srcBomId} = srcDetail;
                        let dstIndex = dstDataList.findIndex(dstDetail => dstDetail.bom_id === srcBomId);
                        if (dstIndex === -1) {//未在目标中找到，则删除源数据
                            deleteDataIndexList.push(srcDetail.dataIndex);
                        } else {//在目标中找到了，更新源数据
                            let dstDetail = dstDataList.splice(dstIndex, 1)[0];
                            deleteFields(dstDetail);
                            let isDirectSubProduct = (dstDetail.parent_prod_pkg_key === orgDstProdPkgKey);//是否母件的直接子件
                            let updateData = Object.assign(dstDetail, {root_prod_pkg_key: newProdPkgKey}, isDirectSubProduct && {parent_prod_pkg_key: newProdPkgKey});
                            dataUpdater.updateDetail('QuoteLinesObj', srcDetail.dataIndex, updateData);
                        }
                    });
                    if (dstDataList && dstDataList.length) {//目标中新增的子件
                        let addNewDataList = dstDataList.map(dstDetail => {
                            deleteFields(dstDetail);
                            let isDirectSubProduct = (dstDetail.parent_prod_pkg_key === orgDstProdPkgKey);//是否母件的直接子件
                            return Object.assign(dstDetail, {
                                dataIndex: formApis.createNewDataIndex(),
                                root_prod_pkg_key: newProdPkgKey
                            }, isDirectSubProduct && {parent_prod_pkg_key: newProdPkgKey})
                        });
                        dataUpdater.add(addNewDataList);
                    }
                    if (deleteDataIndexList && deleteDataIndexList.length) {//有要删除的源子件数据，则删除
                        dataUpdater.del('QuoteLinesObj', deleteDataIndexList);
                    }
                } else {//如果目标中没有子件则将源中的子件清空
                    if (srcDataList && srcDataList.length) {
                        let deleteDataIndexList = srcDataList.map(it => it.dataIndex);
                        dataUpdater.del('QuoteLinesObj', deleteDataIndexList);
                    }
                }
            }
            deleteFields(quoteLinesData);
            dataUpdater && dataUpdater.updateDetail('QuoteLinesObj', dataIndex, quoteLinesData);
            //最后触发计算，计算时全量计算，非自定义字段不参与计算
            let objectDescribe = dataGetter.getDescribe('QuoteLinesObj');
            let fields = objectDescribe && objectDescribe.fields;
            let fieldApiNames = [];
            let filterCalcFields = [];
            each(fields, (fieldDesc, fieldApiName) => {
                fieldApiNames.push(fieldApiName);
                let {define_type} = fieldDesc || {};
                if (define_type !== 'custom') {//非自定义字段不参与计算
                    filterCalcFields.push(fieldApiName)
                }
            });
            return formApis.triggerCalAndUIEvent({
                objApiName: 'QuoteLinesObj',
                modifiedDataIndexs: [dataIndex],
                changeFields: fieldApiNames,
                filterFields: {QuoteLinesObj: filterCalcFields}
            })
        }
    }
}