import {
    add,
    divide,
    formatValueDecimalPlaces,
    getPercentileFieldDecimalPlacesFromDescribe,
    getPriceFieldApiName,
    getSalesPriceFieldApiName,
    getSubtotalFieldApiName,
    isEmpty,
    multiply
} from "../../pluginbase-ava/package/pluginutils";

export class BackCalculation {

    constructor(context) {
        this.bizStateConfig = context.bizStateConfig;
    }

    sellingPriceEditAfter(options) {
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        if (isOpenManualChangePrice) {
            return;
        }
        let {dataGetter, changeData, dataIndex, objApiName, fieldName: sellingPriceFieldName} = options;
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let salesPriceFieldApiName = getSalesPriceFieldApiName(objApiName);
        let salesPrice = tempData[salesPriceFieldApiName];
        if (isEmpty(salesPrice)) {
            return;
        }
        salesPrice = parseFloat(salesPrice);
        if (salesPrice === 0) {//报价为0，不反算额外折扣
            return;
        }
        let sellingPrice = tempData[sellingPriceFieldName];
        if (isEmpty(sellingPrice)) {
            sellingPrice = '0';
        }
        sellingPrice = parseFloat(sellingPrice);
        let extraDiscount = multiply(divide(sellingPrice, salesPrice), 100);
        let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe('extra_discount', objectDescribe);
        extraDiscount = extraDiscount ? formatValueDecimalPlaces(extraDiscount, decimalPlaces) : 0;
        Object.assign(changeData, {extra_discount: "" + extraDiscount});
    }

    salesAmountEditAfter(options) {
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        if (isOpenManualChangePrice) {
            return;
        }
        let {dataGetter, changeData, dataIndex, objApiName, fieldName: salesAmountFieldName} = options;
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let quantity = tempData['quantity'];
        if (isEmpty(quantity)) {
            return;
        }
        quantity = parseFloat(quantity);
        if (quantity === 0) {
            return;
        }
        //数量不为0，反算销售单价
        let salesAmount = tempData[salesAmountFieldName];
        if (isEmpty(salesAmount)) {
            salesAmount = '0';
        }
        salesAmount = parseFloat(salesAmount);
        let sellingPrice = divide(salesAmount, quantity);
        let updateData = this.reverseDiscountBySalesPrice(tempData, sellingPrice, options);
        Object.assign(changeData, updateData);
    }

    extraDiscountAmountEditAfter(options) {
        let {dataGetter, changeData, dataIndex, objApiName, fieldName: extraDiscountAmountFieldName} = options;
        let salesPriceFieldApiName = getSalesPriceFieldApiName(objApiName);
        let totalAmountFieldApiName = getSubtotalFieldApiName(objApiName);
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let quantity = tempData['quantity'];
        if (isEmpty(quantity)) {
            return;
        }
        quantity = parseFloat(quantity);//数量
        let salesPrice = tempData[salesPriceFieldApiName];
        if (isEmpty(salesPrice)) {
            return;
        }
        salesPrice = parseFloat(salesPrice);//报价
        let totalAmount = multiply(quantity, salesPrice);//报价小计
        if (totalAmount === 0) {
            return;
        }
        let extraDiscountAmount = tempData[extraDiscountAmountFieldName];
        if (isEmpty(extraDiscountAmount)) {
            extraDiscountAmount = '0';
        }
        extraDiscountAmount = parseFloat(extraDiscountAmount);//额外折扣小计
        //报价小计不为0，反算额外折扣
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let totalAmountField = objectDescribe && objectDescribe.fields && objectDescribe.fields[totalAmountFieldApiName];
        let totalAmountFieldDecimalPlaces = totalAmountField && totalAmountField.decimal_places || 0;
        totalAmount = totalAmount ? formatValueDecimalPlaces(totalAmount, totalAmountFieldDecimalPlaces) : 0;
        let extraDiscount;
        if (this.bizStateConfig.isOpenPricePolicy()) {
            // https://wiki.firstshare.cn/pages/viewpage.action?pageId=206062451
            // 按开价格政策后，按新的公式反算，即：【额外折扣】= 1 -【额外折扣小计 / ( 报价 * 数量 )】
            //1. 校验 **额外折扣小计** 的公式是否符合： `$sales_price$*$quantity$*(1-$extra_discount$)`
            let extraDiscountAmountField = objectDescribe && objectDescribe.fields && objectDescribe.fields[extraDiscountAmountFieldName]
            let extraDiscountAmountFieldDefaultValue = extraDiscountAmountField && extraDiscountAmountField.default_value
            if (extraDiscountAmountFieldDefaultValue === '$sales_price$*$quantity$*(1-$extra_discount$)'
                || extraDiscountAmountFieldDefaultValue === '$total_amount$*(1-$extra_discount$)') {
                //2. 校验通过后，反算额外折扣的值  $extra_discount$ = 1 - $extra_discount_amount$ / ( $sales_price$ * $quantity$ )
                extraDiscount = multiply(add(1, -divide(extraDiscountAmount, totalAmount)), 100);
            }
        } else {
            extraDiscount = multiply(add(1, -divide(extraDiscountAmount, totalAmount)), 100);
        }
        let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe('extra_discount', objectDescribe);
        extraDiscount = extraDiscount ? formatValueDecimalPlaces(extraDiscount, decimalPlaces) : 0;
        Object.assign(changeData, {extra_discount: "" + extraDiscount});
    }

    totalDiscountEditAfter(options) {
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        if (isOpenManualChangePrice) {
            return;
        }
        let {dataGetter, changeData, dataIndex, objApiName, fieldName: totalDiscountFieldName} = options;
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let priceFieldName = getPriceFieldApiName(objApiName);
        let price = tempData[priceFieldName];
        if (isEmpty(price)) {
            return;
        }
        price = parseFloat(price);
        if (price === 0) {
            return;
        }
        //价格不为0，反算销售单价
        let totalDiscount = tempData[totalDiscountFieldName];
        if (isEmpty(totalDiscount)) {
            totalDiscount = '0';
        }
        totalDiscount = parseFloat(totalDiscount);//报价
        let sellingPrice = multiply(divide(totalDiscount, 100), price);
        let updateData = this.reverseDiscountBySalesPrice(tempData, sellingPrice, options);
        Object.assign(changeData, updateData);
    }

    reverseDiscountBySalesPrice(objectData, sellingPrice, options) {
        let {dataGetter, objApiName} = options;
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let salesPriceFieldApiName = getSalesPriceFieldApiName(objApiName);
        let salesPrice = objectData[salesPriceFieldApiName];
        if (isEmpty(salesPrice)) {
            salesPrice = 0;
        }
        salesPrice = parseFloat(salesPrice);
        if (salesPrice === 0) {//报价为0，不反算额外折扣，直接回填销售单价
            return {selling_price: "" + sellingPrice};
        } else {
            let sellingPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields['selling_price'];
            let sellingPriceFieldDecimalPlaces = sellingPriceField && sellingPriceField.decimal_places || 0;
            sellingPrice = sellingPrice ? formatValueDecimalPlaces(sellingPrice, sellingPriceFieldDecimalPlaces) : 0;
            let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe("extra_discount", objectDescribe);
            let extraDiscount = multiply(divide(sellingPrice, salesPrice), 100);
            extraDiscount = extraDiscount ? formatValueDecimalPlaces(extraDiscount, decimalPlaces) : 0;
            return {extra_discount: "" + extraDiscount};
        }
    }
}