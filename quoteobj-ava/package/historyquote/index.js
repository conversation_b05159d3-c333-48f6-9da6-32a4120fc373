import cdnres from "../../../objformmain/libs/cdnres";
import fsapi from "fs-hera-api";
import BizStateConfig from "../../../pluginbase-ava/package/BizStateConfig";

Component({

    data: {
        dEnableHistoryQuote: false,
        dHistoryQuoteImage: cdnres.getRes('images/icon_quote_history.png')
    },

    methods: {
        selectHistoryQuote() {
            let {formContext, objectData} = this;
            let masterData = formContext && formContext.getMasterData() || {};
            let {price_book_id, product_id, record_type, dataIndex} = objectData || {};
            fsapi.page.utilOpen({
                name: 'ava://object_list/object_list/pages/historyquote/historyquote',
                params: {
                    account_id: masterData.account_id,
                    price_book_id,
                    product_id,
                    record_type,
                    objectData: objectData
                },
                onSuccess: (res) => {
                    let result = res && res.dataList;
                    if (result && result.object_describe_api_name === 'QuoteLinesObj') {
                        formContext && formContext.catchRunPluginHook("QuoteObj.selectHistoryQuote.after", {
                            dataIndex: dataIndex,
                            quoteLinesData: result
                        });
                    }
                },
                onFail: (error) => {
                    fsapi.util.showToast(error)
                }
            });
        },

        getBizStateConfig() {
            let bizStateConfig = this.bizStateConfig;
            if (bizStateConfig == null) {
                let formContext = this.formContext;
                let pluginService = formContext && formContext.getPluginService && formContext.getPluginService();
                bizStateConfig = new BizStateConfig(pluginService && pluginService.bizStateConfig, pluginService.api.getPlugins());
                this.bizStateConfig = bizStateConfig;
            }
            return bizStateConfig;
        },
    },

    lifetimes: {
        attached() {
            this.triggerEvent("getDataDetail", rst => {
                this.formContext = rst.formContext;
                this.objectData = rst.objectData;
                let pageOptions = this.formContext.getPageOptions();
                let isEdit = pageOptions && pageOptions.action && pageOptions.action.toLowerCase() === 'edit';
                let bizStateConfig = this.getBizStateConfig();
                let isOpenAttribute = bizStateConfig.isOpenAttribute();
                let enforcePriority = bizStateConfig.isOpenPriceBookPriority();
                let openPricePolicy = bizStateConfig.isOpenPricePolicy();
                let openQuoteHistoryPrice = bizStateConfig.isOpenQuoteHistoryPrice();
                let enableHistoryQuote = !isEdit && !isOpenAttribute && !enforcePriority && !openPricePolicy && openQuoteHistoryPrice
                this.setData({
                    dEnableHistoryQuote: enableHistoryQuote
                })
            })
        }
    }
})