/*
 * @Description: <%= description %>
 * @Author: <%= author %>
 */
class PluginName {

    /**
     * 初始化
     *
     * @param {object} pluginService
     * @param {object} pluginParam 
     */
    constructor(pluginService, pluginParam) {
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
    }

    /**
     * 注册hook
     *
     * @param {object} pluginService
     * @param {object} pluginParam 
     */
    apply(pluginService, pluginParam) {
        return this.getHook(pluginService, pluginParam);
    }

    /**
     * 获取插件配置
     *
     * @param {object} pluginService
     * @param {object} pluginParam 
     */
    getHook(pluginService, pluginParam) {
        return [
            {
                /**
                 * 监听的事件
                 */
                event: '',
                /**
                 * 事件处理函数
                 *
                 * @param {object} context
                 * @param {object} context.api 各种api
                 * @param {object} context.preData 前置插件执行结果
                 * @param {object} context.skipPlugin 停止后续插件执行
                 * @param {object} param 插件执行时的参数
                 */
                functional: (context, param) => {
                    
                }
            }
        ];
    }
}

export default PluginName;