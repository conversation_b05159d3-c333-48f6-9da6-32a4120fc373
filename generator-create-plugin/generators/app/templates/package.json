{"name": "<%= name %>", "version": "1.0.0", "description": "<%= description %>", "main": "dist/<%= main %>", "scripts": {"test": "", "dev": "webpack --mode development --config ./build/webpack.dev.config.js --watch", "build": "webpack --mode production --config ./build/webpack.product.config.js", "release": "webpack --mode production --config ./build/webpack.product.config.js && npm publish --registry https://registry-npm.firstshare.cn"}, "repository": {"type": "git", "url": "http://git.firstshare.cn/bigfe/plugin_log.git"}, "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "^7.16.8", "@babel/core": "^7.17.7", "@babel/preset-env": "^7.16.11", "@types/node": "^18.7.11", "babel-loader": "^8.2.3", "ts-loader": "^9.2.8", "typescript": "^4.6.4", "webpack": "^5.70.0", "webpack-cli": "^4.9.2"}}