'use strict';
const Generator = require('yeoman-generator');
const chalk = require('chalk');
const yosay = require('yosay');
const path = require('path');
const mkdirp = require('mkdirp');

module.exports = class extends Generator {
    prompting() {
        this.log(
            yosay(
                `Welcome to the hunky-dory ${chalk.red('generator-create-plugin')} generator!`
            )
        );

        const prompts = [
            {
                type: 'input',
                name: 'name',
                message: 'Please enter plugin name',
                default: `plugin-${new Date().getTime()}`
            },
            {
                type: 'input',
                name: 'author',
                message: 'Please enter the plugin owner',
                default: ''
            },
            {
                type: 'input',
                name: 'description',
                message: 'Please enter the plugin description',
                default: ''
            },
            {
                type: 'input',
                name: 'main',
                message: 'Please enter the plugin entry',
                default: 'index.js'
            }
        ];

        return this.prompt(prompts).then(props => {
            this.props = props;
        });
    }

    getProps() {
        return {
            name: this.props.name,
            author: this.props.author,
            description: this.props.description,
            main: this.props.main
        };
    }

    default() {
        console.log(this.destinationPath())
        if (path.basename(this.destinationPath()) !== this.props.name) {
            this.log(`\nYour generator must be inside a folder namedc ${this.props.name}\n I will automatically create this folder.\n`);

            mkdirp(this.props.name);
            this.destinationRoot(this.destinationPath(this.props.name));
        }
    }

    writing() { 
        this._writingByCopy();
        this._writingByCopyTpl();
        this._writingDir();
    }

    _writingByCopy() {
        let config = {
            'README.md': 'README.md',
            '_gitignore': '.gitignore',
        };

        this._writingHandle(config, 'copy');
    }

    _writingByCopyTpl() {
        let config = {
            'build/webpack.dev.config.js': 'build/webpack.dev.config.js',
            'build/webpack.product.config.js': 'build/webpack.product.config.js',
            'package.json': 'package.json',
            '_package-lock.json': 'package-lock.json',
            'src/index.js': `src/${this.props.main}`
        };

        this._writingHandle(config, 'copyTpl');
    }

    _writingHandle(config, type) {
        Object.keys(config).forEach(key => {
            if (type === 'copy') {
                this.fs.copy(
                    this.templatePath(key),
                    this.destinationPath(config[key])
                );
            }
            else if (type === 'copyTpl') {
                this.fs.copyTpl(
                    this.templatePath(key),
                    this.destinationPath(config[key]),
                    this.getProps()
                );
            }
        })
    }

    _writingDir() {
        let dirs = ['dist', 'docs'];

        dirs.forEach(dir => {
            mkdirp(dir);
        });
    }

    // 安装依赖
    install() {
        this.installDependencies();
    }
};
