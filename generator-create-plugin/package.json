{"name": "generator-create-plugin", "version": "0.0.4", "description": "", "homepage": "", "author": {"name": "liang", "email": "<EMAIL>", "url": ""}, "files": ["generators"], "main": "generators/app/index.js", "keywords": ["", "yeoman-generator"], "devDependencies": {"yeoman-test": "^1.7.0", "yeoman-assert": "^3.1.1", "eslint": "^6.6.0", "prettier": "^1.19.1", "husky": "^3.0.9", "lint-staged": "^9.4.3", "eslint-config-prettier": "^6.6.0", "eslint-plugin-prettier": "^3.1.1", "eslint-config-xo": "^0.27.2", "jest": "^26.1.0"}, "engines": {"npm": ">= 4.0.0"}, "dependencies": {"yeoman-generator": "^3.1.1", "chalk": "^2.1.0", "yosay": "^2.0.2"}, "jest": {"testEnvironment": "node"}, "lint-staged": {"*.js": ["eslint --fix", "git add"], "*.json": ["prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "eslintConfig": {"extends": ["xo", "prettier"], "env": {"jest": true, "node": true}, "rules": {"prettier/prettier": "error"}, "plugins": ["prettier"]}, "scripts": {"pretest": "eslint .", "test": "jest", "release": "npm publish --registry https://registry-npm.firstshare.cn"}, "repository": "https://git.firstshare.cn/bigfe/plugin-create.git", "license": "Apache-2.0"}