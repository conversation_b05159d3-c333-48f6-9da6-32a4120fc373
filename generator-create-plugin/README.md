# generator-create-plugin [![NPM version][npm-image]][npm-url] [![Build Status][travis-image]][travis-url] [![Dependency Status][daviddm-image]][daviddm-url]
> 

## Installation

First, install [Yeoman](http://yeoman.io) and generator-create-plugin using [npm](https://www.npmjs.com/) (we assume you have pre-installed [node.js](https://nodejs.org/)).

```bash
npm install -g yo
npm install -g generator-create-plugin
```

Then generate your new project:

```bash
yo create-plugin
```

## Getting To Know Yeoman

 * <PERSON><PERSON> has a heart of gold.
 * <PERSON><PERSON> is a person with feelings and opinions, but is very easy to work with.
 * <PERSON><PERSON> can be too opinionated at times but is easily convinced not to be.
 * Feel free to [learn more about <PERSON><PERSON>](http://yeoman.io/).

## License

Apache-2.0 © [liang]()


[npm-image]: https://badge.fury.io/js/generator-create-plugin.svg
[npm-url]: https://npmjs.org/package/generator-create-plugin
[travis-image]: https://travis-ci.com//generator-create-plugin.svg?branch=master
[travis-url]: https://travis-ci.com//generator-create-plugin
[daviddm-image]: https://david-dm.org//generator-create-plugin.svg?theme=shields.io
[daviddm-url]: https://david-dm.org//generator-create-plugin
