'use strict';
const path = require('path');
const assert = require('yeoman-assert');
const helpers = require('yeoman-test');

describe('generator-create-plugin:app', () => {
  beforeAll(() => {
    return helpers
      .run(path.join(__dirname, '../generators/app'))
      .withPrompts({
        name: 'plugin-test',
        author: 'someone',
        description: 'description of the plugin',
        main: 'index.js'
      });
  });

  it('creates files', () => {
    assert.file(['package.json']);
  });
});
