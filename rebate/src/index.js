import PPM from "plugin_public_methods";
import Base from "plugin_base";
import RebateImp from "./package/rebate";
import RebateProduct from "./package/gift";
import RebateAfterHandle from "./package/after_handle";
export default class Rebate extends Base {
	constructor(pluginService, pluginParam) {
		super(...arguments);
		this.rebatePolicy = null;
		this.policyInfo = null;
	}
	//初始化
	init(param, plugin) {
		this.initFields(param);
		this.supplementFieldsMap(plugin);
		this.initModule(param);

		return {
			fieldMap: this.fieldMap,
			decimalMap: this.decimalMap,
			fields: this.fields,
		};
	}

	//初始化字段/小数位数信息
	initFields(param) {
		this.masterApi = param.masterObjApiName;
		this.detailApi = param.objApiName;
		this.detailDesc = param.dataGetter.getDescribe(this.detailApi);
		this.fieldMap = this.getAllFields(this.detailApi);

		const me = this,
			describeLayout = param.dataGetter.getDescribeLayout(),
			detailObj = describeLayout.detailObjectList.find((d) => d.objectApiName == this.detailApi),
			detailFields = detailObj.objectDescribe.fields,
			decimalFields = ["product_price", "price_book_price", "quantity", "subtotal", "rebate_dynamic_amount"],
			events = describeLayout.layout.events || [];
		//处理__r字段
		["account_id", "product_id"].forEach((key) => {
			me.fieldMap[key + "__r"] = me.fieldMap[key] + "__r";
		});
		const firstAvailableTable = detailObj.layoutList.find((l) => !l.not_match);
		this.recordType = (firstAvailableTable && firstAvailableTable.record_type) || "default__c";

		this.fields = {
			[this.masterApi]: describeLayout.objectDescribe.fields,
			[this.detailApi]: detailFields,
		};
		//小数位数
		this.decimalMap = decimalFields.reduce((accu, item) => {
			const fName = me.fieldMap[item];
			if (fName) {
				accu[fName] = (detailFields[fName] && detailFields[fName].decimal_places) || 2;
			}
			return accu;
		}, {});
		//UI事件相关
		this.events = events.reduce((eventMap, e) => {
			switch (e.type) {
				case 6: //返利相关UI事件
					const eventId = e.triggers[0];
					if (eventId == 8 || eventId == 9) {
						const eName = eventId == 8 ? "rebate_match" : "rebate_cancel";
						eventMap[eName] = e;
					}
					break;
			}
			return eventMap;
		}, {});
	}

	// 从其他插件补全映射字段
	supplementFieldsMap(plugin) {
		const plugins = plugin?.api?.getPlugins() || [];
		const periodPlugin = plugins.find(p => p.pluginApiName === "period_product");

		if (!periodPlugin) return;

		const targetDetail = periodPlugin.params?.details?.find(d => d.objectApiName == this.detailApi);

		this.fieldMap = {
			...(targetDetail?.fieldMapping || {}),
			...this.fieldMap,
		};
	}

	initModule(param) {
		const requestId = param.dataGetter.getRequestId(),
			masterData = param.dataGetter.getMasterData();
		this.rebatePolicy = new RebateImp(
			requestId,
			this.masterApi,
			this.detailApi,
			param.formType,
			this.fields,
			this.fieldMap,
			this.decimalMap,
			this.request,
			param.triggerCal
		);

		this.rebateProduct = new RebateProduct(
			this.masterApiName,
			this.detailApi,
			this.detailDesc,
			param.formType,
			this.recordType,
			this.fields,
			this.fieldMap,
			this.decimalMap,
			this.request,
			param.triggerCal,
			param.getRowBasicData
		);

		this.rebateAfterHandle = new RebateAfterHandle(
			this.masterApiName,
			this.detailApi,
			this.request,
			param.triggerUIEvent,
			this.fieldMap,
			this.events
		);
	}

	async initData(param) {
		switch (param.formType) {
			case "add":
				break;
			case "edit":
				break;
			default:
				await this.removePolicyFromData(param);
		}
	}

	//复制/从草稿新建等初始化数据，清除返利优惠券信息
	async removePolicyFromData(param) {
		const args = this.getExecuteArgs(param),
			res = await this.rebatePolicy.cancelPolicy(args);
		await this.afterUseRebate(param, res);
		this.policyInfo = res.policyInfo;
	}

	//查询可用优惠券
	async queryCoupon(param, condition) {
		const args = this.getExecuteArgs(param);
		if (condition?.filters) {
			args.extraArgs = {
				queryFilters: condition.filters
			}
		}
		const couponData = await this.rebatePolicy.queryCoupon(args);
		return couponData;
	}

	//搜索纸质优惠券
	async searchCoupon(param, couponNo) {
		const masterData = param.dataGetter.getMasterData(),
			couponRes = await this.rebatePolicy.queryPaperCoupon(masterData, couponNo);
		return couponRes;
	}
	//绑定纸质优惠券

	async bindPaperCoupon(param, couponId) {
		const masterData = param.dataGetter.getMasterData(),
			couponRes = await this.rebatePolicy.bindPaperCoupon(masterData, couponId);
		return couponRes;
	}

	async useCoupon(userTrigger, coupons, param) {
		const args = this.getExecuteArgs(param);
		let useRes = null;
		if (coupons.length <= 0) {
			useRes = await this.rebatePolicy.cancelCoupon(args);
		} else {
			useRes = await PPM.composeAsync(
				this.afterCal.bind(this),
				PPM.partial(this.calGifts.bind(this), null),
				PPM.partial(this.matchCoupon.bind(this), userTrigger, coupons)
			)(args);
		}
		await this.afterUseRebate(param, useRes);
		return useRes;
	}

	async checkBeforeSubmit(userTrigger, param) {
		const args = this.getExecuteArgs(param),
			matchRes = await PPM.composeAsync(
				this.afterCal.bind(this),
				PPM.partial(this.calGifts.bind(this), null),
				PPM.partial(this.checkMatch.bind(this), userTrigger)
			)(args);
		await this.afterUseRebate(param, matchRes);
		return matchRes.policyInfo;
	}

	//返利单选范围返利品
	async parsePickProducts(param, gifts, unit) {
		const masterData = param.dataGetter.getMasterData(),
			detailsArr = param.dataGetter.getDetail(this.detailApi),
			res = await this.calRangeProducts(masterData, gifts, unit, detailsArr);
		return res;
	}

	//获取返利模块标准入参
	getExecuteArgs(param, rebateType) {
		const detailsArr = param.dataGetter.getDetail(this.detailApi);
		return {
			masterData: param.dataGetter.getMasterData(),
			detailDataMap: this.getDetailMap(detailsArr),
			detailsArr: detailsArr,
			policyInfo: {},
			changeInfo: {},
			modifyInfo: {},
			rebateType: rebateType || "",
		};
	}
	//过滤价格政策赠品&返利产品
	getDetailMap(dataArr = []) {
		const isPolicyGift = (data) => {
			return ["1", "2"].includes(data.is_giveaway);
		},
			isBom = (data) => data.parent_rowId;
		return dataArr.reduce((dataMap, d) => {
			if (!isPolicyGift(d) && !isBom(d)) {
				d.children && delete d.children;
				dataMap[d.rowId] = d;
			}
			return dataMap;
		}, {});
	}

	async afterUseRebate(param, res = {}) {
		this.updateData(param, res.changeInfo);
		//　更新方法为param带的trigger方法，才能通过底层更新数据
		this.rebateAfterHandle.updateTriggerUIEvent(param.triggerUIEvent);
		res = await this.rebateAfterHandle.calUiEvent(res);
	}

	updateData(param, result = {}) {
		const { mdUpdate = {}, masterUpdate = {}, mdAdd = [], mdDel = [] } = result;
		//删除的明细数据
		mdDel.forEach((rowId) => {
			param.dataUpdater.del(this.detailApi, rowId);
		});
		//新增的明细数据 (根据parentKey分组赠品)
		const curDetailsArr = param.dataGetter.getDetail(this.detailApi);
		if (curDetailsArr.length >= 1) {
			const firstKey = curDetailsArr[0].rowId;
			param.dataUpdater.insert(this.detailApi, firstKey, mdAdd, true);
		} else {
			param.dataUpdater.add(mdAdd);
		}

		//更新主对象数据
		param.dataUpdater.updateMaster(masterUpdate);
		//更新的明细数据
		Object.keys(mdUpdate).forEach((key) => {
			param.dataUpdater.updateDetail(this.detailApi, key, mdUpdate[key]);
		});
		//设置所有赠品预制字段不可编辑
		this.setRebateProductStatus(param);
	}

	//设置返利品状态
	setRebateProductStatus(param) {
		const { pricing_mode, service_start_time } = this.fieldMap;

		//设置所有赠品预制字段不可编辑
		const fields = this.fields[this.detailApi],
			packageFields = Object.keys(fields).filter((name) => fields[name].define_type == "package"),
			detailsArr = param.dataGetter.getDetail(this.detailApi);

		const periodGiftIds = [];
		const normalGiftIds = [];

		// 分类赠品
		(detailsArr.filter((d) => d.is_giveaway == "2") || []).forEach(g => {
			if (pricing_mode && g[pricing_mode] === "cycle") {
				periodGiftIds.push(g.rowId);
			} else {
				normalGiftIds.push(g.rowId);
			}
		});


		//设置非周期性赠品，所有预制字段不可编辑
		param.dataUpdater.setReadOnly({
			dataIndex: normalGiftIds,
			fieldName: packageFields,
			status: true
		});

		//设置周期性赠品，除开始时间以外的预制字段不可编辑
		if (periodGiftIds.length > 0) {
			const disableFields = packageFields.filter(
				f => ![service_start_time].includes(f)
			);
			param.dataUpdater.setReadOnly({
				dataIndex: periodGiftIds,
				fieldName: disableFields,
				status: true,
			});
		}
	}

	/******************************返利适配客户账户******************************/
	//从客户账户查询返利
	async queryRebateFromFund(param, fundArgs) {
		const { rebate_rule_id, product_rebate_rule_id, misc_content, account_id = "account_id" } = this.fieldMap,
			type = fundArgs.accountType == "1" ? "Money" : "Product",
			executeArgs = this.getExecuteArgs(param, type),
			{ masterData } = executeArgs,
			extraArgs = {
				fundAccountId: fundArgs.fundAccountId,
				needRule: true,
				changeRule: false,
				ruleId: (type == "Money" ? masterData[rebate_rule_id] : masterData[product_rebate_rule_id]) || "",
				rangeRuleIds: type == "Money" ? (masterData?.misc_content?.rangeRebate || []).map((r) => r.rangeRuleId) : [],
				rebateType: type,
			},
			result = await this.rebatePolicy.queryRebate(executeArgs, extraArgs);
		return {
			...result,
			rebateType: type,
		};
	}

	//选择返利使用规则，查询可用返利
	async queryRebateFromRule(param, fundArgs, ruleId, rangeRules = [], tempMiscContent = {}) {
		const type = fundArgs.accountType == "1" ? "Money" : "Product",
			extraArgs = {
				fundAccountId: fundArgs.fundAccountId,
				needRule: true,
				changeRule: true,
				ruleId: ruleId,
				rangeRuleIds: rangeRules,
				rebateType: type,
			},
			executeArgs = this.getExecuteArgs(param, type);

		const miscContent = executeArgs.masterData?.misc_content || {},
			keys = ["coupon", "rebate", "rangeRebate", "product_rebate"],
			parsedMiscContent = keys.reduce((accObj, key) => {
				if (key === "rangeRebate") {
					accObj[key] = this.mergeRangeRebate(miscContent[key] || [], tempMiscContent[key] || []);
				} else {
					accObj[key] = [...(miscContent[key] || []), ...(tempMiscContent[key] || [])];
				}
				return accObj;
			}, {});

		executeArgs.masterData = {
			...executeArgs.masterData,
			misc_content: parsedMiscContent,
		};
		const result = await this.rebatePolicy.queryRebate(executeArgs, extraArgs);
		return result;
	}
	//合并已使用的返利和当前操作的临时数据
	mergeRangeRebate = (oriItem, tempItem) => {
		const merged = [];
		const tempMap = new Map();

		tempItem.forEach((t) => {
			if (tempMap.has(t.rangeRuleId)) {
				tempMap.get(t.rangeRuleId).rangeRebates.push(...t.rangeRebates);
			} else {
				tempMap.set(t.rangeRuleId, {
					rangeRuleId: t.rangeRuleId,
					rangeRebates: [...t.rangeRebates],
				});
			}
		});

		oriItem.forEach((o) => {
			if (tempMap.has(o.rangeRuleId)) {
				tempMap.get(o.rangeRuleId).rangeRebates.push(...o.rangeRebates);
			} else {
				merged.push(o);
			}
		});

		return [...merged, ...Array.from(tempMap.values())];
	};

	//自动使用返利单
	async autoUseRebate(param, fundArgs, isForceUse = false) {
		const args = this.getExecuteArgs(param),
			useRes = await PPM.composeAsync(
				this.afterCal.bind(this),
				PPM.partial(this.autoMatchRebate.bind(this), fundArgs, isForceUse),
				this.getExecuteArgs.bind(this)
			)(param);
		await this.afterUseRebate(param, useRes);
		return useRes;
	}

	//操作返利单submit
	async useRebate(userTrigger, submitRebates, param, fundArgs, isForceUse = false) {
		const args = this.getExecuteArgs(param),
			{ fundAccountId, fundAccountField } = fundArgs,
			rebates = this.collectRebates(args, submitRebates, fundAccountId),
			{ rebateDatas = [], productRebateDatas = [], rangeRebateDatas = [] } = rebates;
		let useRes = null;
		if (rebateDatas.length <= 0 && productRebateDatas.length <= 0 && rangeRebateDatas.length <= 0) {
			useRes = await this.rebatePolicy.cancelRebate(args, fundAccountField);
		} else {
			useRes = await PPM.composeAsync(
				this.afterCal.bind(this),
				PPM.partial(this.calGifts.bind(this), submitRebates),
				PPM.partial(this.matchRebate.bind(this), userTrigger, rebates, isForceUse)
			)(args);
		}
		await this.afterUseRebate(param, useRes);
		return useRes;
	}

	//查询返利条件字段
	async queryRebateCond(param) {
		const args = this.getExecuteArgs(param);
		const result = await this.rebatePolicy.reqRebateCondition(args);
		return result?.rebateConditionField || {};
	}

	//查询账户最大可用返利金额
	async queryAvailableFund(param, fundIds) {
		const { flag, args } = this.getAndCheckExecuteArgs(param);
		const moneyFundIds = fundIds?.moneyFunds || [];
		if (flag) {
			const result = await this.rebatePolicy.reqFundAvailableAmount(args, moneyFundIds);
			return result?.useAmount || [];
		} else {
			return moneyFundIds.map((id) => {
				return {
					fundAccountId: id,
					amount: "0",
				};
			});
		}
	}

	getAndCheckExecuteArgs(param) {
		const args = this.getExecuteArgs(param),
			{ account_id = "account_id" } = this.fieldMap;
		let flag = args.masterData[account_id];
		return { flag, args };
	}

	/**************************************** 优惠券领券 ****************************************/
	//查询是否有可用优惠券
	queryPendingCoupons(accountId) {
		return this.rebatePolicy.queryPendingCoupons(accountId);
	}
	//获取待领取列表
	fetchPendingCoupons(accountId) {
		return this.rebatePolicy.fetchPendingCoupons(accountId);
	}
	//领券优惠券
	collectCoupon(param, batchId) {
		const args = this.getExecuteArgs(param);
		return this.rebatePolicy.collectCoupon(args, batchId);
	}

	matchRebate(userTrigger, rebates, isForceUse, args) {
		return this.rebatePolicy.useRebate(userTrigger, rebates, isForceUse, args);
	}
	matchCoupon(userTrigger, coupons, args) {
		return this.rebatePolicy.useCoupon(userTrigger, coupons, args);
	}
	checkMatch(userTrigger, args) {
		return this.rebatePolicy.checkMatch(userTrigger, args);
	}
	calGifts(submitData, param) {
		return this.rebateProduct.calGifts(param, submitData);
	}
	afterCal(param) {
		return this.rebatePolicy.calBatch(param);
	}
	calRangeProducts(masterData, gifts, unit, detailsArr) {
		return this.rebateProduct.calRangeProducts(masterData, gifts, unit, detailsArr);
	}
	//返利单按账号纬度使用
	collectRebates(args, rebates, fundAccountId) {
		return this.rebatePolicy.collectRebates(args.masterData, rebates, fundAccountId);
	}

	autoMatchRebate(fundArgs, isForceUse, args) {
		return this.rebatePolicy.autoUseRebate(fundArgs, isForceUse, args);
	}
}
