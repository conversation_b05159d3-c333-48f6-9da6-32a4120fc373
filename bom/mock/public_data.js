let mdDetails =  [
    {
        "record_type": "default__c",
        "object_describe_api_name": "SalesOrderProductObj",
        "rowId": "1718087575238236",
        "object_describe_id": "6594c79fc715080007e4b227",
        "lock_rule": "default_lock_rule",
        "base_order_product_amount": "240.00",
        "life_status": "normal",
        "dynamic_amount": "0",
        "lock_status": "0",
        "close_status": false,
        "order_product_amount": "240.00",
        "field_single_choice__c": "option1",
        "quantity": "2",
        "print_hierarchy": ".",
        "product_id": "65bb47226d2fed00018eef9f",
        "product_id__r": "wsh组合",
        "price_book_product_id": "65bb47226d2fed00018eef9f90242",
        "price_book_product_id__r": "PBProdCode20240201000135",
        "price_book_id": "6594c79bc715080007e4b05b",
        "price_book_id__r": "标准价目表",
        "product_price": 120,
        "discount": "100.00",
        "price_book_price": "120.00",
        "is_package": "是",
        "is_package__v": true,
        "attribute": null,
        "attribute_json": null,
        "attribute_price_book_id": null,
        "attribute_price_book_id__r": null,
        "node_type": null,
        "root_prod_pkg_key": "1718087575238236",
        "prod_pkg_key": "1718087575238236",
        "bom_core_id": "6642d09cb823d600089dd0f9",
        "bom_core_id__r": "20240514-001344",
        "bom_id": "6642d09cb823d600089dd0f7",
        "new_bom_path": "6642d09cb823d600089dd0f7",
        "bom_type__v": "configure",
        "base_sales_price": "120.00",
        "field_0QNiv__c": "240.00",
        "product_life_status__r": "正常",
        "base_product_price": "120.00",
        "product_status": "已上架",
        "product_status__v": "1",
        "is_saleable": "是",
        "field_81sf2__c": "100.00",
        "field_wuIbU__c": "480.00",
        "price_book_discount": "100.0000",
        "product_status__r": "已上架",
        "mc_exchange_rate": "1.000000",
        "bom_version": "b20240514-v001344",
        "field_Cj2J2__c": "7.00",
        "product_life_status__v": "normal",
        "mc_functional_currency": null,
        "is_saleable__v": true,
        "bom_type": "配置BOM",
        "unit__r": "",
        "bom_type__r": "配置BOM",
        "field_2klbf__c": "3.00",
        "product_life_status": "正常",
        "mc_currency": "CNY",
        "field_ws65F__c": "2.00",
        "unit": "",
        "field_0eO1T__c": "240.00",
        "subtotal": "240.00",
        "base_subtotal": "240.00",
        "sales_price": "120.00",
        "unit__v": null,
        "mc_exchange_rate_version": null,
        "children": [
            "1718087555770223"
        ],
        "_trLevel": 0,
        "_trFoldIcon": true,
        "__operateWidth": 152,
        "_cellStatus": {
            "price_book_id": {
                "readonly": true
            },
            "product_id": {
                "readonly": true
            },
            "price_book_product_id": {
                "readonly": true
            },
            "node_price": {
                "readonly": true
            },
            "node_discount": {
                "readonly": true
            },
            "product_price": {
                "readonly": true
            },
            "bom_id": {
                "readonly": true
            },
            "parent_prod_package_id": {
                "readonly": true
            },
            "price_book_price": {
                "readonly": true
            }
        }
    },
    {
        "record_type": "default__c",
        "object_describe_api_name": "SalesOrderProductObj",
        "rowId": "1718087555770223",
        "object_describe_id": "6594c79fc715080007e4b227",
        "lock_rule": "default_lock_rule",
        "base_order_product_amount": "0.00",
        "life_status": "normal",
        "dynamic_amount": "0",
        "lock_status": "0",
        "close_status": false,
        "order_product_amount": "0.00",
        "field_single_choice__c": "option1",
        "quantity": "4.000",
        "print_hierarchy": ".",
        "parent_bom_id": "6642d09cb823d600089dd0f7",
        "adjust_price": 10,
        "amount": "2.000",
        "modified_adjust_price": 10,
        "parent_rowId": "1718087575238236",
        "price_editable": "是",
        "amount_editable": "是",
        "amount_any": "否",
        "order_field": "1",
        "__adjust_price": "0.00",
        "attribute": null,
        "undefined": "b20240513-v001344",
        "new_bom_path": "6642d09cb823d600089dd0f7.6642d09cb823d600089dd0f8",
        "related_core_id": "6641e5c6ab2ab00007bbfb06",
        "product_id": "663c713d35634d0007a7e0aa",
        "product_id__r": "B",
        "bom_id": "6642d09cb823d600089dd0f8",
        "bom_id__r": "20240514004101",
        "product_group_id__v": null,
        "share_rate": "",
        "node_type": "standard",
        "price_mode": "配置价格",
        "bom_core_id": "6641e5c6ab2ab00007bbfb06",
        "product_price": 10,
        "defQuantity": "2.000",
        "root_product_id": "65bb47226d2fed00018eef9f",
        "root_product_id__r": "wsh组合",
        "_isChildren": true,
        "node_no": "1",
        "sales_price": null,
        "discount": null,
        "subtotal": null,
        "base_subtotal": "0.00",
        "base_sales_price": "0.00",
        "node_subtotal": null,
        "price_book_price": "0.00",
        "prod_pkg_key": "1718087555770223",
        "root_prod_pkg_key": "1718087575238236",
        "parent_prod_pkg_key": "1718087575238236",
        "__parentBomRowId": "1718087575238236",
        "node_discount": null,
        "node_price": 10,
        "field_0QNiv__c": "40.00",
        "product_life_status__r": "正常",
        "base_product_price": "10.00",
        "increment": "",
        "product_status": "已上架",
        "product_status__v": "1",
        "is_saleable": "是",
        "price_mode__r": "配置价格",
        "field_81sf2__c": "0.00",
        "field_wuIbU__c": "80.00",
        "product_status__r": "已上架",
        "price_mode__v": "1",
        "mc_exchange_rate": "1.000000",
        "bom_version": "b20240513-v001344",
        "field_Cj2J2__c": "7.00",
        "product_life_status__v": "normal",
        "mc_functional_currency": null,
        "amount_editable__v": true,
        "is_saleable__v": true,
        "amount_any__v": false,
        "bom_type": "配置BOM",
        "is_package": "是",
        "bom_type__v": "configure",
        "unit__r": "",
        "min_amount": "",
        "bom_type__r": "配置BOM",
        "field_2klbf__c": "3.00",
        "product_life_status": "正常",
        "mc_currency": "CNY",
        "is_package__v": true,
        "field_ws65F__c": "2.00",
        "unit": "",
        "field_0eO1T__c": "40.00",
        "price_editable__v": true,
        "max_amount": "5",
        "unit__v": null,
        "mc_exchange_rate_version": null,
        "children": [
            "1718087555770224"
        ],
        "_trLevel": 1,
        "_trFoldIcon": true,
        "__operateWidth": 24,
        "_cellStatus": {
            "price_book_id": {
                "readonly": true
            },
            "product_id": {
                "readonly": true
            },
            "price_book_product_id": {
                "readonly": true
            },
            "amount_any": {
                "readonly": true
            },
            "subtotal": {
                "readonly": true
            },
            "remark": {
                "readonly": true
            },
            "sales_price": {
                "readonly": true
            },
            "discount": {
                "readonly": true
            },
            "quote_line_id": {
                "readonly": true
            },
            "price_book_discount": {
                "readonly": true
            },
            "price_book_price": {
                "readonly": true
            },
            "attribute": {
                "readonly": true
            },
            "attribute_price_book_id": {
                "readonly": true
            },
            "nonstandard_attribute": {
                "readonly": true
            },
            "unit": {
                "readonly": true
            },
            "bom_type": {
                "readonly": true
            },
            "bom_version": {
                "readonly": true
            },
            "is_package": {
                "readonly": true
            }
        }
    },
    {
        "record_type": "default__c",
        "object_describe_api_name": "SalesOrderProductObj",
        "rowId": "1718087555770224",
        "object_describe_id": "6594c79fc715080007e4b227",
        "lock_rule": "default_lock_rule",
        "base_order_product_amount": "0.00",
        "life_status": "normal",
        "dynamic_amount": "0",
        "lock_status": "0",
        "close_status": false,
        "order_product_amount": "0.00",
        "field_single_choice__c": "option1",
        "quantity": "4.000",
        "print_hierarchy": ".",
        "parent_bom_id": "6642d09cb823d600089dd0f8",
        "adjust_price": "10.00",
        "amount": "1.000",
        "modified_adjust_price": "10.00",
        "parent_rowId": "1718087555770223",
        "price_editable": "是",
        "amount_editable": "是",
        "amount_any": "否",
        "order_field": "1",
        "__adjust_price": "10.00",
        "attribute": null,
        "undefined": null,
        "new_bom_path": "6642d09cb823d600089dd0f7.6642d09cb823d600089dd0f8.6641e5c6ab2ab00007bbfb03",
        "related_core_id": null,
        "product_id": "663c714a35634d0007a7e2e2",
        "product_id__r": "B1",
        "bom_id": "6641e5c6ab2ab00007bbfb03",
        "bom_id__r": "20240513004101",
        "product_group_id__v": null,
        "share_rate": "",
        "node_type": "standard",
        "price_mode": "配置价格",
        "product_price": 10,
        "defQuantity": "1.000",
        "root_product_id": "65bb47226d2fed00018eef9f",
        "root_product_id__r": "wsh组合",
        "_isChildren": true,
        "node_no": "1",
        "sales_price": null,
        "discount": null,
        "subtotal": null,
        "base_subtotal": "0.00",
        "base_sales_price": "0.00",
        "node_subtotal": null,
        "price_book_price": "0.00",
        "prod_pkg_key": "1718087555770224",
        "root_prod_pkg_key": "1718087575238236",
        "parent_prod_pkg_key": "1718087555770223",
        "__parentBomRowId": "1718087555770223",
        "node_discount": null,
        "node_price": 10,
        "field_0QNiv__c": "40.00",
        "product_life_status__r": "正常",
        "base_product_price": "10.00",
        "increment": "1.000",
        "product_status": "已上架",
        "product_status__v": "1",
        "is_saleable": "是",
        "price_mode__r": "配置价格",
        "field_81sf2__c": "0.00",
        "field_wuIbU__c": "80.00",
        "product_status__r": "已上架",
        "price_mode__v": "1",
        "mc_exchange_rate": "1.000000",
        "bom_version": null,
        "field_Cj2J2__c": "7.00",
        "product_life_status__v": "normal",
        "mc_functional_currency": null,
        "amount_editable__v": true,
        "is_saleable__v": true,
        "amount_any__v": false,
        "bom_type": null,
        "is_package": "否",
        "unit__r": "",
        "min_amount": "1",
        "field_2klbf__c": "3.00",
        "product_life_status": "正常",
        "mc_currency": "CNY",
        "is_package__v": false,
        "field_ws65F__c": "2.00",
        "unit": "",
        "field_0eO1T__c": "40.00",
        "price_editable__v": true,
        "max_amount": "3",
        "unit__v": null,
        "mc_exchange_rate_version": null,
        "_trLevel": 2,
        "__operateWidth": 24,
        "_cellStatus": {
            "price_book_id": {
                "readonly": true
            },
            "product_id": {
                "readonly": true
            },
            "price_book_product_id": {
                "readonly": true
            },
            "amount_any": {
                "readonly": true
            },
            "subtotal": {
                "readonly": true
            },
            "remark": {
                "readonly": true
            },
            "sales_price": {
                "readonly": true
            },
            "discount": {
                "readonly": true
            },
            "quote_line_id": {
                "readonly": true
            },
            "price_book_discount": {
                "readonly": true
            },
            "price_book_price": {
                "readonly": true
            },
            "attribute": {
                "readonly": true
            },
            "attribute_price_book_id": {
                "readonly": true
            },
            "nonstandard_attribute": {
                "readonly": true
            },
            "unit": {
                "readonly": true
            },
            "bom_type": {
                "readonly": true
            },
            "bom_version": {
                "readonly": true
            },
            "is_package": {
                "readonly": true
            }
        }
    }
];


let public_param = {
    masterObjApiName: 'SalesOrderObj',
    masterApiName: 'SalesOrderObj',
    objApiName: 'SalesOrderProductObj',
    mdApiName:'SalesOrderProductObj', 

    recordType: "default__c",
    triggerUIEvent: () => {},
    dataGetter: {
        getData: (mdApiName, rowId,) => {
            return mdDetails.find(c => c.rowId === rowId);
        },
        getMasterData: () => {},
        getDescribe:() => {
            return {
                "fields": {
                    discount:{
                        "describe_api_name": "SalesOrderProductObj",
                        "default_is_expression": true,
                        "auto_adapt_places": true,
                        "pattern": "",
                        "description": "",
                        "is_unique": false,
                        "type": "percentile",
                        "decimal_places": 2,
                        "default_to_zero": true,
                        "is_required": true,
                        "define_type": "package",
                        "is_single": false,
                        "label_r": "折扣",
                        "index_name": "d_1",
                        "max_length": 100,
                        "is_index": false,
                        "is_active": true,
                        "create_time": 1562060925360,
                        "is_encrypted": false,
                        "length": 12,
                        "default_value": "$price_book_product_id__r.discount$",
                        "label": "折扣",
                        "api_name": "discount",
                        "_id": "6594c79fc715080007e4b1ef",
                        "is_index_field": false,
                        "help_text": "",
                        "status": "new",
                      
                        "show_tag": false,
                        "help_text_type": "hover",
                        "is_tile_help_text": false
                    },
                    product_price: {
                        "describe_api_name": "SalesOrderProductObj",
                        "default_is_expression": true,
                        "auto_adapt_places": false,
                        "remove_mask_roles": {},
                        "description": "价格（元）",
                        "is_unique": false,
                        "type": "currency",
                        "extend_info": {
                            "show_positive_sign": true
                        },
                        "decimal_places": 0,
                        "default_to_zero": true,
                        "is_required": true,
                        "define_type": "package",
                        "is_single": false,
                        "label_r": "价格",
                        "index_name": "d_2",
                        "max_length": 14,
                        "is_index": true,
                        "is_active": true,
                        "create_time": 1562060925361,
                        "is_encrypted": false,
                        "length": 14,
                        "default_value": "$price_book_product_id__r.pricebook_sellingprice$*EXCHANGERATE($price_book_product_id__r.mc_currency$,$mc_currency$,1.0)",
                        "label": "价格",
                        "currency_unit": "￥",
                        "is_need_convert": false,
                        "api_name": "product_price",
                        "_id": "6594c79fc715080007e4b1f2",
                        "is_index_field": false,
                        "is_show_mask": false,
                        "round_mode": 4,
                        "help_text": "",
                        "status": "released",
               
                        "empty_prompt": "",
                        "show_tag": false,
                        "font_color": "",
                        "help_text_type": "hover",
                        "is_tile_help_text": false
                    },
                    quantity: {
                        "describe_api_name": "SalesOrderProductObj",
                        "default_is_expression": false,
                        "auto_adapt_places": false,
                        "remove_mask_roles": {},
                        "description": "数量",
                        "is_unique": false,
                        "type": "number",
                        "decimal_places": 3,
                        "default_to_zero": true,
                        "is_required": true,
                        "define_type": "package",
                        "is_single": false,
                        "label_r": "数量",
                        "index_name": "d_5",
                        "max_length": 14,
                        "is_index": true,
                        "is_active": true,
                        "create_time": 1562060925361,
                        "is_encrypted": false,
                        "display_style": "input",
                        "step_value": 1,
                        "length": 11,
                        "default_value": "2",
                        "label": "数量",
                        "is_need_convert": false,
                        "api_name": "quantity",
                        "_id": "6594c79fc715080007e4b200",
                        "is_index_field": false,
                        "is_show_mask": false,
                        "round_mode": 4,
                        "help_text": "",
                        "status": "released",
                  
                        "empty_prompt": "",
                        "show_tag": false,
                        "font_color": "",
                        "help_text_type": "hover",
                        "is_tile_help_text": false
                    },
                    bom_type: {
                        "describe_api_name": "SalesOrderProductObj",
                        "is_index": false,
                        "is_active": true,
                        "create_time": 1690818980799,
                        "is_encrypted": false,
                        "auto_adapt_places": false,
                        "quote_field_type": "select_one",
                        "remove_mask_roles": {},
                        "description": "bom类型",
                        "is_unique": false,
                        "label": "bom类型",
                        "type": "quote",
                        "quote_field": "bom_core_id__r.category",
                        "is_need_convert": false,
                        "is_required": false,
                        "api_name": "bom_type",
                        "define_type": "package",
                        "_id": "64c7d9a47fe97148d0a95db2",
                        "is_single": false,
                        "is_index_field": false,
                        "index_name": "s_27",
                        "is_show_mask": false,
                        "help_text": "",
                        "status": "released",
                        "help_text_type": "hover",
                        "options": [
                            {
                                "value": "configure",
                                "label": "配置BOM"
                            },
                            {
                                "value": "standard",
                                "label": "标准BOM"
                            }
                        ],
                        "is_tile_help_text": false,
                        "length": 0,
                        "decimal_places": 0
                    }
                },
            }
        },
        getDetail:() => {
            return mdDetails
        },
        getDetails:() =>{
            return {
                "SalesOrderProductObj": mdDetails,
            }
        }
    },
    dataUpdater: {
        insert: () => {},
        setReadOnly: () => {},
        add: (data)=>{return data},
        updateDetail(mdApiName, rowId, up){
            let row = mdDetails.find(c => c.rowId === rowId);
            if(row)row = Object.assign(row, up);
            return row;
        },
       
    },

    triggerCalAndUIEvent:()=>{},
    triggerCal:()=>{},
}

let CRM = {
    util: {
        cloneBomData: (data) => {
            return data
        },
        setChildrenAmount: () => {
        },
        isGrayScale: () => {
        },
        division(a,b){
            return a/b;
        },
        formatDecimalPlace(data, placeNum){
            return data.toFixed(placeNum);
        },
        forEachTreeData: function (data, cb) {
			// if (!Array.isArray(data)) return;
			// let groupPDataList = [];

			// function findPData(rowId) {
			// 	return groupPDataList.find(function (item) {
			// 		return item.rowId == rowId;
			// 	})
			// }

			// function _fn(list, pData) {
			// 	list.forEach(function (item) {
            //         if(!item instanceof Object) return;
			// 		if (item.isGroup && pData) groupPDataList.push(pData);

			// 		// 如果父节点是分组，则加上分组的父节点信息；
			// 		if (pData && pData.isGroup) {
			// 			let groupPData = findPData(pData.pid); // 如果父级是分组，找分组的父级
			// 			cb && cb(item, pData, groupPData);
			// 		} else {
			// 			cb && cb(item, pData);
			// 		}
			// 		if (item.children) _fn(item.children, item)
			// 	})
			// }

			// if (data.length) {
			// 	_fn(data)
			// }
		},

    }
}
export { public_param , CRM, mdDetails}