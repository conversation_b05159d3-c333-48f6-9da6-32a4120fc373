import { public_param } from "./public_data"

const render_before_param = {
    ...public_param,
}

const parse_data_before_save_data = [
    [
        {
            "record_type": "default__c",
            "object_describe_api_name": "SalesOrderProductObj",
            "rowId": "1705992007442228",
            "object_describe_id": "5ad9a833319d191e079c102b",
            "lock_rule": "default_lock_rule",
            "life_status": "normal",
            "dynamic_amount": "0",
            "node_type": null,
            "field_j2N6S__c": "2u21A215B",
            "lock_status": "0",
            "order_product_amount": "40.00",
            "print_hierarchy": "-",
            "price_book_product_id": "64c0dafc84f6880001f42a70",
            "price_book_product_id__r": "PBProdCode20230726002535",
            "product_id": "64b79c0bff8e01000139cac4",
            "product_id__r": "D-D",
            "price_book_id": "62fb32f2d5dc810001c1715b",
            "price_book_id__r": "1234567- 775客户",
            "discount": "100",
            "price_book_price": "40.00",
            "product_price": "40.00",
            "is_package": "是",
            "is_package__v": true,
            "attribute": null,
            "attribute_json": null,
            "attribute_price_book_id": null,
            "attribute_price_book_id__r": null,
            "is_giveaway__v": "0",
            "field_JRq1t__c": "1.00",
            "product_status__v": "1",
            "price_editable": null,
            "is_saleable": "是",
            "product_status__r": "已上架",
            "field_goW28__c": "50.00",
            "price_mode": null,
            "bom_type": null,
            "field_t8uED__c": null,
            "is_giveaway": "否",
            "min_amount": null,
            "product_life_status": "正常",
            "field_5CiDP__c": "11.00",
            "unit": "",
            "field_aQ028__c": "40.00",
            "field_6RdF8__c": "0.00",
            "subtotal": "40.00",
            "field_41n5y__c": "",
            "field_KJ84h__c": null,
            "is_giveaway__r": "否",
            "field_41n5y__c__r": "",
            "product_life_status__r": "正常",
            "field_CoMcy__c": "100.0000",
            "increment": null,
            "product_status": "已上架",
            "price_book_discount": "100.0000",
            "bom_version": null,
            "product_life_status__v": "normal",
            "field_C3tKc__c": "4000.00",
            "field_kJXv9__c": "0.00",
            "is_saleable__v": true,
            "field_qm1nr__c": null,
            "field_51j9A__c": "办公用品",
            "unit__r": "",
            "amount_editable": null,
            "product_group_id": null,
            "field_afeCB__c": "50.00",
            "sales_price": "40.00",
            "field_Kz4zw__c": "0.00",
            "max_amount": null,
            "unit__v": null,
            "field_51j9A__c__r": "办公用品",
            "field_41n5y__c__v": null,
            "field_51j9A__c__v": "1",
            "quantity": "1",
            "root_prod_pkg_key": "1705992007442228",
            "prod_pkg_key": "1705992007442228",
            "bom_id": "64b79c65ff8e01000139f0cf",
            "__originalTotalMoney": 40,
            "children": [
                "1705992007439226",
                "1705992007439227",
                "1705992007439222"
            ],
            "_trLevel": 0,
            "_trFoldIcon": true,
            "__operateWidth": 152,
            "__tbIndex": 0,
            "requestId": "96fce43f13964f15baa141036a702231",
            "field_j2N6S__c__o": "",
            "node_price": null,
            "bom_core_id": null,
            "field_feOYf__c": "",
            "node_discount": null,
            "nonstandard_attribute": "",
            "share_rate": null,
            "node_subtotal": null,
            "sale_contract_line_id": null,
            "field_9Y2yr__c": null,
            "field_g1Y4F__c": "",
            "field_otvPY__c": null,
            "field_f85z9__c": null,
            "field_37708__c": "",
            "field_jo3nM__c": null,
            "field_qXB0W__c": [],
            "field_tWwjP__c": null,
            "field_6xy1x__c": null,
            "field_4yrv6__c": "",
            "field_Sj1ok__c": "",
            "field_tZkug__c": null,
            "field_l5SqO__c": null,
            "field_e90u7__c": null,
            "field_R0dw2__c": "",
            "quoteline_id__c": null,
            "field_f2Nlj__c": "",
            "parent_prod_package_id": null
        },
        {
            "record_type": "default__c",
            "object_describe_api_name": "SalesOrderProductObj",
            "rowId": "1705992007439226",
            "object_describe_id": "5ad9a833319d191e079c102b",
            "lock_rule": "default_lock_rule",
            "life_status": "normal",
            "dynamic_amount": "0",
            "node_type": "standard",
            "field_j2N6S__c": "option1",
            "lock_status": "0",
            "order_product_amount": "0",
            "print_hierarchy": "-",
            "parent_bom_id": "64b79c65ff8e01000139f0cf",
            "adjust_price": null,
            "amount": "1.00000",
            "modified_adjust_price": 0,
            "parent_rowId": "1705992007442228",
            "price_editable": "是",
            "amount_editable": "是",
            "order_field": "2",
            "attribute": null,
            "product_id": "64b79bf1ff8e01000139bd37",
            "product_id__r": "D-D-D",
            "bom_id": "64b79c65ff8e01000139f0d1",
            "bom_id__r": "20230719015031",
            "product_group_id__v": null,
            "share_rate": null,
            "price_mode": "配置价格",
            "product_price": "0.00",
            "defQuantity": "2.0",
            "quantity": "2.0",
            "price_book_discount": "100.0000",
            "root_product_id": "64b79c0bff8e01000139cac4",
            "root_product_id__r": "D-D",
            "_isChildren": true,
            "node_no": "2",
            "sales_price": null,
            "discount": null,
            "subtotal": null,
            "node_subtotal": null,
            "price_book_price": null,
            "prod_pkg_key": "1705992007439226",
            "root_prod_pkg_key": "1705992007442228",
            "parent_prod_pkg_key": "1705992007442228",
            "field_41n5y__c__r": "",
            "field_CoMcy__c": "0.0000",
            "field_JRq1t__c": "2.00",
            "increment": "",
            "price_mode__r": "配置价格",
            "price_mode__v": "1",
            "field_goW28__c": "10.00",
            "field_C3tKc__c": "0.00",
            "amount_editable__v": true,
            "field_kJXv9__c": "0.00",
            "field_qm1nr__c": null,
            "field_t8uED__c": null,
            "field_51j9A__c": "办公用品",
            "unit__r": "",
            "min_amount": "0",
            "field_afeCB__c": "33.00",
            "field_5CiDP__c": "12.00",
            "unit": "",
            "field_aQ028__c": "0.00",
            "field_6RdF8__c": "0.00",
            "field_Kz4zw__c": "0.00",
            "price_editable__v": true,
            "max_amount": "",
            "unit__v": null,
            "field_41n5y__c": "",
            "field_51j9A__c__r": "办公用品",
            "field_41n5y__c__v": null,
            "field_KJ84h__c": null,
            "field_51j9A__c__v": "1",
            "node_discount": 0,
            "node_price": "0.00",
            "price_book_product_id__r": null,
            "price_book_product_id": null,
            "price_book_id__r": null,
            "price_book_id": null,
            "_trLevel": 1,
            "__operateWidth": 24,
        }
    ]
]

export { render_before_param, parse_data_before_save_data }