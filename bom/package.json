{"name": "bom", "version": "960.0.6", "description": "BOM插件公共组件", "main": "dist/index.js", "scripts": {"release": "babel src --out-dir dist && npm publish", "dev": "babel src --out-dir dist", "test": "jest", "test:coverage": "jest --coverage", "debugger": "node --inspect-brk ./node_modules/jest/bin/jest --runInBand --no-cache --no-watchman"}, "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "^7.21.0", "@babel/core": "^7.21.3", "@babel/preset-env": "^7.20.2", "plugin_public_methods": "^940.0.6", "plugin_base": "^940.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "dependencies": {}}