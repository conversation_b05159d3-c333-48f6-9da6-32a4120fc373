/**
 * @desc: cpq公共插件
 * @author: wang<PERSON><PERSON>
 * @date: 12/29/21
 */

import PPM from 'plugin_public_methods'
import Base from 'plugin_base'
import Add from './package/add'
import PriceServiceExtend from './package/priceservice_extend'

export default class Bom_Base extends Base {

    options() {
        return {
            defMasterFields: {
                form_account_id: 'account_id',                 // 客户id
                form_partner_id: 'partner_id',                 // 合作伙伴id
                form_price_book_id: 'price_book_id',           // 价目表id
                form_mc_currency: 'mc_currency',               // 价目表id
            },
            defMdFields: {
                product_price: 'price',                         // 价格
                price_book_id: 'price_book_id',                 // 价目表id
                discount: 'discount',	                         // 折扣
                price_book_product_id: 'price_book_product_id', // 开启价目表，替换价目表产品id
                product_id: 'product_id',                       // 产品名称
                quantity: 'quantity',                           // 数量
                price_book_price: 'price_book_price',           // 价目表价格
                price_book_discount: 'pricebook_discount',      // 价目表折扣
                price_book_subtotal: 'price_book_subtotal',     // 价目表小计
                sales_price: 'selling_price',                   // 销售单价
                subtotal: 'total_amount',                       // 小计
                unit: 'unit',
                amount_any: 'amount_any',                       // 数量任意
                // bomcore 相关                                  // 单位
                bom_core_id: 'bom_core_id',
                bom_version: 'bom_version',
                bom_type: 'bom_type',
                related_core_id: 'related_core_id',
                new_bom_path: 'new_bom_path',
                // bom相关
                prod_pkg_key: 'prod_pkg_key',                   // 虚拟key
                bom_id: 'bom_id',
                parent_prod_pkg_key: 'parent_prod_pkg_key',     // 父级虚拟key
                root_prod_pkg_key: 'root_prod_pkg_key',         // 跟节点虚拟key
                product_group_id: 'product_group_id',           // 父级分组id
                is_package: 'is_package',                       // 是否是产品包
                max_amount: 'max_amount',                       // 最大数量
                min_amount: 'min_amount',                       // 最小数量
                increment: 'increment',                         // 数量增加幅度
                price_editable: 'price_editable',               // 子产品价格是否可编辑
                amount_editable: 'amount_editable',             // 子产品数量是否可编辑
                price_mode: 'price_mode',                       // 子产品定价模式
                node_discount: 'node_discount',                 // 选配折扣
                node_price: 'node_price',                       // 标准选配价格
                parent_prod_package_id: 'parent_prod_package_id',// 所属产品组合
                node_type: 'node_type',                          // 临时子件
                temp_node_group_id: 'temp_node_group_id',        // 临时子件分组
                node_no: 'node_no',                              // 临时子件序号

                mc_currency: 'mc_currency',
                mc_exchange_rate: 'mc_exchange_rate',
                // 属性
                attribute_json: 'attribute_json',
                attribute_price_book_id: 'attribute_price_book_id',
                nonstandard_attribute: 'nonstandard_attribute',
                nonstandard_attribute_json: 'nonstandard_attribute_json',
                // 订单特有分摊
                dynamic_amount: 'dynamic_amount',
                node_subtotal: 'node_subtotal',                    // 部件小计
                share_rate: 'share_rate',                          // 分摊比例
                policy_dynamic_amount: 'policy_dynamic_amount',    //
                policy_subtotal: 'policy_subtotal',                //
                policy_price: 'policy_price',                      //
                policy_discount: 'policy_discount',                //
                gift_amortize_price: 'gift_amortize_price',        //
                gift_amortize_subtotal: 'gift_amortize_subtotal',  //

                // 报价单
                extra_discount: 'extra_discount',
                selling_price: 'sales_price',

                extra_discount_amount: 'extra_discount_amount',
                system_discount_amount: 'system_discount_amount',
                sales_amount: 'sales_amount',
                total_discount: 'total_discount',
                base_subtotal: 'base_subtotal',
                base_sales_price: 'base_sales_price',
                base_delivery_amount: 'base_delivery_amount',
                base_order_product_amount: 'base_order_product_amount',
                base_sales_amount: 'base_sales_amount',
                base_total_amount: 'base_total_amount',
                base_selling_price: 'base_selling_price',
                base_product_price: 'base_product_price',
                base_price: 'base_price',

                // 销售合同产品
                executed_order_subtotal: 'executed_order_subtotal',
                unexecuted_order_subtotal: 'unexecuted_order_subtotal',
                unexecuted_quantity: 'unexecuted_quantity',
                executed_quantity: 'executed_quantity',

            },
        }
    }

    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.Add = new Add(pluginService, pluginParam, this);
        this.PriceServiceExtend = new PriceServiceExtend(pluginService, pluginParam, this);
        this.cacheChildren([this.Add, this.PriceServiceExtend]);
    }

    async _mdRenderBefore(plugin, param) {
        let mdApiName = param.objApiName;
        this.parseDataForCovert(param);
        let details = param.dataGetter.getDetail(mdApiName);
        let allFields = this.getAllFields(mdApiName);
        await this.parseTempChildren(details, param);
        // this.changeSomeChildrenField(details, param);
        details = PPM.parseTreeDataForMd({data: details, fields: allFields, mdApiName}, param);
        this.Add.setBomSpecialFieldsVal({data: details, mdApiName}, param);
        this.Add.setFieldsReadonly({data: details, mdApiName}, param);
        PPM.addDefQuantity({data: details, allFields, mdApiName}, param);
        return {
            __execResult: {
                columnRenders: this.formatColumnRender(),
                parseData: [this.parseDataBeforeSave.bind(this, mdApiName, param)],
                beforeMDUpdate: [this.beforeMDUpdate.bind(this, mdApiName, param)]
            },
            __mergeDataType: {
                array: 'concat'
            }
        }
    }

    // 推拉单适配；根据映射规则，把编辑的数量带过来
    parseDataForCovert(param){
        if(['transform', 'referenceCreate'].includes(param.formType)){
            let mdApiName = param.objApiName;
            let {quantity} = this.getAllFields(mdApiName);
            let obj = param.dataGetter.getOptions('conversionRuleInfo');
            console.log(obj)
            let {mappingRules, } = obj;
            let details = param.dataGetter.getDetail(mdApiName);
            let tarMappingRule = this._getMappingRule(mappingRules, mdApiName);     // 映射规则
            let sourceMdApiName = tarMappingRule.source_api_name;                   // 来源对象
            let sourceData = this._getAllSourceData(obj.sourceData, sourceMdApiName) ;          // 来源数据
            if(!sourceData) return;
            let mapField = this._getMappingField(tarMappingRule.field_mapping, '_id');    // 来源数据匹配字段
            let mapQuantity = this._getMappingField(tarMappingRule.field_mapping, quantity);    // 来源数据数量字段
            details.forEach(item => {
                let fd = sourceData.find(c => c.id === item[mapField]);
                if (fd && fd.data) {
                    param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                        [quantity]: fd.data[mapQuantity],
                    });
                }
            })
        }
    }

    // 获取映射来源数据
    _getAllSourceData(sourceData, sourceMdApiName){
        let f = sourceData.find(item => {
           return item.details[sourceMdApiName]
        });
        return f && f.details[sourceMdApiName].data;
    }

    // 获取映射规则
    _getMappingRule(mappingRules, tarMdApiName) {
        return mappingRules.find(item => item.target_api_name === tarMdApiName);
    }

    // 获取映射字段
    _getMappingField(field_mapping, field){
        return field_mapping.find(item => item.source_field_api_name === field).target_field_api_name
    }

    /**
     * 产品名称列的render
     */
    formatColumnRender() {
        return [{
            product_id(cellValue, trData) {
                let r = `<span>${cellValue}</span>`;
                if(trData.isGroup){
                    r = `<span class="fx-icon-fold2 new910"></span> ` + r;
                }
                return r;
            },
        }];
    }

    /**
     * @des 给临时子件补字段；补bom对象的一些字段默认值
     * @param plugin
     * @param options = {list, param}
     * @returns {Promise<void>}
     */
    async parseTempNodeFields(plugin, options = {}){
        let {list, param} = options;
        await this.parseTempChildren(list, param);
    }

    // 请求bom对象描述；取默认值用
    async getBomDescribe(param){

        if(this._bomDescribe) return this._bomDescribe;
        this._bomDescribe = await this.requestBomDescribe(param);
        this._bomDescribe = this._bomDescribe.objectDescribe.fields;
        return this._bomDescribe;
    }

    // 处理临时子件字段默认值
    async parseTempChildren(details, param) {
        if (!this.getConfig('bom_temp_node')) return;
        let bomDes = await this.getBomDescribe(param);
        let mdApiName = param.objApiName;
        let {node_type, price_mode, price_book_id, price_book_product_id, attribute_price_book_id } = this.getAllFields(mdApiName);
        console.log('node_type', node_type)
        let tempData = details.filter(item => item[node_type] === 'temp');
        if (!tempData.length) return;
        let bomBasicData = this.getBomObjBasicData(bomDes);
        tempData.forEach(item => {
            param.dataUpdater.updateDetail(mdApiName, item.rowId, bomBasicData);
            if(bomBasicData[price_mode + '__v'] == '1'){
                let up = {};
                let fields = [price_book_id, price_book_product_id, attribute_price_book_id];
                fields.forEach(f => {
                    up[f] = up[f + '__r'] = null;
                });
                param.dataUpdater.updateDetail(mdApiName, item.rowId, up);
            }
        })
    }

    // server计算产品包价格；
    requestBomDescribe(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/BOMObj/controller/DescribeLayout`;
        return PPM.ajax(this.request, url, Object.assign({}, {
            apiname: "BOMObj",
            include_detail_describe: true,
            include_layout: false,
            layout_type: "add",
            recordType_apiName: "default__c",
        }, param))
    }

    // 获取BOM字段默认值
    getBomObjBasicData(describe){
        if(this.cloneBomBasicFields) return this.cloneBomBasicFields;
        const fieldMapping = this.getAllFields();
        let fields = ['price_editable', 'amount_editable', 'price_mode', 'min_amount', 'max_amount', 'increment', 'amount_any'];
        let editableField = ['price_editable', 'amount_editable', 'amount_any'];
        let cloneBomBasicFields = {};
        fields.forEach(f => {
            let fm = fieldMapping[f];
            cloneBomBasicFields[fm] = describe?.[f]?.default_value;
            if(editableField.includes(f)){
                cloneBomBasicFields[fm + '__v'] = cloneBomBasicFields[fm] ? true : false;
                cloneBomBasicFields[fm] = cloneBomBasicFields[fm] ? this.i18n('是') : this.i18n('否');
            }else if(f === 'price_mode'){
                cloneBomBasicFields[fm + '__v'] = cloneBomBasicFields[fm] == '1' ? '1' : '2';
                cloneBomBasicFields[fm] = cloneBomBasicFields[fm] == '1' ? this.i18n('配置价格') : this.i18n('价目表价格');
            }
        });
        this.cloneBomBasicFields = cloneBomBasicFields;
        return cloneBomBasicFields;
    }

    // 保存之前，把子产品上的特殊字段改为0
    parseDataBeforeSave(mdApiName, param, data = []) {
        this.validProdKey(data, mdApiName, param);
        this.Add.setBomSpecialFieldsVal({data, mdApiName, val: '0', onlyChangeData: true}, param);
        return data;
    }

    // 保存前再校验一下虚拟key是否正常，可能有映射的数据没有映射虚拟key，直接保存订单。
    validProdKey(data, mdApiName, param){
        if(this.isGrayDeleteRoot()) return;
        let allFields = this.getAllFields();
        const {
            root_prod_pkg_key
        } = allFields;
        data.forEach(item => {
            if (PPM.isBom(item, allFields).isPackage) {
                if(!item[root_prod_pkg_key]){
                    this.Add.updateProdKey([], item);
                }
            }
        })
    }

    // 处理UI事件新添加数据，组装树状结构
     beforeMDUpdate(mdApiName, param, res = {}){
         if(!res.add || !res.add.length) return;
         let addData = res.add.filter(item => item._fromUIEvent);
         if(!addData.length) return;
         let allFields = this.getAllFields(mdApiName);
         let newData = PPM.parseTreeDataForMd({data: addData, fields: allFields, mdApiName}, param);
         let groupData = newData.filter(item => item.isGroup);
         res.add = res.add.concat(groupData);
         // 处理BOM子产品
         this.Add._bom_parseChildren({}, {
             details: newData,
             mdApiName,
             param
         });
     }

     // 属性是否可以回填。是子产品 且 没有价目表id的，不回填属性价目表
    _attribute_matchRealPrice(plugin, opt){
        let {rowData, mdApiName, param} = opt;
        let {parent_prod_pkg_key, price_book_id, price_mode} = this.getAllFields(mdApiName);
        if (rowData[parent_prod_pkg_key] && !rowData[price_book_id]) {
            if (rowData[price_mode] == '1' || rowData[price_mode + '__v'] == '1') {
                return {
                    notMatch: true
                }
            }
        }
    }

    // 是否灰度了不回填根结点
    isGrayDeleteRoot(){
        // return true
        return this.getConfig('bom_delete_root') == '1';
        // return CRM.util.isGrayScale('CRM_DELETE_ROOT');
    }

    getDescribe(mdApiName = '', param = {}, field = '') {
        return this.Add.getDescribe(...arguments);
    }


     getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            }, {
                event: 'bom.parseTempNodeFields',
                functional: this.parseTempNodeFields.bind(this)
            }, {
                event: 'attribute.matchRealPrice',
                functional: this._attribute_matchRealPrice.bind(this)
            },
        ]
    }


}
 