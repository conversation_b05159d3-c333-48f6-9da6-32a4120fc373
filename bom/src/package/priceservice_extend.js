/**
 * @Description: BOM (适配取价服务)
 * <AUTHOR>
 * @date 2022/1/22
 */

import PPM from 'plugin_public_methods'

export default class PriceServiceExtend {

    constructor(pluginService, pluginParam, parent) {
        this.parent = parent;
    }

    getAllFields(mdApiname) {
        return this.parent.getAllFields(mdApiname);
    }

    // 处理入参
    _parseFullProductList_after(plugin, opt = {}) {
        let data = this.addSomeFieldsToParam(opt.data, opt.metaData, opt.param.objApiName, opt.noChildrenPrice);
        data = this.resetChildrenAmount(opt.data);
    }

    // 添加一些bom字段给取价接口
    addSomeFieldsToParam(data = [], metaData = [], mdApiName, noChildrenPrice) {
        let {
            product_price, prod_pkg_key, bom_id, root_prod_pkg_key, parent_prod_pkg_key,
        } = this.getAllFields(mdApiName);
        return data.map((item, index) => {
            let d = metaData[index];
            let o = {
                'rootProdKey': d[root_prod_pkg_key] || '',
                'parentProdKey': d[parent_prod_pkg_key] || d.parent_rowId || '',
                'prodKey': d[prod_pkg_key] || d.rowId || '',
                'bomId': d[bom_id] || '',
            };
            if(!noChildrenPrice) o.price = d[product_price] || d.newestPrice || d.modified_adjust_price || d.adjust_price || '';
            return Object.assign(item, o)
        })
    }

    /** 走取价服务计算产品包价格时，子产品的数量需要除以产品包的数量；
     * @desc
     * @param data
     * @returns {Array}
     */
    resetChildrenAmount(data = []) {
        let rootData = [];
        let childrenData = [];
        data.forEach(item => {
            item.parentProdKey ? childrenData.push(item) : rootData.push(item);
        });
        rootData.forEach(rd => {
            if (rd.amount != '0') {
                childrenData.forEach(child => {
                    if (child.rootProdKey == rd.prodKey) {
                        child.amount = PPM.division(child.amount, rd.amount)
                    }
                })
            }
        });
        return data;
    }

    // 过滤配置价格和分组
    _filterConfigPrice(data, param){
        return data.filter(item => !item.isGroup && !this._isConfigPrice(item, param));
    }

    // 处理取价服务入参
    _parseFullProductList_before(plugin, param = {}) {
        let data = this._filterConfigPrice(param.data, param);
        this.addRootKey(data);
        data = PPM.parseTreeToNormal(data, true);
        return {data}
    }

    /**
     * @desc 取价计算之前，去掉bom子产品；
     * @param plugin
     * @param param
     * @returns {{modifyIndex: Array}}
     * @private
     */
    _getRealPriceAndCalculate_before(plugin, param = {}) {
        let {parent_prod_pkg_key} = this.getAllFields(param.objApiName);
        let mi = param.modifyIndex;
        let delIndex = [];
        param.data.forEach(item => {
            if (item[parent_prod_pkg_key]) delIndex.push(item.rowId);
        });
        PPM.deleteArrChildren(mi, delIndex);
        return {
            modifyIndex: mi,
        }
    }

    // 取价服务匹配数据逻辑
    _matchRealPrice(plugin, param) {
        let data = this.matchRealPriceData(param.curData, param.realPriceList, param.value, param);
        return {data}
    }

    /**
     * @desc 从取价服务返回的数据中，匹配对应的明细数据；兼容明细有重复产品
     * @param rowData
     * @param realPriceData
     * @param value
     * @returns {*}
     */
    matchRealPriceData(rowData = {}, realPriceData = [], value, param) {
        let f = {};
        let {prod_pkg_key, bom_id, product_id, price_book_id} = this.getAllFields(param.objApiName);
        if (rowData[prod_pkg_key]) {  // bom
            f = {'prod_pkg_key': rowData[prod_pkg_key]};
        }
        let res = PPM.findWhere(realPriceData, f);
        // 开了cpq，筛选条件需要加上bom_id，即使bom_id没有值；
        if (!res) {
            let last = {'product_id': rowData[product_id] || rowData._id};
            last.bom_id = rowData[bom_id] ? rowData[bom_id] : undefined;
            if (rowData[price_book_id]) {
                last.pricebook_id = rowData[price_book_id]
            }
            res = PPM.findWhere(realPriceData, last);
        }
        let guarantee = _.findWhere(realPriceData, {
            'product_id': rowData[product_id],
        });
        return res || guarantee || value;
    }

    // 是否是配置价格
    _isConfigPrice(data, param){
        let {price_mode, parent_prod_pkg_key} = this.getAllFields(param.objApiName);
        return (data[price_mode] == '1' || data[price_mode + '__v'] == '1') && data[parent_prod_pkg_key];
    }

    /**
     * @desc 取价服务回填数据，过滤掉分组
     * @param plugin
     * @param param
     * @returns {{data: *}}
     * @private
     */
    _getRealPriceAndCalculate_parseData(plugin, param) {
        let data = this._filterConfigPrice(param.data, param);
        return {data};
    }

    // 如果来自草稿箱，需要特殊处理BOM价格；
    // todo: 只处理价格有问题，如果开关是不走取价服务，对应的销售单价、小计就不正确了；下个版本775再优化；
    // parseDataFromDraft(mdData, from) {
    //     let _this = this;
    //     let draftData = this.model.get('oldDraftData');
    //     let {bom_id} = this.getAllFields();
    //     if (from === 'draft' && draftData && draftData.slave_draft_data) {
    //         PPM.each(mdData, function (arr, key) {
    //             let dfData = draftData.slave_draft_data[key];
    //             if (dfData) {
    //                 arr.forEach(arr, function (item) {
    //                     if (item.bom_id) {
    //                         let findData = CRM.util.findDataByBomId(dfData, item.bom_id, bom_id);
    //                         if (findData) item[_this.priceField] = findData[_this.priceField];
    //                     }
    //                 })
    //             }
    //         })
    //     }
    // }

    // 是否是配置价格
    getPriceModeIsConfig(data){
        let {price_mode} = this.getAllFields();
        return data[price_mode] == '1' || data[price_mode + '__v'] == '1';
    }


    /**
     * @desc 取价服务回填数据，子产品折扣置为0
     * @param plugin
     * @param obj
     * @returns {{data: *}}
     * @private
     */
    _matchRealPrice_after(plugin, obj) {
        let {data, param, value} = obj;
        let mdApiName = param.objApiName;
        let {parent_prod_pkg_key, discount, price_book_id, price_book_product_id, attribute_price_book_id, price_mode, product_price} = this.getAllFields(mdApiName);
        if (data[parent_prod_pkg_key]) {
            if(!this.parent.Add.noClearSalesPrice()){
                param.dataUpdater.updateDetail(mdApiName, data.rowId, {
                    [discount]: 0
                });
            }
            // 定价模式为配置价格，需要删掉子产品的价目表id，价目表产品id
            if (data[price_mode] == '1' || data[price_mode + '__v'] == '1') {
                let up = {};
                let fields = [price_book_id, price_book_product_id, attribute_price_book_id];
                fields.forEach(f => {
                    up[f] = up[f + '__r'] = null;
                });
                // 子件配置价格，重置掉价目表价格；
                up[product_price] = data._productPrice;
                param.dataUpdater.updateDetail(mdApiName, data.rowId, up);
            }else{
                let des = this.parent.getDescribe(mdApiName, param);
                let nodePriceDec = des[product_price] && des[product_price].decimal_places;
                let realPrice = CRM.util.formatDecimalPlace(PPM.multiplicational(value.selling_price , (value.discount / 100)), nodePriceDec);
                param.dataUpdater.updateDetail(mdApiName, data.rowId, {
                    [product_price]: realPrice
                });
            }
        }
    }

    // 为了取价服务接口计算产品包的差值，临时添加rootKey给接口用；
    addRootKey(data = []) {
        let allFields = this.getAllFields();
        function _fn(bomData, rootKey) {
            PPM.forEachTreeData(bomData, function (child, pData, groupPData) {
                if (!child.isGroup) {
                    // child.rootKey = rootKey;
                    if (child.parent_rowId) child.parent_prod_pkg_key = groupPData ? groupPData.rowId : pData.rowId;
                }
            })
        }

        data.forEach(function (item) {
            if (PPM.isBom(item, allFields).isPackage) {
                // if (item.rowId) item.rootKey = item.rowId;
                if (item.children) _fn([item], item.rowId)
            }
        });
    }


    // _mdRenderBefore(opt){
    //     // let data = opt.data;
    //     // if(data && data.length){
    //     //
    //     // }
    // }

    // 添加的数据，需要补充is_package
    _batchAddAfter_after(plugin, opt){
        let {is_package} = this.getAllFields();
        let {data, realPriceData} = opt;
        data.forEach((item, index) => {
            let rData = realPriceData[index];
            if(rData && rData.hasOwnProperty('is_package')){
                item[is_package] = rData.is_package;
                item[is_package + '__v'] = rData.is_package__v;
            }
        })
    }

    // 取价结果匹配时，过滤特殊数据
    _matchRealPriceData_before(plugin, param) {
        return {noMatch: param.data.isGroup};
    }

    // 取件结果，如果删除了包数据，那他下边的子件也需要删除；
    _formatRowDataByRealPrice_after(plugin, obj){
        let {delData, param} = obj;
        let mdApiName = param.objApiName;
        if(!mdApiName) return;
        let {parent_prod_pkg_key, prod_pkg_key} = this.getAllFields(mdApiName);
        let details = param.dataGetter.getDetail(mdApiName);

        function _getChildren(_id) {
            if(!PPM.hasValue(_id)) return;
            let res = [];
            function _fn(id){
                details.forEach(c => {
                    if(c[parent_prod_pkg_key] &&c[parent_prod_pkg_key] === id){
                        res.push(c);
                        _fn(c[prod_pkg_key])
                    }
                });
            }
            _fn(_id);
            return res;
        }

        if(delData && details?.length){
            delData.forEach(item => {
                if(!PPM.hasValue(item[prod_pkg_key])) return;
                let findChildren = _getChildren(item[prod_pkg_key]);
                if(findChildren?.length){
                    findChildren.forEach(c => {
                        param.dataUpdater.del(mdApiName, c.rowId);
                    })
                }
            })
        }
    }

    // 切换主对象价目表，bom数据需要重新计算
    async _checkMdIsMatchCustomer_after(plugin, opt) {
        let {mdApiName, param} = opt;
        let details = param.dataGetter.getDetail(mdApiName);
        if(!details.length) return;
        let allFields = this.getAllFields(mdApiName);
        let {price_book_id, product_price,} = allFields;
        let masterApiName = param.masterObjApiName;
        let masterData = param.dataGetter.getMasterData();
        let recordType = details[0].record_type;
        await this.parent.Add.calculateBomPrice({
            data: details,
            mdApiName,
            masterData,
            masterApiName,
            recordType,
            changeField: [price_book_id, product_price],
            switchMasterPriceBook: true
        }, param);
    }

    // 取价计算之后，需要单独计算一次所有子件的自定义字段；
    async _getRealPriceAndCalculate_end(plugin, opt) {
        let _this = this;
        let {mdApiName, data, param, res} = opt;
        let {product_price, price_book_price, price_book_discount, price_book_product_id, parent_prod_pkg_key} = this.getAllFields(mdApiName);
        let changeField = [product_price, price_book_price, price_book_discount, price_book_product_id];
        let allModify = [];
        this._calculatePrice = res.applicablePriceSystem || res.calculatePrice;
        data.forEach(item => {
            if(!item.isGroup && item[parent_prod_pkg_key]) allModify.push(item.rowId);
        });
        if(!allModify.length) return;
        await param.triggerCalAndUIEvent({
            triggerField: changeField,
            changeFields: changeField,
            operateType: 'mdEdit',
            dataIndex: allModify,
            objApiName: mdApiName,
            parseParam: function (obj) {
                return _this.parent.Add.filterCalFields(obj, mdApiName);
            }
        });

    }

    // 计算阶梯价前，过滤掉包和子数据，先不触发阶梯价；
    _getTieredPriceBefore(plugin, opt) {
        let { changeRowIds, param } = opt;
        let newRowIds = []
        let allFields = this.getAllFields();
        let mdApiName = param.objApiName;
        let detailList = param.dataGetter.getDetail(mdApiName);

        detailList.forEach(item => {
            let isBom = !!(item.parent_rowId || PPM.isBom(item, allFields).isPackage);
            let row = changeRowIds.includes(item.rowId);
            if (row && !isBom) {
                newRowIds.push(item.rowId)
            };
        })
        return {
            changeRowIds: newRowIds
        }
    }

    // 取件服务之后，如果需要计算接口重新计算价格，bom 还要再走一次 bom 计算；
    async _mdRenderAfter(plugin, param){
        if(this._calculatePrice){
            this._calculatePrice = false;
            let mdApiName = param.objApiName;
            let masterApiName = param.masterObjApiName;
            let masterData = param.dataGetter.getMasterData();
            let details = param.dataGetter.getDetail(mdApiName);
            let {parent_prod_pkg_key} = this.getAllFields(mdApiName);
            let changeRow = details.filter(c => c[parent_prod_pkg_key]);
            if(!details.length) return;
            await this.parent.Add.calculateBomPrice({
                data: details,
                mdApiName,
                masterApiName,
                masterData,
                changeRow,
                recordType: details[0].record_type,

            }, param);
        }
    }

    getHook() {
        return [
            {
                event: 'price-service.parseFullProductList.before',
                functional: this._parseFullProductList_before.bind(this)
            }, {
                event: 'price-service.parseFullProductList.after',
                functional: this._parseFullProductList_after.bind(this)
            }, {
                event: 'price-service.getRealPriceAndCalculate.before',
                functional: this._getRealPriceAndCalculate_before.bind(this)
            }, {
                event: 'price-service.matchRealPriceData.after',
                functional: this._matchRealPrice.bind(this)
            }, {
                event: 'price-service.getRealPriceAndCalculate.parseData',
                functional: this._getRealPriceAndCalculate_parseData.bind(this)
            }, {
                event: 'price-service.matchRealPrice.after',
                functional: this._matchRealPrice_after.bind(this)
            }, {
                event: 'price-service.batchAddAfter.after',
                functional: this._batchAddAfter_after.bind(this)
            },{
                event: 'price-service.matchRealPriceData.before',
                functional: this._matchRealPriceData_before.bind(this)
            },
            {
                event: 'price-service.formatRowDataByRealPrice.after',
                functional: this._formatRowDataByRealPrice_after.bind(this)
            },
            {
                event: 'price-service.checkMdIsMatchCustomer.after',
                functional: this._checkMdIsMatchCustomer_after.bind(this)
            },{
                event: 'price-service.getRealPriceAndCalculate.end',
                functional: this._getRealPriceAndCalculate_end.bind(this)
            }, {
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            },
            {
                event: 'price-service.getTieredPrice.before',
                functional: this._getTieredPriceBefore.bind(this)
            },
        ];
    }

}

